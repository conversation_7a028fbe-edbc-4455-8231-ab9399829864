{"cells": [{"cell_type": "code", "execution_count": 1, "id": "959bf227", "metadata": {}, "outputs": [], "source": ["from IntegrationUtilities import PrescintoIntegrationUtilities\n", "m = PrescintoIntegrationUtilities(server = 'IN',token ='884c5f4d-223b-4191-9400-3afd6ee9b538')"]}, {"cell_type": "code", "execution_count": 51, "id": "9ca890d4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON> set a DataFrame with multiple columns to the single column Mapping\n"]}, {"ename": "ValueError", "evalue": "DataFrame constructor not properly called!", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "\u001b[1;32m~\\AppData\\Local\\Temp\\ipykernel_16112\\1908803833.py\u001b[0m in \u001b[0;36m?\u001b[1;34m()\u001b[0m\n\u001b[0;32m     45\u001b[0m     \u001b[1;31m# Step 6: Save final DataFrame to CSV\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     46\u001b[0m     \u001b[0mdf\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mto_csv\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0moutput_file\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mindex\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;32mFalse\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     47\u001b[0m     \u001b[0mprint\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33mf\"\u001b[0m\u001b[1;33mProcessed and saved data with total_generation for \u001b[0m\u001b[1;33m{\u001b[0m\u001b[0mplant_name\u001b[0m\u001b[1;33m}\u001b[0m\u001b[1;33m at \u001b[0m\u001b[1;33m{\u001b[0m\u001b[0moutput_file\u001b[0m\u001b[1;33m}\u001b[0m\u001b[1;33m.\u001b[0m\u001b[1;33m\"\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     48\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 49\u001b[1;33m \u001b[0mprocess_plant_data\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m'IN.INTE.PLAS'\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;34m\"RC Plasto\"\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;34m'2024-11-28'\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;34m'2025-03-31'\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[1;32m~\\AppData\\Local\\Temp\\ipykernel_16112\\1908803833.py\u001b[0m in \u001b[0;36m?\u001b[1;34m(plant_name, plant_long_name, start_date, end_date)\u001b[0m\n\u001b[0;32m     21\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     22\u001b[0m     \u001b[0mprint\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mdgr\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     23\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     24\u001b[0m     \u001b[1;31m# Step 2: Create DataFrame\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 25\u001b[1;33m     \u001b[0mdf\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mpd\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mDataFrame\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mdgr\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     26\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     27\u001b[0m     \u001b[1;31m# Step 3: Add 'Plant Short Name' and 'Plant Long Name'\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     28\u001b[0m     \u001b[0mdf\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;34m'Plant Short Name'\u001b[0m\u001b[1;33m]\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mplant_name\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\frame.py\u001b[0m in \u001b[0;36m?\u001b[1;34m(self, data, index, columns, dtype, copy)\u001b[0m\n\u001b[0;32m    882\u001b[0m                 \u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    883\u001b[0m         \u001b[1;31m# For data is scalar\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    884\u001b[0m         \u001b[1;32melse\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    885\u001b[0m             \u001b[1;32mif\u001b[0m \u001b[0mindex\u001b[0m \u001b[1;32mis\u001b[0m \u001b[1;32mNone\u001b[0m \u001b[1;32mor\u001b[0m \u001b[0mcolumns\u001b[0m \u001b[1;32mis\u001b[0m \u001b[1;32mNone\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 886\u001b[1;33m                 \u001b[1;32mraise\u001b[0m \u001b[0mValueError\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m\"DataFrame constructor not properly called!\"\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    887\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    888\u001b[0m             \u001b[0mindex\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mensure_index\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mindex\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    889\u001b[0m             \u001b[0mcolumns\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mensure_index\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mcolumns\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31mValueError\u001b[0m: DataFrame constructor not properly called!"]}], "source": ["import pandas as pd\n", "\n", "def process_plant_data(plant_name, plant_long_name, start_date, end_date):\n", "    output_file = f'CSV_Wind/{plant_name}.csv'\n", "    \n", "    # Step 1: Fetch Data\n", "    category = ['Turbine']\n", "    params = ['WTUR.Generation today']\n", "    condition_generation = {\"Generation today\": \"last\"}\n", "    \n", "    dgr = m.fetchDataV2(\n", "        plant_name,\n", "        category,\n", "        params,\n", "        None,\n", "        start_date,\n", "        end_date,\n", "        granularity='60m',\n", "        condition=condition_generation\n", "    )\n", "\n", "    print(dgr)\n", "\n", "    # Step 2: Create DataFrame\n", "    df = pd.DataFrame(dgr)\n", "\n", "    # Step 3: Add 'Plant Short Name' and 'Plant Long Name'\n", "    df['Plant Short Name'] = plant_name\n", "    df['Plant Long Name'] = plant_long_name\n", "    \n", "    # Step 4: Parse and format 'time' column\n", "    if 'time' in df.columns:\n", "        df['date_parsed'] = pd.to_datetime(df['time'], utc=True)\n", "        df['DateValue'] = df['date_parsed'].dt.strftime('%B %d, %Y')\n", "        df.drop(columns=['date_parsed'], inplace=True)\n", "\n", "    # Step 5: Calculate total_generation directly\n", "    if 'time' in df.columns:\n", "        generation_cols = df.columns.difference(['time', 'Plant Short Name', 'Plant Long Name', 'DateValue'])\n", "        df['total_generation'] = df[generation_cols].sum(axis=1, skipna=True)\n", "\n", "        # Now keep only the necessary columns\n", "        df = df[['time', 'Plant Short Name', 'Plant Long Name', 'DateValue', 'total_generation']]\n", "    \n", "    # Step 6: Save final DataFrame to CSV\n", "    df.to_csv(output_file, index=False)\n", "    print(f\"Processed and saved data with total_generation for {plant_name} at {output_file}.\")\n", "\n", "process_plant_data('IN.INTE.PLAS', \"RC Plasto\", '2024-11-28', '2025-03-31')\n"]}, {"cell_type": "code", "execution_count": 27, "id": "38566c8d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\<PERSON><PERSON><PERSON><PERSON><PERSON>\\Wind_solar\\DATA MISSMATCH\\IntegrationUtilities.py:308: FutureWarning: Passing literal json to 'read_json' is deprecated and will be removed in a future version. To read from a literal string, wrap it in a 'StringIO' object.\n", "  dataResp = pd.read_json(resp, orient=\"split\")\n"]}], "source": ["plantName = 'IN.INTE.KSIP'\n", "startDate = '2025-01-05'\n", "endDate = '2025-03-31'\n", "\n", "category = ['Turbine']\n", "params = ['WTUR.Generation today']\n", "condition_generation = {\"Generation today\": \"last\"}\n", "\n", "dgr = m.fetchDataV2(\n", "plantName,\n", "category,\n", "params,\n", "None,\n", "startDate,\n", "endDate,\n", "granularity='60m',\n", "condition=None\n", ")"]}, {"cell_type": "code", "execution_count": 28, "id": "4f948406", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>time</th>\n", "      <th>BBV19.Generation today</th>\n", "      <th>BBV20.Generation today</th>\n", "      <th>BBV24.Generation today</th>\n", "      <th>BBV26.Generation today</th>\n", "      <th>BBV44.Generation today</th>\n", "      <th>BBV47.Generation today</th>\n", "      <th>BBV56.Generation today</th>\n", "      <th>BBV57.Generation today</th>\n", "      <th>BBV58.Generation today</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-01-05T00:00:00Z+05:30</td>\n", "      <td>23.385691</td>\n", "      <td>24.587199</td>\n", "      <td>19.621783</td>\n", "      <td>-1.818846</td>\n", "      <td>15.463715</td>\n", "      <td>21.788803</td>\n", "      <td>-2.767752</td>\n", "      <td>20.936667</td>\n", "      <td>17.458730</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-01-05T01:00:00Z+05:30</td>\n", "      <td>233.006543</td>\n", "      <td>260.398287</td>\n", "      <td>57.817456</td>\n", "      <td>180.619616</td>\n", "      <td>256.290405</td>\n", "      <td>281.088197</td>\n", "      <td>229.133597</td>\n", "      <td>272.916734</td>\n", "      <td>280.884227</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-01-05T02:00:00Z+05:30</td>\n", "      <td>469.565992</td>\n", "      <td>NaN</td>\n", "      <td>161.658035</td>\n", "      <td>541.233945</td>\n", "      <td>654.032847</td>\n", "      <td>883.613557</td>\n", "      <td>705.826379</td>\n", "      <td>545.414720</td>\n", "      <td>642.659109</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-01-05T03:00:00Z+05:30</td>\n", "      <td>1034.742577</td>\n", "      <td>NaN</td>\n", "      <td>468.584020</td>\n", "      <td>933.792307</td>\n", "      <td>1080.019572</td>\n", "      <td>1599.923361</td>\n", "      <td>1043.870823</td>\n", "      <td>757.461310</td>\n", "      <td>954.801687</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-01-05T04:00:00Z+05:30</td>\n", "      <td>1690.149874</td>\n", "      <td>260.398287</td>\n", "      <td>806.507929</td>\n", "      <td>1388.985809</td>\n", "      <td>1301.524986</td>\n", "      <td>1957.123521</td>\n", "      <td>1255.196741</td>\n", "      <td>991.413886</td>\n", "      <td>1239.852069</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2059</th>\n", "      <td>2025-03-31T19:00:00Z+05:30</td>\n", "      <td>833.903102</td>\n", "      <td>2030.496123</td>\n", "      <td>726.395642</td>\n", "      <td>936.492633</td>\n", "      <td>915.911029</td>\n", "      <td>1226.921355</td>\n", "      <td>1856.564698</td>\n", "      <td>2486.468106</td>\n", "      <td>1839.959805</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2060</th>\n", "      <td>2025-03-31T20:00:00Z+05:30</td>\n", "      <td>1030.953710</td>\n", "      <td>2208.326022</td>\n", "      <td>841.706992</td>\n", "      <td>1084.456256</td>\n", "      <td>1371.397499</td>\n", "      <td>1455.103943</td>\n", "      <td>2075.273339</td>\n", "      <td>2720.007938</td>\n", "      <td>2111.281451</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2061</th>\n", "      <td>2025-03-31T21:00:00Z+05:30</td>\n", "      <td>1230.288976</td>\n", "      <td>2676.913699</td>\n", "      <td>1310.779120</td>\n", "      <td>1316.489513</td>\n", "      <td>2021.702278</td>\n", "      <td>1936.704743</td>\n", "      <td>2523.966042</td>\n", "      <td>3238.631076</td>\n", "      <td>2663.292172</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2062</th>\n", "      <td>2025-03-31T22:00:00Z+05:30</td>\n", "      <td>1425.794894</td>\n", "      <td>3200.395297</td>\n", "      <td>1524.917567</td>\n", "      <td>1798.088115</td>\n", "      <td>2522.501630</td>\n", "      <td>2458.553645</td>\n", "      <td>3054.256335</td>\n", "      <td>3793.326618</td>\n", "      <td>3321.830014</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2063</th>\n", "      <td>2025-03-31T23:00:00Z+05:30</td>\n", "      <td>1627.606962</td>\n", "      <td>3935.671145</td>\n", "      <td>1924.980992</td>\n", "      <td>2810.985922</td>\n", "      <td>3370.284711</td>\n", "      <td>3181.930436</td>\n", "      <td>4017.564522</td>\n", "      <td>4433.746913</td>\n", "      <td>4401.364611</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2064 rows × 10 columns</p>\n", "</div>"], "text/plain": ["                            time  BBV19.Generation today  \\\n", "0     2025-01-05T00:00:00Z+05:30               23.385691   \n", "1     2025-01-05T01:00:00Z+05:30              233.006543   \n", "2     2025-01-05T02:00:00Z+05:30              469.565992   \n", "3     2025-01-05T03:00:00Z+05:30             1034.742577   \n", "4     2025-01-05T04:00:00Z+05:30             1690.149874   \n", "...                          ...                     ...   \n", "2059  2025-03-31T19:00:00Z+05:30              833.903102   \n", "2060  2025-03-31T20:00:00Z+05:30             1030.953710   \n", "2061  2025-03-31T21:00:00Z+05:30             1230.288976   \n", "2062  2025-03-31T22:00:00Z+05:30             1425.794894   \n", "2063  2025-03-31T23:00:00Z+05:30             1627.606962   \n", "\n", "      BBV20.Generation today  BBV24.Generation today  BBV26.Generation today  \\\n", "0                  24.587199               19.621783               -1.818846   \n", "1                 260.398287               57.817456              180.619616   \n", "2                        NaN              161.658035              541.233945   \n", "3                        NaN              468.584020              933.792307   \n", "4                 260.398287              806.507929             1388.985809   \n", "...                      ...                     ...                     ...   \n", "2059             2030.496123              726.395642              936.492633   \n", "2060             2208.326022              841.706992             1084.456256   \n", "2061             2676.913699             1310.779120             1316.489513   \n", "2062             3200.395297             1524.917567             1798.088115   \n", "2063             3935.671145             1924.980992             2810.985922   \n", "\n", "      BBV44.Generation today  BBV47.Generation today  BBV56.Generation today  \\\n", "0                  15.463715               21.788803               -2.767752   \n", "1                 256.290405              281.088197              229.133597   \n", "2                 654.032847              883.613557              705.826379   \n", "3                1080.019572             1599.923361             1043.870823   \n", "4                1301.524986             1957.123521             1255.196741   \n", "...                      ...                     ...                     ...   \n", "2059              915.911029             1226.921355             1856.564698   \n", "2060             1371.397499             1455.103943             2075.273339   \n", "2061             2021.702278             1936.704743             2523.966042   \n", "2062             2522.501630             2458.553645             3054.256335   \n", "2063             3370.284711             3181.930436             4017.564522   \n", "\n", "      BBV57.Generation today  BBV58.Generation today  \n", "0                  20.936667               17.458730  \n", "1                 272.916734              280.884227  \n", "2                 545.414720              642.659109  \n", "3                 757.461310              954.801687  \n", "4                 991.413886             1239.852069  \n", "...                      ...                     ...  \n", "2059             2486.468106             1839.959805  \n", "2060             2720.007938             2111.281451  \n", "2061             3238.631076             2663.292172  \n", "2062             3793.326618             3321.830014  \n", "2063             4433.746913             4401.364611  \n", "\n", "[2064 rows x 10 columns]"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["dgr"]}, {"cell_type": "code", "execution_count": 29, "id": "d63b6406", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                          time  total_generation\n", "0    2025-01-05 00:00:00+05:30        138.655990\n", "1    2025-01-05 01:00:00+05:30       2052.155063\n", "2    2025-01-05 02:00:00+05:30       4604.004583\n", "3    2025-01-05 03:00:00+05:30       7873.195656\n", "4    2025-01-05 04:00:00+05:30      10891.153103\n", "...                        ...               ...\n", "2059 2025-03-31 19:00:00+05:30      12853.112493\n", "2060 2025-03-31 20:00:00+05:30      14898.507149\n", "2061 2025-03-31 21:00:00+05:30      18918.767619\n", "2062 2025-03-31 22:00:00+05:30      23099.664115\n", "2063 2025-03-31 23:00:00+05:30      29704.136214\n", "\n", "[2064 rows x 2 columns]\n"]}], "source": ["import pandas as pd\n", "\n", "# Your dataframe\n", "df = dgr.copy()  # Or however you load your data\n", "\n", "# Ensure 'time' is parsed as datetime (optional but good practice)\n", "df['time'] = pd.to_datetime(df['time'])\n", "\n", "# Select only the BBV columns (everything except 'time')\n", "generation_cols = df.columns.drop('time')\n", "\n", "# Sum across the BBV columns row-wise, skipping NaNs\n", "df['total_generation'] = df[generation_cols].sum(axis=1)\n", "\n", "# Result\n", "print(df[['time', 'total_generation']])\n"]}, {"cell_type": "code", "execution_count": 12, "id": "b1cfe15f", "metadata": {}, "outputs": [], "source": ["categories = None\n", "deviceDict = None\n", "parameterDict = None\n", "try:\n", "    categories, deviceDict, parameterDict,componentTagsDict = m.getWindPlantInfo(plantName)\n", "except Exception as e:\n", "    print(e)"]}, {"cell_type": "code", "execution_count": 19, "id": "75be76fe", "metadata": {}, "outputs": [{"data": {"text/plain": ["['Plant', 'Turbine']"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["categories"]}, {"cell_type": "code", "execution_count": 13, "id": "3de7d066", "metadata": {}, "outputs": [{"data": {"text/plain": ["['Avg <PERSON> of the farm',\n", " 'Generation today',\n", " 'PLF',\n", " 'Total Active Power',\n", " 'Total Generation',\n", " 'Total Reactive Power',\n", " 'Wind Direction']"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["componentTagsDict['Plant']"]}, {"cell_type": "code", "execution_count": 52, "id": "026761d8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📥 Processing: IN.INTE.ANSP.csv\n", "✅ Inserted 14088 records from IN.INTE.ANSP.csv\n", "📥 Processing: IN.INTE.APIP.csv\n", "✅ Inserted 25248 records from IN.INTE.APIP.csv\n", "📥 Processing: IN.INTE.BMPL.csv\n", "✅ Inserted 24312 records from IN.INTE.BMPL.csv\n", "📥 Processing: IN.INTE.CILW.csv\n", "✅ Inserted 24168 records from IN.INTE.CILW.csv\n", "📥 Processing: IN.INTE.KPLW.csv\n", "✅ Inserted 27504 records from IN.INTE.KPLW.csv\n", "📥 Processing: IN.INTE.KSIP.csv\n", "✅ Inserted 27504 records from IN.INTE.KSIP.csv\n", "📥 Processing: IN.INTE.MPLW.csv\n", "✅ Inserted 16320 records from IN.INTE.MPLW.csv\n", "📥 Processing: IN.INTE.NDLW.csv\n", "✅ Inserted 14256 records from IN.INTE.NDLW.csv\n", "📥 Processing: IN.INTE.NIPL.csv\n", "✅ Inserted 22128 records from IN.INTE.NIPL.csv\n", "📥 Processing: IN.INTE.QEPL.csv\n", "✅ Inserted 22344 records from IN.INTE.QEPL.csv\n", "📥 Processing: IN.INTE.SABE.csv\n", "✅ Inserted 12048 records from IN.INTE.SABE.csv\n", "📥 Processing: IN.INTE.SPFL.csv\n", "✅ Inserted 13920 records from IN.INTE.SPFL.csv\n", "📥 Processing: IN.INTE.SSPL.csv\n", "✅ Inserted 24192 records from IN.INTE.SSPL.csv\n", "🎉 All Wind CSV files processed and inserted successfully.\n"]}], "source": ["import os\n", "import pandas as pd\n", "from sqlalchemy import create_engine, Column, String, DateTime, Float, Integer\n", "from sqlalchemy.orm import sessionmaker, declarative_base\n", "\n", "# -------------------- Configuration --------------------\n", "DATABASE_URL = 'mysql+pymysql://root:test123@localhost/data_missmatch'\n", "CSV_FOLDER_PATH = 'CSV_wind'  # Folder where Wind CSVs are located\n", "\n", "# -------------------- SQLAlchemy Setup --------------------\n", "Base = declarative_base()\n", "\n", "class WindDGR(Base):\n", "    __tablename__ = 'data_wind'  # Corrected for wind\n", "    id = Column(Integer, primary_key=True, autoincrement=True)\n", "    time = Column(DateTime)\n", "    daily_generation = Column(Float, nullable=True)  # Changed from daily_energy to daily_generation\n", "    plant_long_name = Column(String(255))\n", "    date_value = Column(String(50))\n", "    plant_short_name = Column(String(100))\n", "\n", "# -------------------- Create Table --------------------\n", "engine = create_engine(DATABASE_URL)\n", "Base.metadata.create_all(engine)\n", "Session = sessionmaker(bind=engine)\n", "session = Session()\n", "\n", "# -------------------- Process CSV Files --------------------\n", "for file_name in os.listdir(CSV_FOLDER_PATH):\n", "    if file_name.endswith('.csv'):\n", "        file_path = os.path.join(CSV_FOLDER_PATH, file_name)\n", "        print(f\"📥 Processing: {file_name}\")\n", "        \n", "        df = pd.read_csv(file_path, encoding='utf-8')\n", "\n", "        # Convert 'time' to datetime (UTC)\n", "        df['time'] = pd.to_datetime(df['time'], utc=True, errors='coerce')\n", "\n", "        # Format date into 'Month Day, Year'\n", "        df['DateValue'] = df['time'].dt.strftime('%B %d, %Y')\n", "\n", "        # Replace blank strings with None\n", "        df.replace({r'^\\s*$': None}, regex=True, inplace=True)\n", "\n", "        # Fill NaN values for 'Daily Generation' column\n", "        df['Daily Generation'] = df['total_generation'].fillna(0)\n", "\n", "        # Map rows to WindDGR model\n", "        records = [\n", "            WindDGR(\n", "                time=row['time'],\n", "                daily_generation=row['Daily Generation'],\n", "                plant_long_name=row['Plant Long Name'],\n", "                date_value=row['DateValue'],\n", "                plant_short_name=row['Plant Short Name']\n", "            )\n", "            for _, row in df.iterrows()\n", "        ]\n", "\n", "        # Insert in bulk\n", "        session.bulk_save_objects(records)\n", "        session.commit()\n", "        print(f\"✅ Inserted {len(records)} records from {file_name}\")\n", "\n", "# Close DB session\n", "session.close()\n", "print(\"🎉 All Wind CSV files processed and inserted successfully.\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "50ee008e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}