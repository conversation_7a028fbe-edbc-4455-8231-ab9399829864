from sqlalchemy import Column, Integer, String, Float, <PERSON><PERSON><PERSON>, Date, Enum
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class WindReport(Base):
    __tablename__ = 'dgr_wind_db'

    id = Column(Integer, primary_key=True)
    date = Column(Date, nullable=False)
    plant_short_name = Column(String, nullable=False)
    plant_long_name = Column(String, nullable=False)
    generation = Column(Float, nullable=False)
    wind_speed = Column(Float, nullable=False)
    approved = Column(Boolean, default=False)
    review = Column(Boolean, default=False)
    action_performed = Column(Boolean, default=False)
    status = Column(Enum('Sent', 'Pending', 'In Review', 'Regenerated', 'Not Sent', name='status_enum'), default='Pending')
    regenerate = Column(<PERSON>olean, default=False)
    dgr_path = Column(String(500))
    comments = Column(String)
    dont_send = Column(<PERSON>olean, default=False)

    def to_dict(self):
        return {
            'id': self.id,
            'date': self.date.isoformat(),
            'plant_short_name': self.plant_short_name,
            'plant_long_name': self.plant_long_name,
            'generation': self.generation,
            'wind_speed': self.wind_speed,
            'approved': self.approved,
            'review': self.review,
            'action_performed': self.action_performed,
            'status': self.status,
            'regenerate': self.regenerate,
            'dgr_path': self.dgr_path,
            'comments': self.comments,
            'dont_send': self.dont_send
        }


class SolarReport(Base):
    __tablename__ = 'dgr_solar_db'

    id = Column(Integer, primary_key=True)
    date = Column(Date, nullable=False)
    plant_short_name = Column(String, nullable=False)
    plant_long_name = Column(String, nullable=False)
    generation = Column(Float, nullable=False)
    pr = Column(Float, nullable=False)
    poa = Column(Float, nullable=False)
    approved = Column(Boolean, default=False)
    review = Column(Boolean, default=False)
    action_performed = Column(Boolean, default=False)
    status = Column(Enum('Sent', 'Pending', 'In Review', 'Regenerated', 'Not Sent', name='status_enum'), default='Pending')
    regenerate = Column(Boolean, default=False)
    dgr_path = Column(String(500))
    comments = Column(String)
    dont_send = Column(Boolean, default=False)

    def to_dict(self):
        return {
            'id': self.id,
            'date': self.date.isoformat(),
            'plant_short_name': self.plant_short_name,
            'plant_long_name': self.plant_long_name,
            'generation': self.generation,
            'pr': self.pr,
            'poa': self.poa,
            'approved': self.approved,
            'review': self.review,
            'action_performed': self.action_performed,
            'status': self.status,
            'regenerate': self.regenerate,
            'dgr_path': self.dgr_path,
            'comments': self.comments,
            'dont_send': self.dont_send
        }

class DgrBothDb(Base):
    __tablename__ = 'dgr_both_db'

    id = Column(Integer, primary_key=True)
    date = Column(Date, nullable=False)
    plant_short_name_solar = Column(String, nullable=False)
    plant_long_name_solar = Column(String, nullable=False)
    generation_solar = Column(Float, nullable=False)
    pr = Column(Float, nullable=False)
    poa = Column(Float, nullable=False)
    plant_short_name_wind = Column(String, nullable=False)
    plant_long_name_wind = Column(String, nullable=False)
    generation_wind = Column(Float, nullable=False)
    wind_speed = Column(Float, nullable=False)
    approved = Column(Boolean, default=False)
    review = Column(Boolean, default=False)
    action_performed = Column(Boolean, default=False)
    status = Column(Enum('Sent', 'Pending', 'In Review', 'Regenerated', 'Not Sent', name='status_enum'), default='Pending')
    regenerate = Column(Boolean, default=False)
    dgr_path = Column(String(500))
    comments = Column(String)
    dont_send = Column(Boolean, default=False)

    def to_dict(self):
        return {
            'id': self.id,
            'date': self.date.isoformat(),
            'plant_short_name_solar': self.plant_short_name_solar,
            'plant_long_name_solar': self.plant_long_name_solar,
            'generation_solar': self.generation_solar,
            'pr': self.pr,
            'poa': self.poa,
            'plant_short_name_wind': self.plant_short_name_wind,
            'plant_long_name_wind': self.plant_long_name_wind,
            'generation_wind': self.generation_wind,
            'wind_speed': self.wind_speed,
            'approved': self.approved,
            'review': self.review,
            'action_performed': self.action_performed,
            'status': self.status,
            'regenerate': self.regenerate,
            'dgr_path': self.dgr_path,
            'comments': self.comments,
            'dont_send': self.dont_send
        }



from sqlalchemy import Column, Integer, String, Date

class WhatsAppMessage(Base):
    __tablename__ = 'whatsapp_messages'

    id = Column(Integer, primary_key=True, autoincrement=True)
    wa_id = Column(String(20), nullable=False)
    message_id = Column(String(100), nullable=False)
    report_type = Column(String(50), nullable=False)
    plant_short_name = Column(String(100), nullable=False)
    plant_long_name = Column(String(100), nullable=False)
    date = Column(Date, nullable=False)
    dgr_path = Column(String(500))  # <-- New column

    def to_dict(self):
        return {
            'id': self.id,
            'wa_id': self.wa_id,
            'message_id': self.message_id,
            'report_type': self.report_type,
            'plant_short_name': self.plant_short_name,
            'plant_long_name': self.plant_long_name,
            'date': self.date,
            'dgr_path': self.dgr_path
        }
