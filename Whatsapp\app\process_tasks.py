
from src.solar_automation import generate_solar_automation_report
from src.wind_automation import generate_wind_automation_report
from src.both_plants_automation import combined_both_plants
from helper.logger_setup import setup_logger
from whatsapp.sender_whatsapp import *
from DB.db_ops import get_solar_report_data, get_wind_report_data, get_combine_report_data
from helper.utils import get_contact_number_from_csv

logging = setup_logger('process_tasks', 'process_tasks.log')

###################################################################################################################
def send_whatsapp_report_solar(plant: str, customer: str, yesterday: str) -> None:
    """
    Sends the solar DGR report via WhatsApp to a list of recipients.

    Args:
        plant (str): Plant short name.
        customer (str): Customer name.
        yesterday (str): Date string for the report.

    Returns:
        None
    """
    try:
        report_data = get_solar_report_data(plant, yesterday)
        poa = report_data["poa"]
        dgr_path = report_data["dgr_path"]
        pr = report_data["pr"]
        generation = report_data["generation"]
        numbers = get_contact_number_from_csv(plant)
        # numbers = [contact_number] if contact_number and contact_number != "N/A" else []
        # print("numbers", numbers)
        # pr("Type", type(numbers))
        if not numbers:
            logging.warning(f"No WhatsApp contact numbers found for plant: {plant}")

        for number in numbers:
            try:
                print("number", number)
                send_solar_dgr_template(
                    to_number="91" + number,
                    customer_name=customer,
                    date=yesterday,
                    today_gen=generation,
                    today_pr=pr,
                    today_poa=poa,
                    plant_short_name=plant,
                    dgr_path=dgr_path
                )
            except Exception as e:
                logging.error(f"Failed to send report to {number}: {e}", exc_info=True)
    except Exception as e:
        logging.error(f"Error during WhatsApp report sending: {e}", exc_info=True)


def process_plant_solar(plant: str, customer: str, yesterday: str, report_type: str) -> None:
    """
    Processes and generates a solar report for a given plant and customer.

    Args:
        plant (str): Plant short name.
        customer (str): Customer name.
        yesterday (str): Date string for the report.
        report_type (str): Type of report to generate.

    Returns:
        None
    """
    try:
        logging.info(f"Processing report for {plant} - {customer}")
        report_filename = generate_solar_automation_report(plant, yesterday, customer, report_type)
        logging.info(f"Generated report: {report_filename}")
    except Exception as e:
        logging.error(f"Error processing report for {plant}: {e}", exc_info=True)

#################################################################################################################


def send_whatsapp_report_wind(plant: str, customer: str, yesterday: str) -> None:
    """
    Sends the wind DGR report via WhatsApp to a list of recipients.

    Args:
        plant (str): Plant short name.
        customer (str): Customer name.
        yesterday (str): Date string for the report.

    Returns:
        None
    """
    try:
        report_data = get_wind_report_data(plant, yesterday)
        wind_speed = report_data["wind_speed"]
        dgr_path = report_data["dgr_path"]
        generation = report_data["generation"]
        numbers = get_contact_number_from_csv(plant)
        # numbers = [contact_number] if contact_number and contact_number != "N/A" else []
        print("numbers", numbers)
        print("Type", type(numbers))
        if not numbers:
            logging.warning(f"No WhatsApp contact numbers found for plant: {plant}")

        for number in numbers:
            try:
                send_wind_dgr_template(
                    to_number="91" + number,
                    customer_name=customer,
                    today_gen=generation,
                    today_ws=wind_speed,
                    report_date=yesterday,
                    plant_short_name=plant,
                    dgr_path=dgr_path
                )
            except Exception as e:
                logging.error(f"Failed to send report to {number}: {e}", exc_info=True)
    except Exception as e:
        logging.error(f"Error during WhatsApp report sending: {e}", exc_info=True)


def process_plant_wind(plant: str, customer: str, yesterday: str, report_type: str) -> None:
    """
    Processes and generates a wind report for a given plant and customer.

    Args:
        plant (str): Plant short name.
        customer (str): Customer name.
        yesterday (str): Date string for the report.
        report_type (str): Type of report to generate.

    Returns:
        None
    """
    try:
        logging.info(f"Processing report for {plant} - {customer}")
        report_filename = generate_wind_automation_report(plant, yesterday, customer, report_type, "0")
        logging.info(f"Generated report: {report_filename}")
    except Exception as e:
        logging.error(f"Error processing report for {plant}: {e}", exc_info=True)

###################################################################################################################



###################################################################################################################
def process_plant_both(
    plant_solar: str,
    plant_wind: str,
    customer: str,
    yesterday: str,
    report_type: str
) -> None:
    """
    Processes and generates a combined solar and wind report for given plants and customer.

    Args:
        plant_solar (str): Solar plant short name.
        plant_wind (str): Wind plant short name.
        customer (str): Customer name.
        yesterday (str): Date string for the report.
        report_type (str): Type of report to generate.

    Returns:
        None
    """
    try:
        logging.info(f"Processing report for {plant_solar} & {plant_wind} - {customer}")
        report_filename = combined_both_plants(plant_solar, plant_wind, yesterday, customer, report_type, "0")
        logging.info(f"Generated report: {report_filename}")
    except Exception as e:
        logging.error(f"Error processing report for {plant_solar} & {plant_wind}: {e}", exc_info=True)


def send_whatsapp_report_both(
    plant_solar: str,
    plant_wind: str,
    customer: str,
    yesterday: str
) -> None:
    """
    Sends the combined solar and wind DGR report via WhatsApp to a list of recipients.

    Args:
        plant_solar (str): Solar plant short name.
        plant_wind (str): Wind plant short name.
        customer (str): Customer name.
        yesterday (str): Date string for the report.

    Returns:
        None
    """
    try:
        report_data = get_combine_report_data(plant_wind, yesterday)
        wind_speed = report_data["wind_speed"]
        dgr_path = report_data["dgr_path"]
        generation_solar = report_data["generation_solar"]
        pr = report_data["pr"]
        poa = report_data["poa"]
        generation_wind = report_data["generation_wind"]
        total_calculated = round(float(generation_solar) + float(generation_wind), 2)
        numbers = get_contact_number_from_csv(plant_wind)
        if not numbers or numbers == ["N/A"]:
            logging.warning(f"No WhatsApp contact numbers found for plant: {plant_wind}")
        else:
            for number_item in numbers:
                if number_item == "N/A":
                    logging.warning(f"Skipping 'N/A' contact for plant: {plant_wind}")
                    continue
                try:
                    send_both_dgr_template(
                        to_number="91" + number_item,
                        customer_name=customer,
                        wind_today_gen=generation_wind,
                        wind_today_ws=wind_speed,
                        report_date=yesterday,
                        solar_today_gen=generation_solar,
                        solar_today_pr=pr,
                        solar_today_poa=poa,
                        dgr_path=dgr_path,
                        plant_short_name_wind=plant_wind,
                        total_calculated=total_calculated
                    )
                except Exception as e:
                    logging.error(f"Failed to send report to {number_item}: {e}", exc_info=True)
    except Exception as e:
        logging.error(f"Error during WhatsApp report sending for both plants: {e}", exc_info=True)


###################################################################################################################
