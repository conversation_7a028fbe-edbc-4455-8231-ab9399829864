import pandas as pd
import csv
import schedule
import time
from datetime import datetime, timedelta
from IntegrationUtilities import PrescintoIntegrationUtilities
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from logger_setup import setup_logger
from PyPDF2 import PdfMerger
# Configure logging

logging = setup_logger('test_automate', 'test_automate.log')

# Initialize integration utility
def initialize_integration(server: str, token: str):
    return PrescintoIntegrationUtilities(server=server, token=token)



def generate_dgr_report(
    wind_speed_data: pd.DataFrame,
    daily_generation: pd.DataFrame,
    start_date: str,
    customer_name: str,
    project: str,
    ma_percent: str
) -> dict:
    """
    Generate a CSV report combining wind speed and daily generation data,
    grouped by 'Loc No' with mean wind speed and total daily generation.
    """
    output_file = f"Test_DGR_Report_{start_date}.csv"
    
    try:
        # Merge datasets on 'time'
        merged_data = pd.merge(wind_speed_data, daily_generation, on="time", how="left").fillna(0)
        
        # Extract Loc Nos (removing ".Wind-Speed" suffix)
        loc_nos = [col.replace(".Wind-Speed", "") for col in wind_speed_data.columns if col != "time"]

        # Prepare processed data
        processed_data = []
        for loc_no in loc_nos:
            processed_data.append({
                "Date": start_date,
                "Customer Name": customer_name,
                "Project": project,
                "Loc No": loc_no,
                "Avg Wind Speed": merged_data[f"{loc_no}.Wind-Speed"].mean(),
                "Total Daily Generation": merged_data.get(f"{loc_no}.Generation today", 0).sum(),
                "MA%": ma_percent
            })

        # Convert to DataFrame
        df = pd.DataFrame(processed_data)

        # Write aggregated data to CSV
        with open(output_file, mode="w", newline="") as file:
            writer = csv.writer(file)
            writer.writerow(["Date", "Customer Name", "Project", "Loc No", "Avg Wind Speed", "Total Daily Generation", "MA%"])
            writer.writerows(df.values)
        
        print(f"CSV report generated: {output_file}")
        # return {"status": "success", "result": "report generated", "file": output_file}
        return output_file

    except Exception as e:
        print(f"Error generating CSV report: {e}")
        return {"status": "failed", "result": "report not generated"}


def fetch_data(
    integration, plant_name: str, start_date: str, end_date: str, params: list, category: str, granularity: str, condition: dict = None
) -> pd.DataFrame:
    """
    Fetch data from the integration utility.
    """
    try:
        return pd.DataFrame(integration.fetchDataV2(plant_name, category, params, None, start_date, end_date, granularity, condition))
    except Exception as e:
        logging.error(f"Error fetching data for {params}: {e}")
        return pd.DataFrame()


def generate_dgr_pdf(
    date: str, customer_name: str, project: str,
    avg_wind_speed: float, daily_generation: float, ma_percent: str
) -> None:
    """
    Generate a PDF report for Daily Generation Report.
    """
    output_file = f"DGR_Report_{date}.pdf"
    try:
        doc = SimpleDocTemplate(output_file, pagesize=A4)
        elements = []
        styles = getSampleStyleSheet()

        elements.append(Paragraph("<b><font size=14>Daily Generation Report</font></b>", styles["Title"]))
        elements.append(Spacer(1, 10))

        data = [
            ["Date:", date],
            ["Customer Name:", customer_name],
            ["Project:", project],
            ["Avg Wind Speed (m/s):", avg_wind_speed],
            ["Daily Generation (MU):", daily_generation],
            ["MA%:", ma_percent]
        ]

        table = Table(data, colWidths=[150, 250])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 5),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        elements.append(table)
        doc.build(elements)
        logging.info(f"PDF report generated: {output_file}")
        return output_file
    except Exception as e:
        logging.error(f"Error generating PDF report: {e}")



def csv_to_pdf(csv_filename):

    """
    Converts a CSV file to a PDF file with table formatting.
    """
    pdf_filename = "convert_csv_to_pdf.pdf"
    try:
        df = pd.read_csv(csv_filename)  # Read the CSV file
        data = [df.columns.tolist()] + df.values.tolist()  # Convert DataFrame to list format

        doc = SimpleDocTemplate(pdf_filename, pagesize=A4)
        elements = []

        table = Table(data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        elements.append(table)
        doc.build(elements)

        print(f"PDF file '{pdf_filename}' generated successfully from '{csv_filename}'.")
        return pdf_filename

    except Exception as e:
        print(f"Error while converting CSV to PDF: {e}")




def merge_pdfs(pdf1, pdf2):
    """
    Merges two PDF files into a single PDF.
    """
    output_pdf = "final_report_wind.pdf"
    try:
        merger = PdfMerger()
        merger.append(pdf1)
        merger.append(pdf2)
        merger.write(output_pdf)
        merger.close()
        print(f"PDFs merged successfully into '{output_pdf}'")
    except Exception as e:
        print(f"Error while merging PDFs: {e}")


def main(server: str, token: str, plant_name: str, start_date: str, end_date: str, customer_name: str, project: str, ma_percent: str):
    integration = initialize_integration(server, token)
    wind_speed = fetch_data(integration, plant_name, start_date, end_date, ['WTUR.Wind-Speed'], 'Turbine', '15m', {"Wind-Speed": "mean"})
    daily_generation = fetch_data(integration, plant_name, start_date, end_date, ['WTUR.Generation today'], 'Turbine', '15m')
    
    second_pdf = generate_dgr_report(wind_speed, daily_generation, start_date, customer_name, project, ma_percent)

    avg_wind_speed = wind_speed.iloc[:, 1:].mean(axis=1).iloc[0] if not wind_speed.empty else 0
    avg_daily_generation = daily_generation.iloc[:, 1:].mean(axis=1).iloc[0] if not daily_generation.empty else 0
    
    first_pdf = generate_dgr_pdf(start_date, customer_name, project, avg_wind_speed, avg_daily_generation, ma_percent)
    print(type(second_pdf))
    convert_csv_to_pdf = csv_to_pdf(second_pdf)
    merge_pdfs(first_pdf, convert_csv_to_pdf)


def scheduled_task():
    yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
    main("IN", "1d8f9dc5-8f50-4ffc-8b90-1b40b866283c", "IN.INTE.SPFL", yesterday, yesterday, "Sipani Fibers", "INTEGRUM", "0")

# yesterday = '2025-01-24'
# main("IN", "1d8f9dc5-8f50-4ffc-8b90-1b40b866283c", "IN.INTE.KSIP", yesterday, yesterday, "Kyathi steels", "INTEGRUM", "0")


if __name__ == "__main__":
    # Schedule the task to run daily at 1:00 AM
    schedule.every().day.at("01:00").do(scheduled_task)
    
    logging.info("Scheduled task set to run daily at 1:00 AM")
    
    while True:
        schedule.run_pending()
        time.sleep(60)
