{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from IntegrationUtilities import PrescintoIntegrationUtilities"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["m = PrescintoIntegrationUtilities(server = 'IN',token ='1d8f9dc5-8f50-4ffc-8b90-1b40b866283c')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["#KSIPL SOLAR\n", "\n", "plantName = 'IN.INTE.KSIS'\n", "startDate = '2023-01-01'\n", "endDate = '2025-02-19'"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['MFM', 'SMB-24', 'WS', 'Inverter', 'Plant']\n"]}], "source": ["categories = None\n", "deviceDict = None\n", "parameterDict = None\n", "try:\n", "    categories, deviceDict, parameterDict = m.getPlantInfo(plantName)\n", "    print(categories)\n", "except Exception as e:\n", "    print(f'Error PlantInfo: {str(e)}')"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['MFM', 'SMB-24', 'WS', 'Inverter', 'Plant']\n"]}], "source": ["if categories is not None:\n", "    print(categories)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Devices of Inverter:\n", "['INV-01', 'INV-02', 'INV-03', 'INV-04', 'INV-05', 'INV-06', 'INV-07', 'INV-08', 'INV-09', 'INV-10', 'INV-11', 'INV-12', 'INV-13', 'INV-14', 'INV-15', 'INV-16', 'INV-17', 'INV-18', 'INV-19', 'INV-20', 'INV-21', 'INV-22', 'INV-23', 'INV-24', 'INV-25', 'INV-26', 'INV-27', 'INV-28', 'INV-29', 'INV-30', 'INV-31', 'INV-32', 'INV-33', 'INV-34', 'INV-35', 'INV-36', 'INV-37', 'INV-38', 'INV-39', 'INV-40', 'INV-41', 'INV-42']\n", "\n", "Weather Station:\n", "['MFM-01', 'MFM-02', 'MFM-03']\n"]}], "source": ["# Dictionary of devices based on categories\n", "if deviceDict is not None:\n", "    print(f'Devices of Inverter:\\n{deviceDict[\"Inverter\"]}')\n", "    print()\n", "    print(f'Weather Station:\\n{deviceDict[\"MFM\"]}')"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Parameters of Inverter:\n", "\n", "['AC Current Line1', 'AC Current Line2', 'AC Current Line3', 'AC Frequency', 'AC Voltage V1-N', 'AC Voltage V2-N', 'AC Voltage V3-N', 'Active Power', 'Daily Energy', 'Efficiency', 'Inverter Status', 'Inverter Temperature', 'PV Current', 'PV Power', 'PV Voltage', 'Specific Energy', 'Specific Power', 'Status', 'Total Energy']\n", "\n", "\n", "Parameters of Weather Station:\n", "\n", "['Ambient Temperature', 'Daily GHI Energy', 'Daily POA Energy', 'GH Irradiance', 'GHI Instantaneous Energy', 'Module Temperature', 'POA Instantaneous Energy', 'POA Irradiance', 'Wind Direction', 'Wind Speed']\n"]}], "source": ["# Dictionary of parameters\n", "if parameterDict is not None:\n", "    print(f'Parameters of Inverter:\\n')\n", "    print(parameterDict[\"Inverter\"])\n", "    print('\\n')\n", "    print('Parameters of Weather Station:\\n')\n", "    print(parameterDict[\"WS\"])"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\<PERSON><PERSON><PERSON><PERSON><PERSON>\\Wind_solar\\IntegrationUtilities.py:308: FutureWarning: Passing literal json to 'read_json' is deprecated and will be removed in a future version. To read from a literal string, wrap it in a 'StringIO' object.\n", "  dataResp = pd.read_json(resp, orient=\"split\")\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>time</th>\n", "      <th>Plant.Active Power</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-01-01T00:00:00Z+05:30</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-01-01T00:05:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-01-01T00:10:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-01-01T00:15:00Z+05:30</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-01-01T00:20:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>224923</th>\n", "      <td>2025-02-19T23:35:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>224924</th>\n", "      <td>2025-02-19T23:40:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>224925</th>\n", "      <td>2025-02-19T23:45:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>224926</th>\n", "      <td>2025-02-19T23:50:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>224927</th>\n", "      <td>2025-02-19T23:55:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>224928 rows × 2 columns</p>\n", "</div>"], "text/plain": ["                              time  Plant.Active Power\n", "0       2023-01-01T00:00:00Z+05:30                 0.0\n", "1       2023-01-01T00:05:00Z+05:30                 NaN\n", "2       2023-01-01T00:10:00Z+05:30                 NaN\n", "3       2023-01-01T00:15:00Z+05:30                 0.0\n", "4       2023-01-01T00:20:00Z+05:30                 NaN\n", "...                            ...                 ...\n", "224923  2025-02-19T23:35:00Z+05:30                 NaN\n", "224924  2025-02-19T23:40:00Z+05:30                 NaN\n", "224925  2025-02-19T23:45:00Z+05:30                 NaN\n", "224926  2025-02-19T23:50:00Z+05:30                 NaN\n", "224927  2025-02-19T23:55:00Z+05:30                 NaN\n", "\n", "[224928 rows x 2 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["invAcPwrDf = m.fetchDataV2(plantName,['Plant'],['Active Power'],None, startDate, endDate)\n", "invAcPwrDf"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "df = pd.DataFrame(invAcPwrDf)\n", "df['time'] = pd.to_datetime(df['time'])"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Index: 224928 entries, 0 to 224927\n", "Data columns (total 2 columns):\n", " #   Column              Non-Null Count   Dtype                    \n", "---  ------              --------------   -----                    \n", " 0   time                224928 non-null  datetime64[ns, UTC+05:30]\n", " 1   Plant.Active Power  14656 non-null   float64                  \n", "dtypes: datetime64[ns, UTC+05:30](1), float64(1)\n", "memory usage: 13.2+ MB\n"]}], "source": ["df.info()"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Plant.Active Power</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>14656.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>2526.906157</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>3433.246301</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>-1584.170000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>6036.082500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>8991.250000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Plant.Active Power\n", "count        14656.000000\n", "mean          2526.906157\n", "std           3433.246301\n", "min          -1584.170000\n", "25%              0.000000\n", "50%              0.000000\n", "75%           6036.082500\n", "max           8991.250000"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["df.describe()"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["time                       0\n", "Plant.Active Power    210272\n", "dtype: int64"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["df.isnull().sum()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Missing data based on time"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>time</th>\n", "      <th>Plant.Active Power</th>\n", "    </tr>\n", "    <tr>\n", "      <th>time</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-01-01 00:00:00+05:30</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-01 00:05:00+05:30</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-01 00:10:00+05:30</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-01 00:15:00+05:30</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-01 00:20:00+05:30</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-02-19 23:35:00+05:30</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-02-19 23:40:00+05:30</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-02-19 23:45:00+05:30</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-02-19 23:50:00+05:30</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-02-19 23:55:00+05:30</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>224928 rows × 2 columns</p>\n", "</div>"], "text/plain": ["                           time  Plant.Active Power\n", "time                                               \n", "2023-01-01 00:00:00+05:30     0                   0\n", "2023-01-01 00:05:00+05:30     0                   1\n", "2023-01-01 00:10:00+05:30     0                   1\n", "2023-01-01 00:15:00+05:30     0                   0\n", "2023-01-01 00:20:00+05:30     0                   1\n", "...                         ...                 ...\n", "2025-02-19 23:35:00+05:30     0                   1\n", "2025-02-19 23:40:00+05:30     0                   1\n", "2025-02-19 23:45:00+05:30     0                   1\n", "2025-02-19 23:50:00+05:30     0                   1\n", "2025-02-19 23:55:00+05:30     0                   1\n", "\n", "[224928 rows x 2 columns]"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["missing_summary = df.isnull().groupby(df.time).sum()\n", "missing_summary"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Missing Data Visualization"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20124\\2387286398.py:3: FutureWarning: \n", "\n", "Passing `palette` without assigning `hue` is deprecated and will be removed in v0.14.0. Assign the `x` variable to `hue` and set `legend=False` for the same effect.\n", "\n", "  sns.barplot(x=[\"Available Data\", \"Missing Data\"], y=[100 - missing_percentage, missing_percentage], palette=[\"green\", \"red\"])\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["missing_percentage = df[\"Plant.Active Power\"].isna().mean() * 100\n", "plt.figure(figsize=(6, 4))\n", "sns.barplot(x=[\"Available Data\", \"Missing Data\"], y=[100 - missing_percentage, missing_percentage], palette=[\"green\", \"red\"])\n", "plt.ylabel(\"Percentage\")\n", "plt.title(\"Missing Data Overview\")\n", "plt.ylim(0, 100)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Result:                                                                                                             \n", "The dataset contains missing values, particularly in the Plant Active Power column.                                 \n", "Missing values occur at specific times, possibly due to sensor failures or maintenance"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Daily Average Power Trend"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1400x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["df_daily = df.resample('D', on='time').mean()\n", "plt.figure(figsize=(14, 5))\n", "plt.plot(df_daily.index, df_daily[\"Plant.Active Power\"], color='blue', alpha=0.6)\n", "plt.xlabel(\"Date\")\n", "plt.ylabel(\"Daily Average Active Power (kW)\")\n", "plt.title(\"Daily Active Power Trend Over Time\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Result:                                                                                                             \n", "The daily average power trend shows fluctuations in energy production over time.                                    \n", "A clear seasonal pattern is visible, with higher power generation during summer months and lower output during winter months.                                                                                                       \n", "Periodic dips in power output might indicate cloudy days, maintenance shutdowns, or sensor issues.                  \n", "The trend confirms that solar generation is affected by weather conditions, daylight hours, and system efficiency.  "]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA1sAAAHWCAYAAACBjZMqAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAZ7RJREFUeJzt3Qd4FOXWwPETCCX0EkKRXqSJIKB0BUGqXpoFpSkIilgALyjfRURRURAQEEUslItIuYIiIEWqFOklFAGl994hIcl8z3mXWXdDCEnIZJPs//c8w+6UnZ3dnbBz9rzveQMsy7IEAAAAAJCo0iTu7gAAAAAAimALAAAAABxAsAUAAAAADiDYAgAAAAAHEGwBAAAAgAMItgAAAADAAQRbAAAAAOAAgi0AAAAAcADBFgAAAAA4gGALAGIxYMAACQgISJLnqlu3rplsS5cuNc/9v//9L0me//nnn5eiRYtKcnb58mV58cUXJV++fOa96dGjh68PSfbv32+OZfz48b4+FABAMkOwBcBv6MWwXhTbU8aMGaVAgQLSqFEjGTlypFy6dClRnufo0aMmSNu8ebMkN8n52OLio48+Mp9jt27d5L///a+0b9/+jo+JjIw0n7N+5r/++muCn3vy5Mny2WefSXJhB3n2lDZtWilcuLC0bNkyxX6+MVm5cqV5TXnz5pUMGTKYHwReeuklOXjwYIL3efXqVfN3oD9oJIW5c+ea5wPgfwIsy7J8fRAAkBT0Iv2FF16Q999/X4oVKyY3btyQ48ePmwuuhQsXmgvVWbNmyf333+9+TEREhJk0MIur9evXy4MPPijjxo0z2aK4Cg8PN7fp06c3t3pc9erVk+nTp8uTTz4Zr9eakGPT9yMqKspc0CZX1atXl8DAQFmxYkWcH6OfbcOGDc1Feq1atWTSpEkJeu7HH39ctm3bZoIcT/o1GhYWJunSpTMBT1LR49Dz+Nlnn5WmTZuaoHLnzp3y5ZdfmuP5448/pFKlSpKSjRo1St544w0pXry4OV/z589vXuM333zjDmJq1qwZ7/2ePn1a8uTJI++++26SBEGvvvqqjB492pwrAPxLoK8PAACSWpMmTaRq1aru+b59+8rixYvNxfS//vUvczEXFBRk1umFvU5O0l/ZM2XK5A6yfEWDheTu5MmTUq5cuXg9RoOrypUrS8eOHeX//u//5MqVK5I5c+ZEOyY7S+or+tratWvnnteAUs9jDbq++uorSc7sc/92GS1tJlq7dm2ZN2+e13aa2dTXqT9CbN++XXLmzJmERw0AcUczQgAQkUcffVTeeecdOXDggFfmI6Y+W5op0QvAHDlySJYsWaR06dLmIt7ORmnmSGkWzW7iZffn0T5Z9913n2zYsEEefvhhcwFpPzZ6ny2bZix0G+2npEGCXkgfOnTIaxvN2sSURfPc552OLaY+WxqYvPnmm1KoUCGT8dLX+umnn97yC73uR3+9/+mnn8zr023Lly9vLpLjGkR17tzZNBXTwKVixYoyYcKEW/qv7du3T+bMmeM+9uhZpuiuXbsmM2fOlDZt2sjTTz9t5n/++ecYt9Umho888ohkzZpVsmXLZt4rbTpov4/6vHp+2M9tv1fR+2zp+6Pzum10GthrUH3u3Dn3sjVr1kjjxo0le/bs5nzQY9BA427OZaXvlU2zo1WqVDE/IgQHB5vg7MiRI+71mtHVY966dat72Y8//miWtWrVymv/ZcuWlWeeecZrmf7N2PvPlSuXeb+jn6OxnfsxGThwoHl+PQ+iB2QlSpSQwYMHy7Fjx7wCytv9DXme2/p5aVZLvffee+7P085w6bb6d713717TxFj/5rQZqmbEPc97+5yM3hQx+vmg+9OslvJs9gnAPxBsAcBNdv+fBQsW3HYb/RVdM2DaTEsvvoYOHWqCH/viWC9Edbnq2rWr6Vekk15c2s6cOWOya9rES/sAaVPB2Hz44YfmQv+tt96S119/3QR7DRo0MIFDfMTl2DzphaW+tuHDh5tgYNiwYSbY6t27t/Tq1euW7bVp3yuvvGIutPVC+Pr169K6dWvzemOjr0MvkPVY2rZtK0OGDDGBh16kjhgxwn3sul4DBX3f7GO3L5pvR4MILaqhx6TBqj7P999/f8t2emHcrFkzOXv2rAmIPv74Y/M8drD4n//8x8zr89vPfbv+WxrU6cX0tGnTblmny7RJo52J0Yyqvv8XL140Tdq0T9r58+dNwLR27VpJiL///tvc5s6d2/3a9Ji0ieOgQYOkS5cuMmPGDPODgT6X0vt6zMuXL3fv5/fff5c0adJ4Ndk8deqU/Pnnn17njJ6fHTp0kFKlSplzRLNRixYtMtvY+4/vua8ZL91HnTp1TFPJmGjAp0H97Nmz4/X+6DmjWT+lfcHsz9MzqNQfOPSc1+Bfz2UNJPXz0Sm+tH/ZY489Zu7bz6UTAD+hfbYAwB+MGzdOf5a21q1bd9ttsmfPbj3wwAPu+Xfffdc8xjZ8+HAzf+rUqdvuQ/ev2+jzRffII4+YdWPGjIlxnU62JUuWmG3vuece6+LFi+7l06ZNM8tHjBjhXlakSBGrY8eOd9xnbMemj9f92H766Sez7QcffOC13ZNPPmkFBARYf/31l3uZbpc+fXqvZVu2bDHLR40aZcXms88+M9tNmjTJvSw8PNyqUaOGlSVLFq/XrsfXrFkzK64ef/xxq1atWu75sWPHWoGBgdbJkyfdy86fP29lzZrVqlatmnXt2jWvx0dFRbnv6/N6vj+2ffv23fKe6rFXqVLFa7u1a9ea7SZOnOjed6lSpaxGjRp5Pc/Vq1etYsWKWY899lisr81+3vfee8+cj8ePH7eWLl1qzl9d/uOPP5r3MSQkxLrvvvu8Xtvs2bPNNv3793cvK1++vPX000+75ytXrmw99dRTZrudO3eaZTNmzDDz+tmq/fv3W2nTprU+/PBDr2MLDQ0177Pn8tjO/eg2b95stn3jjTdi3e7++++3cuXKddvz/Xbntr5fun/9+45pW1332muvuZfp56Ofv57j9t++/fept3c6H7p37+71/wgA/0FmCwA8aPOh2KoSatNBpU3RtJhEQuiv8dqML640a6BN22zaT0ULBWhxACfp/jUbotk0T9qsUOOr6JX9NNumzbtsWmhEm+Npc6w7PY9mnbTQg2f/MX1ezUotW7YsQcevWZT58+d77VczbdGzTpop1M/87bffvqXvVUKbe2nWRZvL2VkmNXXqVPPZN2/e3MxrxcA9e/bIc889Z45VizbopE0369evb7JMcTnHNNui2Ro7c6fP+cknn5hMjRZE0SaamnH0fG2axStTpozJmNo0i6TZLKXvx5YtW0wGVLN59nK91b8BbQ6oNEOmx6iZM/v4ddJj0UzXkiVLEnTu23+Dnud9THS9ZgWdoM1iozeT1SI2v/32myPPByB1ItgCAA96cR/bBZ5eRGvHfB3rSZsYafM0vXCPT+B1zz33xKsYhl60etILv5IlS96xv9Ld0j5H2lcl+vuhTfrs9Z60mmN02lzOs3/S7Z5HX6M2WYvL88SVBjdaYfGBBx6Qv/76y0zaTLBatWpeTQntgMgOIBLDU089ZV6PHoPS4FT7TWkTOg1AlQZaSgt3aLDkOWm1PW2qeuHChTs+lwZEGjBqszsN8DS46tOnj9d7p80/o9Ngy/O91WBL+0Dp+7Rq1SpzntWoUcMrCNNbPf/tz0pfg742/fyivwYtNKPHkpBz3z7n7jQcg66/U0CWEPr6tAKip3vvvdfcOv13ByB1oRohANx0+PBhc3GrgcztaAEAzTjoL/aaFdA+PXpBrX1stK9XXEp/25UOE9PtMjDa9ySpypHf7nl8Ve7aDqg0OIiJZtyiX1AnFg1SNUjRQFyLQGgZdh0XSjNONjtA1z5qtyvRrpnWO9FAR7OKd0v7bSk9v/W90SqHWhxCX4eOQ6c/RGzatMn00fJ8Dfb4ZTF9/tGPP67nvv4NahVQz4Id0WkwumvXLq/KonosMZ1v+neQlH9zAGAj2AKAm+xO61qB7E6/emszL520IIAWNdACChqA6UVvYlcaszMgNr2Y1OyD53hgmkGKXoxAaebCM6CIz7EVKVLENJmKnj3QAgn2+sSg+9GLar1w98xu3c3zaCU+zc5o0y+t7udJn0eLoWilwX79+rmbPuoYWrEF2vH9XDULqs33NCDQgFwr6j3xxBPu9fbzaqYrMYKlmNjvnR6DXaXQpss831vNTOqk2SsNtjTIUlroQguiaGZOAwnP4hj6GvR81CIWduYnMWiQp8UztICInsMxnQMayGrApQVrPP8OYmq2Gj07eqfPUs8R3Y/na9q9e7e5tasa2kVOov/dxZSJpfog4L9oRggAN6vCaalpvWjUini3o83QorOzEnrhp+wxnGIKfhJi4sSJXs2p/ve//5nmXtokzfOiV7Mn9sDISqu0RS+/HZ9jswfK/fzzz72Wa3VCvXj0fP67oc+jg0vbTe6UDiStA9pqZiR6sBSfrJY2p9M+bp6T9i/SfdrbaHVADSa1Up9WUPTkmSXR9y4uzfo8+4dptueHH34wgYoGBZ7je2mFO/3ctFS8Zo2i08p/d0uzPiEhITJmzBj3+ak0E6XN/LTvlicNsPRvQSsh2sGWnt/6/miFRs1M6XHbtF+YvkYtoR49o6Tzd6pEGRsNhHUfWpUyeuVNDab1s9W+i1rtz6bvpwbpnu+d9j2LXkrfLiUf29+B53mvx6Hz2pdQf2RRGgDqa/es4Ki++OKLW/aV2P8nAEg5yGwB8Dt6oakXZHpBf+LECXNxqX1e9OJJS4XHNkCtlk7Xiyu9SNXttU+KXlwVLFjQ3QxLL/i0iIBe4OpFql5oaT+h25WwvhMdt0j3rYUF9Hi1ZLZmYLSEt037kGkQpuWqNZjQfkg69pFnwYr4HptmYTS7oFk77aeiY19pU0ktDqLlvaPvO6G0z5GOlaQX1drnSDMH+lr0Allfa0L65GggpUGCjg8WEy1p/9prr8nGjRtNczkNIPU91LG1tGCFZi30Il1LkNvjfWmQoQGhZnl0Ow0EPTNV0WmQo++fZj81WI4+NpVm8bRvlgatOiaZfr7ap0nHv9IsqWa8fvnlF7kbGhxo00XdtwaYWixEzyEtqa/vc8+ePb221wBL3zsNpu3zWQOKmjVrmmIjWoDDs8+VngMffPCBKZev50iLFi3M56XBkI5vpp/tv//97wQdu2bQNBDV91uzuHp+aHClf7tff/21yT5pcRXPAY07depk3m/NTuu4bfr3qee6vr+ehTQ0aNTBsfXz1OyV/o1pnz27357+H6BNhLU/nf596P8Z2mxYm4Taww3o8ATaN09/FND3S98L/YEjej81ZQeoWvRFj03fU+3vCcAP+LocIgAkdel3e9Iyzvny5TMltrWMumeJ8duVfl+0aJHVvHlzq0CBAubxevvss89au3fv9nrczz//bJUrV86Uv/YsA61lqbXEdkxuV/r9hx9+sPr27WtKeAcFBZkS1AcOHLjl8UOHDjVl4jNkyGDKna9fvz7GUti3O7bo5bHVpUuXrJ49e5rXmS5dOlOqfMiQIV6lypXuR8tbR3e7kvTRnThxwnrhhRes4OBg875WqFAhxvL0cSn9vmHDBnM877zzzm230ZLluo2+NtusWbOsmjVrmvc4W7Zs1kMPPWTee9vly5et5557zsqRI4d5rP1exVTq2/b111+bdVpaPnpZedumTZusVq1aWblz5zafne5XS7DruRYb+3n187iTqVOnmpLwun8tld62bVvr8OHDt2y3fft2s8+yZct6Ldfy/7G9p1pmvnbt2lbmzJnNVKZMGXM+7Nq1y71NbOd+bJYvX27+5vTc0HOwcOHCVpcuXcxnGBMdQqB48eLmPKpUqZI1f/78GM/tVatWmfL8up1nGXjdVl/D33//bTVs2NDKlCmTlTdvXrM+MjLSax9aBr5169Zmm5w5c1ovvfSStW3btlvOh4iICFNKPk+ePGbYBC6/AP8RoP/4OuADAABIDjSDppnVmJp2AkB80WcLAAAAABxAsAUAAAAADiDYAgAAAAAH0GcLAAAAABxAZgsAAAAAHECwBQAAAAAOYFDjONCBE48ePWoGatSBCwEAAAD4J8uyzGD1BQoUMAPUx4ZgKw400CpUqJCvDwMAAABAMnHo0CEpWLBgrNsQbMWBZrTsNzRbtmy+PhwAAAAAPnLx4kWTiLFjhNgQbMWB3XRQAy2CLQAAAAABceheRIEMAAAAAHAAwRYAAAAAOIBgCwAAAAAcQLAFAAAAAA4g2AIAAAAABxBsAQAAAIADCLYAAAAAwAEEWwAAAADgAIItAAAAAHAAwRYAAAAAOIBgCwAAAAAcQLAFAAAAAA4g2AIAAAAABxBsAQAAAIADAp3YKeAvDh48KKdPn47344KDg6Vw4cKOHBMAAACSB4It4C4CrTJlysq1a1fj/digoEzy5587CbgAAABSMYItIIE0o6WBVsuWkyRPnrJxftypUztl5sx25vEEWwAAAKkXwRZwlzTQyp+/sq8PAwAAAMkMBTIAAAAAILUFW5GRkfLOO+9IsWLFJCgoSEqUKCEDBw4Uy7Lc2+j9/v37S/78+c02DRo0kD179njt5+zZs9K2bVvJli2b5MiRQzp37iyXL1/22mbr1q1Sp04dyZgxoxQqVEgGDx6cZK8TAAAAgP/xabD1ySefyJdffimff/657Ny508xrEDRq1Cj3Njo/cuRIGTNmjKxZs0YyZ84sjRo1kuvXr7u30UBr+/btsnDhQpk9e7YsX75cunbt6l5/8eJFadiwoRQpUkQ2bNggQ4YMkQEDBsjYsWOT/DUDAAAA8A8+7bO1atUqad68uTRr1szMFy1aVH744QdZu3atO6v12WefSb9+/cx2auLEiZI3b1756aefpE2bNiZImzdvnqxbt06qVq1qttFgrWnTpvLpp59KgQIF5Pvvv5fw8HD57rvvJH369FK+fHnZvHmzDBs2zCsoAwAAAIBUkdmqWbOmLFq0SHbv3m3mt2zZIitWrJAmTZqY+X379snx48dN00Fb9uzZpVq1arJ69Wozr7fadNAOtJRunyZNGpMJs7d5+OGHTaBl0+zYrl275Ny5c7ccV1hYmMmGeU4AAAAAkGIyW2+//bYJZMqUKSNp06Y1fbg+/PBD0yxQaaClNJPlSeftdXobEhLitT4wMFBy5crltY32C4u+D3tdzpw5vdYNGjRI3nvvvUR/vQAAAAD8h08zW9OmTTNN/CZPniwbN26UCRMmmKZ/eutLffv2lQsXLrinQ4cO+fR4AAAAAKQ8Ps1s9e7d22S3tO+VqlChghw4cMBkljp27Cj58uUzy0+cOGGqEdp0vlKlSua+bnPy5Emv/UZERJgKhfbj9VYf48met7fxlCFDBjMBAAAAQIrMbF29etX0rfKkzQmjoqLMfW36p8GQ9uuyabND7YtVo0YNM6+358+fN1UGbYsXLzb70L5d9jZaofDGjRvubbRyYenSpW9pQggAAAAAKT7YeuKJJ0wfrTlz5sj+/ftl5syZpkJgy5YtzfqAgADp0aOHfPDBBzJr1iwJDQ2VDh06mAqDLVq0MNuULVtWGjduLF26dDFVDFeuXCmvvvqqyZbpduq5554zxTF0/C0tET916lQZMWKE9OrVy5cvHwAAAEAq5tNmhFqiXQc1fuWVV0xTQA2OXnrpJTOIsa1Pnz5y5coVU6JdM1i1a9c2pd51cGKb9vvSAKt+/fomU9a6dWszNpdnBcMFCxZI9+7dpUqVKhIcHGyeg7LvAAAAAJwSYOlgVoiVNl3UgE2LZWTLls3Xh4NkQou6aPDetesGyZ+/cpwfd+zYRhk7topp+lq5ctwfBwAAgJQVG/i0GSEAAAAApFYEWwAAAADgAIItAAAAAHAAwRYAAAAAOIBgCwAAAAAcQLAFAAAAAA4g2AIAAAAABxBsAQAAAIADCLYAAAAAwAEEWwAAAADgAIItAAAAAHAAwRYAAAAAOIBgCwAAAAAcQLAFAAAAAA4g2AIAAAAABxBsAQAAAIADCLYAAAAAwAEEWwAAAADgAIItAAAAAHAAwRYAAAAAOIBgCwAAAAAcQLAFAAAAAA4g2AIAAAAABxBsAQAAAIADCLYAAAAAwAEEWwAAAADgAIItAAAAAHAAwRYAAAAAOIBgCwAAAAAcQLAFAAAAAA4g2AIAAAAABxBsAQAAAIADCLYAAAAAwAEEWwAAAACQ2oKtokWLSkBAwC1T9+7dzfrr16+b+7lz55YsWbJI69at5cSJE177OHjwoDRr1kwyZcokISEh0rt3b4mIiPDaZunSpVK5cmXJkCGDlCxZUsaPH5+krxMAAACA//FpsLVu3To5duyYe1q4cKFZ/tRTT5nbnj17yi+//CLTp0+XZcuWydGjR6VVq1bux0dGRppAKzw8XFatWiUTJkwwgVT//v3d2+zbt89sU69ePdm8ebP06NFDXnzxRZk/f74PXjEAAAAAfxHoyyfPkyeP1/zHH38sJUqUkEceeUQuXLgg3377rUyePFkeffRRs37cuHFStmxZ+eOPP6R69eqyYMEC2bFjh/z222+SN29eqVSpkgwcOFDeeustGTBggKRPn17GjBkjxYoVk6FDh5p96ONXrFghw4cPl0aNGvnkdQMAAABI/ZJNny3NTk2aNEk6depkmhJu2LBBbty4IQ0aNHBvU6ZMGSlcuLCsXr3azOtthQoVTKBl0wDq4sWLsn37dvc2nvuwt7H3EZOwsDCzD88JAAAAAFJksPXTTz/J+fPn5fnnnzfzx48fN5mpHDlyeG2ngZWus7fxDLTs9fa62LbRAOratWsxHsugQYMke/bs7qlQoUKJ+EoBAAAA+INkE2xpk8EmTZpIgQIFfH0o0rdvX9OM0Z4OHTrk60MCAAAAkML4tM+W7cCBA6bf1YwZM9zL8uXLZ5oWarbLM7ul1Qh1nb3N2rVrvfZlVyv03CZ6BUOdz5YtmwQFBcV4PFq1UCcAAAAASNGZLS18oWXbtWqgrUqVKpIuXTpZtGiRe9muXbtMqfcaNWqYeb0NDQ2VkydPurfRioYaSJUrV869jec+7G3sfQAAAABAqgy2oqKiTLDVsWNHCQz8J9GmfaU6d+4svXr1kiVLlpiCGS+88IIJkrQSoWrYsKEJqtq3by9btmwx5dz79etnxuayM1Mvv/yy7N27V/r06SN//vmnfPHFFzJt2jRTVh4AAAAAUm0zQm0+qNkqrUIYnZZnT5MmjRnMWCsEahVBDZZsadOmldmzZ0u3bt1MEJY5c2YTtL3//vvubbTs+5w5c0xwNWLECClYsKB88803lH0HAAAAkLqDLc1OWZYV47qMGTPK6NGjzXQ7RYoUkblz58b6HHXr1pVNmzbd9bECAAAAQIppRggAAAAAqRHBFgAAAAA4gGALAAAAABxAsAUAAAAADiDYAgAAAAAHEGwBAAAAgAMItgAAAADAAQRbAAAAAOAAgi0AAAAAcADBFgAAAAA4gGALAAAAABxAsAUAAAAADiDYAgAAAAAHEGwBAAAAgAMItgAAAADAAQRbAAAAAOAAgi0AAAAAcADBFgAAAAA4gGALAAAAABxAsAUAAAAADiDYAgAAAAAHEGwBAAAAgAMItgAAAADAAQRbAAAAAOAAgi0AAAAAcADBFgAAAAA4gGALAAAAABxAsAUAAAAADiDYAgAAAAAHEGwBAAAAgAMItgAAAADAAQRbAAAAAOAAgi0AAAAAcADBFgAAAACkxmDryJEj0q5dO8mdO7cEBQVJhQoVZP369e71lmVJ//79JX/+/GZ9gwYNZM+ePV77OHv2rLRt21ayZcsmOXLkkM6dO8vly5e9ttm6davUqVNHMmbMKIUKFZLBgwcn2WsEAAAA4H98GmydO3dOatWqJenSpZNff/1VduzYIUOHDpWcOXO6t9GgaOTIkTJmzBhZs2aNZM6cWRo1aiTXr193b6OB1vbt22XhwoUye/ZsWb58uXTt2tW9/uLFi9KwYUMpUqSIbNiwQYYMGSIDBgyQsWPHJvlrBgAAAOAfAn355J988onJMo0bN869rFixYl5Zrc8++0z69esnzZs3N8smTpwoefPmlZ9++knatGkjO3fulHnz5sm6deukatWqZptRo0ZJ06ZN5dNPP5UCBQrI999/L+Hh4fLdd99J+vTppXz58rJ582YZNmyYV1AGAAAAAKkiszVr1iwTID311FMSEhIiDzzwgHz99dfu9fv27ZPjx4+bpoO27NmzS7Vq1WT16tVmXm+16aAdaCndPk2aNCYTZm/z8MMPm0DLptmxXbt2mexadGFhYSYb5jkBAAAAQIoJtvbu3StffvmllCpVSubPny/dunWT119/XSZMmGDWa6ClNJPlSeftdXqrgZqnwMBAyZUrl9c2Me3D8zk8DRo0yAR19qTZNwAAAABIMcFWVFSUVK5cWT766COT1dImfV26dDH9s3ypb9++cuHCBfd06NAhnx4PAAAAgJTHp8GWVhgsV66c17KyZcvKwYMHzf18+fKZ2xMnTnhto/P2Or09efKk1/qIiAhTodBzm5j24fkcnjJkyGAqG3pOAAAAAJBigi2tRKj9pjzt3r3bVA20i2VoMLRo0SL3eu0/pX2xatSoYeb19vz586bKoG3x4sUma6Z9u+xttELhjRs33Nto5cLSpUt7VT4EAAAAgFQRbPXs2VP++OMP04zwr7/+ksmTJ5ty7N27dzfrAwICpEePHvLBBx+YYhqhoaHSoUMHU2GwRYsW7kxY48aNTfPDtWvXysqVK+XVV181lQp1O/Xcc8+Z4hg6/paWiJ86daqMGDFCevXq5cuXDwAAACAV82np9wcffFBmzpxp+ki9//77JpOlpd513Cxbnz595MqVK6Y/l2awateubUq96+DENi3trgFW/fr1TRXC1q1bm7G5bFrkYsGCBSaIq1KligQHB5uBkin7DgAAAMApAZYOZoVYadNFDdi0WAb9t2DbuHGjCd67dt0g+fNXjvPjjh3bKGPHVjFNX7VADAAAAFJnbODTZoQAAAAAkFoRbAEAAACAAwi2AAAAAMABBFsAAAAA4ACCLQAAAABwAMEWAAAAADiAYAsAAAAAHECwBQAAAAAOINgCAAAAAAcQbAEAAACAAwi2AAAAAMABBFsAAAAA4ACCLQAAAABwAMEWAAAAADiAYAsAAAAAHECwBQAAAAAOINgCAAAAAAcQbAEAAACAAwi2AAAAAMABBFsAAAAA4ACCLQAAAABwAMEWAAAAADiAYAsAAAAAHECwBQAAAAAOINgCAAAAAAcQbAEAAACAAwi2AAAAAMABBFsAAAAA4ACCLQAAAABwAMEWAAAAADiAYAsAAAAAHECwBQAAAAAOINgCAAAAAAcQbAEAAABAagu2BgwYIAEBAV5TmTJl3OuvX78u3bt3l9y5c0uWLFmkdevWcuLECa99HDx4UJo1ayaZMmWSkJAQ6d27t0RERHhts3TpUqlcubJkyJBBSpYsKePHj0+y1wgAAADAP/k8s1W+fHk5duyYe1qxYoV7Xc+ePeWXX36R6dOny7Jly+To0aPSqlUr9/rIyEgTaIWHh8uqVatkwoQJJpDq37+/e5t9+/aZberVqyebN2+WHj16yIsvvijz589P8tcKAAAAwH8E+vwAAgMlX758tyy/cOGCfPvttzJ58mR59NFHzbJx48ZJ2bJl5Y8//pDq1avLggULZMeOHfLbb79J3rx5pVKlSjJw4EB56623TNYsffr0MmbMGClWrJgMHTrU7EMfrwHd8OHDpVGjRkn+egEAAAD4hwRltvbu3ZtoB7Bnzx4pUKCAFC9eXNq2bWuaBaoNGzbIjRs3pEGDBu5ttYlh4cKFZfXq1WZebytUqGACLZsGUBcvXpTt27e7t/Hch72NvY+YhIWFmX14TgAAAADgeLCl/Z60Wd6kSZNMv6qEqlatmmn2N2/ePPnyyy9Nk786derIpUuX5Pjx4yYzlSNHDq/HaGCl65TeegZa9np7XWzbaAB17dq1GI9r0KBBkj17dvdUqFChBL9GAAAAAP4pQcHWxo0b5f7775devXqZJoAvvfSSrF27Nt77adKkiTz11FNmX5ptmjt3rpw/f16mTZsmvtS3b1/TjNGeDh065NPjAQAAAOAnwZb2jRoxYoQpWPHdd9+Zwha1a9eW++67T4YNGyanTp1K0MFoFuvee++Vv/76ywRxWvhCgy9PWo3Q7uOlt9GrE9rzd9omW7ZsEhQUFONxaNVCXe85AQAAAECSVSPU4hZaHVCrBX7yyScmSPr3v/9tmt116NDBBGHxcfnyZfn7778lf/78UqVKFUmXLp0sWrTIvX7Xrl2mT1eNGjXMvN6GhobKyZMn3dssXLjQBEflypVzb+O5D3sbex8AAAAAkOyCrfXr18srr7xigiPNaGmgpcGSBjOa9WrevHmsj9fttaT7/v37Ten2li1bStq0aeXZZ581faU6d+5smiouWbLEFMx44YUXTJCklQhVw4YNTVDVvn172bJliynn3q9fPzM2l2an1Msvv2wKevTp00f+/PNP+eKLL0wzRS0rDwAAAADJqvS7BlZahl0zTU2bNpWJEyea2zRpXLGbllrXwhdFixaNdT+HDx82gdWZM2ckT548pimilnXX+0rLs+s+dTBjrRCo/bo0WLJpYDZ79mzp1q2bCcIyZ84sHTt2lPfff9+9jR7LnDlzTHClTR8LFiwo33zzDWXfAQAAACS/YEsrB3bq1Emef/55k9WKSUhIiBknKzZTpkyJdX3GjBll9OjRZrqdIkWKmMIasalbt65s2rQp1m0AAAAAwOfBlo6NdSdatl2zTAAAAADgjxLUZ0ubEGpRjOh02YQJExLjuAAAAADA/4ItHfQ3ODg4xqaDH330UWIcFwAAAAD4X7Cl5de18ERM/ad0HQAAAAD4uwQFW5rB2rp16y3Ltfx67ty5E+O4AAAAAMD/gi0t1/7666+b8a8iIyPNtHjxYnnjjTekTZs2iX+UAAAAAOAP1QgHDhxoBiKuX7++BAa6dhEVFSUdOnSgzxYAAAAAJDTY0rLuU6dONUGXNh0MCgqSChUqmD5bAAAAAIAEBlu2e++910wAAAAAgEQItrSP1vjx42XRokVy8uRJ04TQk/bfAgAAAAB/lqBgSwthaLDVrFkzue+++yQgICDxjwwAAAAA/C3YmjJlikybNk2aNm2a+EcEAAAAAP5a+l0LZJQsWTLxjwYAAAAA/DnYevPNN2XEiBFiWVbiHxEAAAAA+GszwhUrVpgBjX/99VcpX768pEuXzmv9jBkzEuv4AAAAAMB/gq0cOXJIy5YtE/9oAAAAAMCfg61x48Yl/pEAAAAAgL/32VIRERHy22+/yVdffSWXLl0yy44ePSqXL19OzOMDAAAAAP/JbB04cEAaN24sBw8elLCwMHnssccka9as8sknn5j5MWPGJP6RAgAAAEBqz2zpoMZVq1aVc+fOSVBQkHu59uNatGhRYh4fAAAAAPhPZuv333+XVatWmfG2PBUtWlSOHDmSWMcGAAAAAP6V2YqKipLIyMhblh8+fNg0JwQAAAAAf5egYKthw4by2WefuecDAgJMYYx3331XmjZtmpjHBwAAAAD+04xw6NCh0qhRIylXrpxcv35dnnvuOdmzZ48EBwfLDz/8kPhHCQAAAAD+EGwVLFhQtmzZIlOmTJGtW7earFbnzp2lbdu2XgUzAAAAAMBfBSb4gYGB0q5du8Q9GgAAAADw52Br4sSJsa7v0KFDQo8HAAAAAPw32NJxtjzduHFDrl69akrBZ8qUiWALAAAAgN9LUDVCHczYc9I+W7t27ZLatWtTIAMAAAAAEhpsxaRUqVLy8ccf35L1AgAAAAB/lGjBll004+jRo4m5SwAAAADwnz5bs2bN8pq3LEuOHTsmn3/+udSqVSuxjg0AAAAA/CvYatGihdd8QECA5MmTRx599FEz4DEAAAAA+LsEBVtRUVGJfyQAAAAAkIokap+tu6HFNTRD1qNHD/ey69evS/fu3SV37tySJUsWad26tZw4ccLrcQcPHpRmzZqZkvMhISHSu3dviYiI8Npm6dKlUrlyZcmQIYOULFlSxo8fn2SvCwAAAIB/SlBmq1evXnHedtiwYXfcZt26dfLVV1/J/fff77W8Z8+eMmfOHJk+fbpkz55dXn31VWnVqpWsXLnSrI+MjDSBVr58+WTVqlWm35iO8ZUuXTr56KOPzDb79u0z27z88svy/fffy6JFi+TFF1+U/PnzS6NGjeL92gEAAADAsWBr06ZNZtLBjEuXLm2W7d69W9KmTWsySDbNVN2JjtHVtm1b+frrr+WDDz5wL79w4YJ8++23MnnyZNMXTI0bN07Kli0rf/zxh1SvXl0WLFggO3bskN9++03y5s0rlSpVkoEDB8pbb70lAwYMMIMsjxkzRooVK+buS6aPX7FihQwfPpxgCwAAAEDyakb4xBNPyMMPPyyHDx+WjRs3munQoUNSr149efzxx2XJkiVmWrx48R33pc0ENfPUoEEDr+UbNmwwwZzn8jJlykjhwoVl9erVZl5vK1SoYAItmwZQFy9elO3bt7u3ib5v3cbeR0zCwsLMPjwnAAAAAHA82NIs0aBBgyRnzpzuZXpfM1PxqUY4ZcoUE6jpvqI7fvy4yUzlyJHDa7kGVrrO3sYz0LLX2+ti20YDqGvXrsV4XHo82mzRngoVKhTn1wQAAAAACQ62NFA5derULct12aVLl+K0D82EvfHGG6YfVcaMGZPVp9G3b1/TjNGe9FgBAAAAwPFgq2XLlvLCCy/IjBkzTFNCnX788Ufp3LmzKWARF9pM8OTJk6aPV2BgoJmWLVsmI0eONPc1+xQeHi7nz5/3epxWI9SCGEpvo1cntOfvtE22bNkkKCgoxmPTqoW63nMCAAAAAMeDLS060aRJE3nuueekSJEiZtL7jRs3li+++CJO+6hfv76EhobK5s2b3VPVqlVNsQz7vlYV1OqBtl27dplS7zVq1DDzeqv70KDNtnDhQhMclStXzr2N5z7sbex9AAAAAECyqUaoY1ppUDVkyBD5+++/zbISJUpI5syZ47yPrFmzyn333ee1TB+vY2rZyzVTpmXmc+XKZQKo1157zQRJWolQNWzY0ARV7du3l8GDB5v+Wf369TNFNzQ7pbTk++effy59+vSRTp06maId06ZNMyXlAQAAACBZDmqs41rpVKpUKRMoWZaVeEcmYsqza3VDHcxYqx9qk0BtumjTUvOzZ882txqEtWvXzoyz9f7777u30bLvGlhpNqtixYqmgMc333xD2XcAAAAAyS+zdebMGXn66adNeXcdS2vPnj1SvHhxk4nSqoTxqUjoaenSpV7zWjhj9OjRZrodbcI4d+7cWPdbt25dMy4YAAAAACTrzFbPnj1NfyrtP6VNCm3PPPOMzJs3LzGPDwAAAAD8J7O1YMECmT9/vhQsWNBruTYnPHDgQGIdGwAAAAD4V2brypUrXhkt29mzZ92FKQAAAADAnyUo2KpTp45MnDjRPa/9tqKiokxFwHr16iXm8QEAAACA/zQj1KBKx8lav369GXhYy6pv377dZLZWrlyZ+EcJAAAAAP6Q2dJxsHbv3i21a9eW5s2bm2aFrVq1MhX/dLwtAAAAAPB38c5s3bhxQxo3bixjxoyR//znP84cFQAAAAD4W2ZLS75v3brVmaMBAAAAAH9uRtiuXTv59ttvE/9oAAAAAMCfC2RERETId999J7/99ptUqVJFMmfO7LV+2LBhiXV8AAAAAJD6g629e/dK0aJFZdu2bVK5cmWzTAtleNIy8AAAAADg7+IVbJUqVUqOHTsmS5YsMfPPPPOMjBw5UvLmzevU8QEAAABA6u+zZVmW1/yvv/5qyr4DAAAAABKhQMbtgi8AAAAAQAKCLe2PFb1PFn20AAAAAOAu+2xpJuv555+XDBkymPnr16/Lyy+/fEs1whkzZsRntwAAAADg38FWx44dbxlvCwAAAABwl8HWuHHj4rM5AAAAAPituyqQAQAAAACIGcEWAAAAADiAYAsAAAAAHECwBQAAAAAOINgCAAAAAAcQbAEAAACAAwi2AAAAAMABBFsAAAAA4ACCLQAAAABwAMEWAAAAADiAYAsAAAAAHECwBQAAAAAOINgCAAAAAAcQbAEAAACAAwi2AAAAACC1BVtffvml3H///ZItWzYz1ahRQ3799Vf3+uvXr0v37t0ld+7ckiVLFmndurWcOHHCax8HDx6UZs2aSaZMmSQkJER69+4tERERXtssXbpUKleuLBkyZJCSJUvK+PHjk+w1AgAAAPBPPg22ChYsKB9//LFs2LBB1q9fL48++qg0b95ctm/fbtb37NlTfvnlF5k+fbosW7ZMjh49Kq1atXI/PjIy0gRa4eHhsmrVKpkwYYIJpPr37+/eZt++fWabevXqyebNm6VHjx7y4osvyvz5833ymgEAAAD4hwDLsixJRnLlyiVDhgyRJ598UvLkySOTJ08299Wff/4pZcuWldWrV0v16tVNFuzxxx83QVjevHnNNmPGjJG33npLTp06JenTpzf358yZI9u2bXM/R5s2beT8+fMyb968OB3TxYsXJXv27HLhwgWTgQPUxo0bpUqVKtK16wbJn79ynB937NhGGTu2ivmRQTOuAAAASDniExskmz5bmqWaMmWKXLlyxTQn1AvRGzduSIMGDdzblClTRgoXLmyCLaW3FSpUcAdaqlGjRuYNsLNjuo3nPuxt7H3EJCwszOzDcwIAAACA+PB5sBUaGmr6Y2l/qpdffllmzpwp5cqVk+PHj5vMVI4cOby218BK1ym99Qy07PX2uti20QDq2rVrMR7ToEGDTLRqT4UKFUrU1wwAAAAg9fN5sFW6dGnTl2rNmjXSrVs36dixo+zYscOnx9S3b1+TFrSnQ4cO+fR4AAAAAKQ8gb4+AM1eaYVApf1f1q1bJyNGjJBnnnnGFL7QvlWe2S2tRpgvXz5zX2/Xrl3rtT+7WqHnNtErGOq8tq8MCgqK8Zg0y6YTAAAAAKTYzFZ0UVFRps+UBl7p0qWTRYsWudft2rXLlHrXPl1Kb7UZ4smTJ93bLFy40ARS2hTR3sZzH/Y29j4AAAAAINVltrS5XpMmTUzRi0uXLpnKgzomlpZl175SnTt3ll69epkKhRpAvfbaayZI0kqEqmHDhiaoat++vQwePNj0z+rXr58Zm8vOTGk/sM8//1z69OkjnTp1ksWLF8u0adNMhUIAAAAASJXBlmakOnToIMeOHTPBlQ5wrIHWY489ZtYPHz5c0qRJYwYz1myXVhH84osv3I9PmzatzJ492/T10iAsc+bMps/X+++/796mWLFiJrDSMbu0eaKO7fXNN9+YfQEAAABAqgy2vv3221jXZ8yYUUaPHm2m2ylSpIjMnTs31v3UrVtXNm3alODjBAAAAIAU32cLAAAAAFIDgi0AAAAAcADBFgAAAAA4gGALAAAAABxAsAUAAAAADiDYAgAAAAAHEGwBAAAAgAMItgAAAADAAQRbAAAAAOAAgi0AAAAAcADBFgAAAAA4gGALAAAAABxAsAUAAAAADiDYAgAAAAAHEGwBAAAAgAMItgAAAADAAQRbAAAAAOAAgi0AAAAAcADBFgAAAAA4gGALAAAAABxAsAUAAAAADiDYAgAAAAAHEGwBAAAAgAMItgAAAADAAQRbAAAAAOAAgi0AAAAAcADBFgAAAAA4gGALAAAAABxAsAUAAAAADiDYAgAAAAAHEGwBAAAAgAMItgAAAADAAQRbAAAAAOAAgi0AAAAASG3B1qBBg+TBBx+UrFmzSkhIiLRo0UJ27drltc3169ele/fukjt3bsmSJYu0bt1aTpw44bXNwYMHpVmzZpIpUyazn969e0tERITXNkuXLpXKlStLhgwZpGTJkjJ+/PgkeY0AAAAA/JNPg61ly5aZQOqPP/6QhQsXyo0bN6Rhw4Zy5coV9zY9e/aUX375RaZPn262P3r0qLRq1cq9PjIy0gRa4eHhsmrVKpkwYYIJpPr37+/eZt++fWabevXqyebNm6VHjx7y4osvyvz585P8NQMAAADwD4G+fPJ58+Z5zWuQpJmpDRs2yMMPPywXLlyQb7/9ViZPniyPPvqo2WbcuHFStmxZE6BVr15dFixYIDt27JDffvtN8ubNK5UqVZKBAwfKW2+9JQMGDJD06dPLmDFjpFixYjJ06FCzD338ihUrZPjw4dKoUSOfvHYAAAAAqVuy6rOlwZXKlSuXudWgS7NdDRo0cG9TpkwZKVy4sKxevdrM622FChVMoGXTAOrixYuyfft29zae+7C3sfcRXVhYmHm85wQAAAAAKTLYioqKMs37atWqJffdd59Zdvz4cZOZypEjh9e2GljpOnsbz0DLXm+vi20bDaKuXbsWY1+y7Nmzu6dChQol8qsFAAAAkNolm2BL+25t27ZNpkyZ4utDkb59+5osmz0dOnTI14cEAAAAIIXxaZ8t26uvviqzZ8+W5cuXS8GCBd3L8+XLZwpfnD9/3iu7pdUIdZ29zdq1a732Z1cr9NwmegVDnc+WLZsEBQXdcjxasVAnAAAAAEiRmS3LskygNXPmTFm8eLEpYuGpSpUqki5dOlm0aJF7mZaG11LvNWrUMPN6GxoaKidPnnRvo5UNNZAqV66cexvPfdjb2PsAAAAAgFSV2dKmg1pp8OeffzZjbdl9rLSflGac9LZz587Sq1cvUzRDA6jXXnvNBElaiVBpqXgNqtq3by+DBw82++jXr5/Zt52devnll+Xzzz+XPn36SKdOnUxgN23aNJkzZ44vXz4AAACAVMynma0vv/zS9ImqW7eu5M+f3z1NnTrVvY2WZ3/88cfNYMZaDl6bBM6YMcO9Pm3atKYJot5qENauXTvp0KGDvP/+++5tNGOmgZVmsypWrGhKwH/zzTeUfQcAAACQOjNb2ozwTjJmzCijR4820+0UKVJE5s6dG+t+NKDbtGlTgo4TAAAAAFJsNUIAAAAASE0ItgAAAADAAQRbAAAAAOAAgi0AAAAAcADBFgAAAAA4gGALAAAAABxAsAUAAAAADiDYAgAAAAAHEGwBAAAAgAMItgAAAADAAQRbAAAAAOAAgi0AAAAAcADBFgAAAAA4gGALAAAAABxAsAUAAAAADiDYAgAAAAAHEGwBAAAAgAMCndgp4G8sS+TwYZH9+0XOnnVN586JREWJZMggkj69SKZMIgUKiGTNmlVEsvj6kAEAAOAwgi3grjwkq1YVlIMHRS5ejHmLK1f+ub93r/5bSkTOS9euV+Wtt0RatBBJly6pjhcAAABJhWALSICVK0V69NCgaY1s2+ZaptmrkiVFQkJEcuVyTWnTioSHuyYNxjQo27s3TC5dyiAbNmSVp592ZbteflnktddEcuTw9SsDAABAYgmwLG0AhdhcvHhRsmfPLhcuXJBs2bL5+nDgQ5s3i8lGLVhgLwmXkiUvS9WquaRECZHAOPx8cezYRhk7tqW8+OIfMmtWfjl50rU8Tx6RQYNEXnhBJA29KQEAAFJ8bMAlHRAHN26IDBggUrWqK9DSoKply9MiUlIefXS/lC4dt0DrHwelW7djJtM1aZJImTIip06JvPiiSLVqImvWOPdaAAAAkDQItoA7CA11BUDvvScSGSnSqpXIrl0i/fodFJFDd7VvLZ7Rtq3Ili0iQ4dq8QyR9etFatYUGTjQ9XwAAABImQi2gFh8840rm7Vpk6sP1g8/iPzvfyLFiyfu82h/r169RHbvFnnuOVcVw/79RRo0EDlyJHGfCwAAAEmDYAuIQUSEyOuvi3Tp4ipu8fjjItu3i7RpIxIQ4Nzz5ssn8v33IhMnimTOLLJ0qUjFiiILFzr3nAAAAHAGwRYQjY6R1bixyKhRrnltzjdrlisQSirt27uyaZUri5w5I9KkicjXXyfd8wMAAODuEWwBHrRgRY0aIosWuTJLM2Zo3yxns1m3U6qUyKpVrsBL+2517SrSp4+riSEAAACSP4It4CbtL1W7tuu2cGGR1au14qBvj0kLaEyY4CrOoYYMETM21/Xrvj0uAAAA3BnBFiAiW7eK1KkjcuiQyL33iqxYIVKhgiQLmlXTYhnal0sLafz4o8gTT4hcueLrIwMAAEBsCLbg99atE3nkETGDC1eqJPL77yKFCkmyo1UK5893NW/87TeRRo1ELlzw9VEBAADgdgi24Nd0fCsNWs6fd/XVWrJEJCREkq26dV2BVvbsIitXitSv7yqgAQAAgOSHYAt+a8cO1zhW5865Ai3NGuXIIcle9equoDA4WGTDBpHHHnMFiwAAAEheCLbgl/bscWWFTp8WqVJF5NdfRbJmlRTjgQdEli93ZeG0RLyWhr90yddHBQAAAE8EW/A7hw+7Aq3jx0Xuv9+V0dJmeSlN2bKuwY5z5hT54w+Rf/1L5No1Xx8VAAAAkkWwtXz5cnniiSekQIECEhAQID/99JPXesuypH///pI/f34JCgqSBg0ayB5NSXg4e/astG3bVrJlyyY5cuSQzp07y+XLl7222bp1q9SpU0cyZswohQoVksGDByfJ60PyHLBY+2hp1cHSpV3BSu7ckmLZwaJm5ZYuFWnVSiQszNdHBQAAAJ8HW1euXJGKFSvK6NGjY1yvQdHIkSNlzJgxsmbNGsmcObM0atRIrnsMMqSB1vbt22XhwoUye/ZsE8B11dFfb7p48aI0bNhQihQpIhs2bJAhQ4bIgAEDZOzYsUnyGpF8XL3qKpmufbXuuUdkwYLkXQwjrh58UGTuXJFMmUTmzRNp00bkxg1fHxUAAAACffnkTZo0MVNMNKv12WefSb9+/aR58+Zm2cSJEyVv3rwmA9amTRvZuXOnzJs3T9atWydVq1Y124waNUqaNm0qn376qcmYff/99xIeHi7fffedpE+fXsqXLy+bN2+WYcOGeQVlSN00+HjmGZFVq1xFMDQbpAMXpxY6GPPPP4s8/riIJog7dhT5739F0qb19ZEBAAD4r2TbZ2vfvn1y/Phx03TQlj17dqlWrZqsXr3azOutNh20Ay2l26dJk8ZkwuxtHn74YRNo2TQ7tmvXLjmnZehiEBYWZjJinhNSLssS6dZNZPZskYwZXbfly0uqo38q//ufSGCgyA8/iLz0kkhUlK+PCgAAwH8l22BLAy2lmSxPOm+v09uQaO3AAgMDJVeuXF7bxLQPz+eIbtCgQSawsyft54WU68MPRb79ViRNGpFp00Rq1ZJUSzNbkye7Xqu+5p49XcEmAAAAkl6yDbZ8qW/fvnLhwgX3dEirKSBFmjRJ5J13XPc//9zVZyu1e+opkXHjXPdHjhQZMMDXRwQAAOCfkm2wlS9fPnN74sQJr+U6b6/T25MnT3qtj4iIMBUKPbeJaR+ezxFdhgwZTHVDzwkpz+LFIp06ue736eNqSugvOnRwBZfq/fdFhg3z9REBAAD4n2QbbBUrVswEQ4sWLXIv075T2herRo0aZl5vz58/b6oM2hYvXixRUVGmb5e9jVYovOFRnk0rF5YuXVpy6gBFSJW2bXOVQbcLYwwaJH6ne3dXE0r15puuZoUAAADwk2BLx8PSyoA62UUx9P7BgwfNuFs9evSQDz74QGbNmiWhoaHSoUMHU2GwRYsWZvuyZctK48aNpUuXLrJ27VpZuXKlvPrqq6ZSoW6nnnvuOVMcQ8ff0hLxU6dOlREjRkivXr18+dLhoKNHRZo2FblwwVWlb/x4Vx8mf9S3r0jv3q77Wnxz+nRfHxEAAID/8Gnp9/Xr10u9evXc83YA1LFjRxk/frz06dPHjMWlJdo1g1W7dm1T6l0HJ7ZpaXcNsOrXr2+qELZu3dqMzWXTAhcLFiyQ7t27S5UqVSQ4ONgMlEzZ99Tp0iWRZs3+GbRYy6F7nC5+JyBA5JNPRM6fF/n6ax2XTiRLFh12wddHBgAAkPoFWDqgFWKlzRc1aNNiGfTfSr4iIlwFMHRgXy1SqSMEFC/u3PNt3LjRBPBdu26Q/Pkrx/lxx45tlLFjq5jmr5Urx/1xdyMy0hVoTZ0qEhTkGmesTp0keWoAAAC/jQ38tHEVUhv9yeDll12BlgYTOpaWk4FWSqODG+sgx5r1u3bNVSJ+40ZfHxUAAEDqRrCFVEHLm9tjaU2ZIvLgg74+ouQnXTpXn62HH9ZfZHRwb5E///T1UQEAAKRePu2zBSSGr75ylTdXX3wh8q9/+fqIki/N+v3yi8ijj4poEc8GDURWrBApWtTXRwYAAHxFi9OdPn063o/TWgiFCxd25JhSC4ItpGizZom88orrvg5e/NJLvj6i5E+bFmtzS81w7dwp8thjIr//ruPO+frIAACALwKtMmXKyrVrV+P92KCgTPLnnzsJuGJBsIUUSwtgtGkjEhUl0rmzyHvv+fqIUo7gYB1vzlUk46+/XAHX0qUiuXP7+sgAAEBS0oyWBlotW06SPHnKxvlxp07tlJkz25nHE2zdHsEWUiTta6RFHrTYgxZ9GDPGVeYccXfPPSK//eYai0wHgdYmhTpPwAUAgP/RQCs+1ZURNxTIQIoctLhxY5GzZ0UeeshVzjyQnw0SRCs2aoClpfJ1bHENuM6c8fVRAQAApA4EW0hRtIpe06YiBw6IlCrlKvGeObOvjyplK1dOZMkSkbx5XQFX/frapMDXRwUAAJDyEWwhxbh61VVpcMsWV2CgA/PmyePro0p9AZe+v1qt8PhxXx8VAABAykawhRQhLEykdWuRZctc1fTmzhUpVszXR5W6lC3rCri0KmFoqKsv1/79vj4qAACAlItgC8leRITIs8+6ypVnyiQyZ45IZfpvOhZw6bhbGsj+/bdIrVoiO3b4+qgAAABSJsoKIFmLjBR5/nmRmTNFMmQQ+flnV8YFzilRwjXuVsOGrkBLx+PSALdaNV8fGQAASOzBiXfqoJtwDMEWknVGq2NHkcmTXdUGp093VctD0pSFX75cpEkTkXXrROrWFfn+e5FWrXx9ZAAAILEHJ1aXL19K0OMSEqwFBwf7zdhcBFtIlm7cEGnXTmTaNFeg9cMPIk884euj8i863tbixa6BozWz9eSTIkOGiPTqxZhmAACklsGJ9+yZK0uWvCPXr1+P1/NdvnxMRAKknV6wxVNQUCb588+dfhFwEWwh2QkPd13ga9PBdOlcGa3mzX19VP4pSxaRn34S6dFDZPRokX//W/9TFhk5UiR9el8fHQAAuNvBiU+fTlgzwuvXz4uIJfXqfS6lStWI8+NOndopM2e2M8EhwRaQxK5cEXnqKZFff3X10ZoxwzWuFnxHM4ujRomULOnKan31lcjWra4gWJsbAgAA/5UzZ8l4BXf+hmqESDa0P6eO76SBVlCQyKxZBFrJhTYb1OzWL7+I5Mghsnq1qyKkluIHAABAzAi2kCzs2+cqM752rUiuXCKLFrmq4SF5adZMZP16kfvvFzl5UqR+fZGPPnJVjQQAAIA3gi34nFa7q1lTZPduEW26u3KlSI24N/2FD0rDa2ZL+8NqkPWf/4g88ogrYAYAAMmHZYmEhYlcvChy9qz2lxI5dkzk6FHX7YkTui67iBSV69fTmwJl+hgkHvpswacmTRJ58UXXfwQVKrgGLi5QwNdHhTvRwaUnThR57DGRV191BcgVK4qMGOEaF41qhQAAODs8zoEDWu7dFTjptHmzdqSeIrNm3Wuuq65edV1f3dnjZpo7V8yk3+H6PZ85s+s2WzbXpN0IdAoOds0jbgi24BOaEXn7bZFPP3XNa1l3Dbz440059D/jDh1E6tQRad/eFXB16iTy3/+KfPmlSOnSvj5CAABSNs086TBWWglYWwDZ099/u4bJ8ZZXRJ6R48dv3U+aNK4Kz2nTuib9DtcMVlSU7ue6hIdHaA1is60u14JlOt2OViTOnLmRiHwje/YUMX3tQ0JEsmblB9foCLaQ5DRtrRfn2i9L9esn8t57rv8IkPIUK+YqlDF0qMiAASJLlrj6dGkwrZP+BwwAAG5Pgx4NoDZt0gyVyKpV12TbtkA5cybdbR+TIUOU5MsXLiEh4ZInzw1Jk+a4zJ49Vho0eEMKFSpuMlNa2TljRldl4dsJDf1RZsxoJy1bzpMyZRqJDrelWTE74NImiBcu/NMUUScdpic8PFhEOktoqO7jnyFjChVyVSvW2/z5XUGePyPYQpLS9HTHjq7Kg5qaHj/eVeodKZv+Stanj+uz7N7dVVHy/fdFxo1z3WpwrdsAAODvtGnftm2uoMoOrrZs0UGCPbeyf6mM0jJiIrJLRHZ7TWFhh+XAAcs0J/QUEtJKChcuHu/j0oyUZqx0iq2lkbZOOnNGZMOG32Xt2kVyzz0vS3h4PnNtp69BM3E6qTRpRPLlEylYUKRoUdfkbwi2kCSuXRPp29fVp0dp/54pU0TKlPH1kSGxs1xz5oj873+uMbkOHRJ54QVX1kurFj7+OM0LAAD+IzT0kMlS7d6dSXbtCpJduzLJvn0ZJTIyIMZMVcmS1yQk5KgsWTJMHnmkuRQvnk/SpdOAK9/N6eHbPteePXNlyZJ35LqmphykP55qk8GCBQ/K2rXvSbVqNaRChXymWaP2HTt8+J/p8uV/+pRpxWkVHKwXf5/IqlXZTJcDzcClZgRbcJw2K+vaVeSvv1zzb7wh8vHHrrQ2Uh8NpjTDpYHV6NGuIEt/wfvXv1xFUDQD9swzNCsAAKQe2s9Jf2D0zFatXx8hhw8Xus0jzojIppvTZnMbFrZbtm+PlO3bXVvcc08bKVy4UpyP4fTpm+kkH9Hv9SJFXJP9nly44Aq6NPu2f7+rZdPp05lEpI+89prrh9lq1VwFtxo1EqlaNfW1hCHYgmO0TW/v3iLffeea1yqDY8e6xmpC6qd9tf79b5HOnUU++cQVeGmbbm1SqP30XnnFVblQfx0DACCl0L5L+iOifqdt3eqa9L4GFjFdZmfKdElCQiIlOPiq5M59TXLnviqZM9+QgIBcIlL/5pT0Gaqk+PE1x80Khvfd51p26ZIGo/tkyZLFkj9/Ozl2LIOsWCFmevdd11irduClU2qoUE2whUSnnSbHjHH11dE2vapbN5FBg0Sy61AO8Cs5c7oymW+95apSqE1J9Rcundegq3lzV0CmAyST7QIAJKe+VVq0QjNNnkHV7caV1CIU5cuLVKok8sADGmTtlq5dH5J27TSwqCwiOVJEhspJWbOKlCp1TpYseVFmz35AcuSoLL/9JrJggZhb/aF+6lTXpDRIa9zYFXjVrp0yW0URbCHRaLp4xgxXBTq7yWC5ciJffeX6A4G3nXbv0XgIDg6WwjrycwoNuv7v/0R69hSZPNmV5dT229q/S6fcuUVatBB5+mmRevUIvACkHgcPHpTT2n7Kj/7PT0lVALVKspZW37XLe9KgStfHRDMuWnnXc9L+R1pcwrZxo1a8uCXdBQ/Fi7u6muikY4etWSMyf75rWrfOlUHUSYcK0hYzdeu6Klg/+KCkGARbuGv6xzFtmit7YZf+zJvX9cegGYvYyo36o8uXj2lyXdq1axfvxwYFZZI//9yZor989T9LPS900l8Jv/7a9QuWjmr/7beuSUvHPvqo65csbU5QsiSFNQCk3ECrTJmycu3a1Xg/NkOGjPLjj/+T/Fo/Ox4I0rxb2+i4U1qgwe43pEGUTnpfl8U28K9+H5Ut6x1Uaf9j/YEQiSswUKRWLdekraP09wnNdtnBlwbFWu34gw8kReEyGAl2/rxrIOLhw0X27v3nPyXNXGhfLU0V41bXr5/XPKDUq/e5lCpVI86PO3Vqp8yc2U5+//13Kav/88dTcvzy1S+tUaNc59Dy5SLTp7uyoydPisya5ZqU9uuqWdM16a9ZWs1SM2UAkNxpRksDrZYtJ0mePHH/v/vAgd9l/vye8rhWG/LDH+Zuxx5w11VowdVdwb6vkx1Y6XTkiOuHvDvRggxa1EEzU9EnjXP1xz7P7KQGaNHLrSdWCxb8IzhYpE0b16Sfu/6gv3ixq5lmSkKwhXjRk3316n+yEVrS3f6D0CqDOsYSF8FxkzNnyZttuJ3PiCX3L1/9NUszWTppIQ2t4qTtt/WXrFWrXMHXTz+5Jpu+DA3WNOtVooRr0uYIOoaHDuIIAL74jtRMig4Ia09//aWdTCqJZVWRGzfKmDGKtGlaTLee948etbR+r5Qs2VqCg+M+ONHlyydk27ZJMnJkgPl/Usc50mBBb5P6vtJy4Drp+2LfxnRfrye0eII9acnw283r9vERGBglwcE3pGBBS8qUyWiGKdHvCr3VSceAiq0Vzt1kJ12fyaUEPQ7/0PPKziymNARbiNOXh7abtfvWeHYM1Y6gL78s0qmTa5BiJL+M2N1mxZI6I6Zf0JUruybt/6fFmDZudAVdOmlJXW36cfCga4rpP2QdtV6/QHUgRW3SqpkxzylPHld1JB200bN9PYDUS4MYvaDXrIgdCNn3PZclZNL92vdv7eNTzpT19vyxKG6qmEn7QNv9oOOmoHmcjm+YummlvtPRJq3KdVxEjnpNERFn5PhxSy5cyCQ//hj/Hx0Tmp1MLVUFcXcIthAjTbtrO9mFC12TjpHg2edGixhoZ8YaNehLk9wzYnebFUton4GwsDDJkIAUU/TgTisP2U0IPZuwan8v7TSrTVi1WpROel8vmm4XiMVEB5HMnDny5uS6nzFjlFmePr3lvk2fPsost+/nypVJ8ufPZX4NvdOkTVRiWx/9F+GYfiU+cuSwnDunFxKWx6/GlnubgADL6/H232VybD4K/6Y/4GkfmbhOep0a07LYAqaYltktMZKKZlMyZLAkbdobcvHiGcmSJVjSp89g/kb1/wSd7PsxLbtwYZ8cOrRSChV6VHLkcNW/jsv37cWLh2X//qX605X5f991G7f7adIESvXqNSV9+ozmc9LAUac73Q8LC5eIiMiby10Haa/X/+N0UN7AQEvSpXNNel+nNGkiJUOGALNM/1/NlEkn1//FQUGu2+jzR4/ukjfeaCctWoyVkBA78Ml1c3LmR0e7OaAGWvH5/k3NVQURdwRbMF9c+v+IZq+0ieAff7jmPeno3tps/MknRZo0Sf2jfac2Cc2K3U2fAdcXuDaDcSa40/6B1au7Jju40wuZs2cD5ciRDHLkSHo5dy5Qzp5NZ5bp/TNn0plbna5dc42aGBaWxky6XfJW8OYUH5HmM9CLvn8CsX+CMpUmjWte/XPr+tzsoM3zAs9+fEyPse+nTZtW0qUL9Nom+mNiWhYRES6R2n7KvU1Mx3br8VmWvr408XqMSpcunQQFZYzTscV1PrZtrl27IuHhrp740beL/h7a61wXs5GSJk1ac9+yXBu47v+zbUzr9O8hU6ZM7nl7+9vNx7bOvqjWgkj6Eemt5329vX79hoSFRd1sBhdwc3Ld1wvwiAjXfETEzZPPhzJk0B9VLHMBr7f6Q4pO0ec9p/Dw8zJhwhiJiND/T6/eYbomERER5r2x/etfS6VUqUfifIyhoavk0KH28uCD86RChbgPNhQaukz272+f4H7Br7wyKV7ByLFjx+TJJ5+S69evJdn3hMqUqWCSNsV37YPmgIlpp59UZfarYGv06NEyZMgQOX78uFSsWFFGjRolDz30kPgD/YLTbJU2AbSbJGhZU+1sqLc3r2+8aLvYhg1d1eDq1HFltOBfWTHXr3LxD9LsphPJO7jTYEuruGSTWrWGSu7cFSQ8PK2ZXBeJacxFoX3fc9nFi6fk6FEtvZnx5n4CPW5jm3SbdJIzZ7BYlut59AI0+i/BetHsuri927SxK6D0vOBL3lJ7m079lSo1/1KV0B8swhIwabOsKyaoEbnsEeRciXYb07JrNzNk2qws/kfbuPFYKVxYm/gl76ZkSd0vOKHvS0K/X+L7ft5NU3yaAyauy35Wldlvgq2pU6dKr169ZMyYMVKtWjX57LPPpFGjRrJr1y4J0U4cKYgGRjp6uX5J2JM9r82rTpxwlcf0nLTAQEwBlU37r2gfGW0WqJmCatVc/VqAhAdpyT+4sx+XN29WqVChdJwfFxo6X2bMaBeH59POG+E3p3+CyXPn4vtLrquZT5s2i6VEiYdvyTjElIXQaefOH2Xu3NekZs2PpGhR10WQHchFv2/Pu45zuaxdO0oefLCfFClS0b3cfg5Prvl/9nPs2GZZs+azaMduT9Hnb1324INvSkjIvXd8HtuRI+slNHSSVKjQTfLnLxOnx+j86dN/ypYtEz2e/87H5vlZxPz6YnuMaypb9lnJmbOQ12dw6zH/s+7MmZ3y99/zpESJlpInTxGvbT2zddGdO/eX7Nz5v5vnoNmbxxTbvC0qhnWRN6cIj1vP+5FSrdq/JX/+0u6MqetWm4vZGVFtVqdTlLn9++85snRpbH+76W9OWRP97z6hj8uUqXCqbEqW0GDkbt+XhH6/JGVT/JTyGfpLVebTp08TbCVHw4YNky5dusgLL7xg5jXomjNnjnz33XfytvbCTyG0TPbrryfssfollz9/pJQuHWgquOmkY0XodM89sbcFT+iAjInVbyeuGDgydUja4C7hkjqYDA+/Fq9x6zJm1F9hj0m+fPmlVKkKcX5cZOQmWbt2hxQqlEvKl9fO/XGXJs0GEVmT4NdYqNBbUqFCqTg/LjR0jYSGzpFSpV6TChVKxONxq2XLlqlJfqFetuzjUqFCoXgc51L5+++hUrHiY1KhQtybkYaGLpedO79I8td3zz39pEIF76A3NufO7bjrC+CU8v9FSsH7gqSSMwF/9ymRXwRb4eHhsmHDBunbt697WZo0aaRBgwayWjspxRAg6GS7cLOdwUVNH/mYd0CkzSf0mLQN8YWb93XSQSVO3KzIc9x937JOyZkz6aVbt4mSV0u03aRFBexxsmJy4sQJad++g4SFJV36XPvt/Pe/3sd5J3dznAl5Ps2KqqNHN0h4uDZjifsvM67bUDlwIMjxx/niOXlc7I+LiLgWr3MmIuJ6inh9KfE1JvQ4eX0xP47/13hcantcSjrW1P6406dd112XL1/2+TW5/fxW9KYTMQiw4rJVCnf06FG55557ZNWqVVJD28nd1KdPH1m2bJmsWbPGa/sBAwbIe++954MjBQAAAJASHDp0SArqQG3+ntmKL82Aaf8uW1RUlJw9e1Zy584tATG0tdPotlChQuYNz6YD98DvcU4gOs4JxITzAtFxTiA6zonkR3NVly5dkgIF7lwp1C+CLe2Po6WItZmZJ53Pp6OeRqN9jKL3M8qhFSTuQP8A+COAJ84JRMc5gZhwXiA6zglExzmRvGTPnj1O2/l+sIskkD59eqlSpYosWrTIK1ul857NCgEAAAAgsfhFZktps8COHTtK1apVzdhaWvr9ypUr7uqEAAAAAJCY/CbYeuaZZ+TUqVPSv39/M6hxpUqVZN68efGqPnc72uTw3XffTVCJc6ROnBOIjnMCMeG8QHScE4iOcyJl84tqhAAAAACQ1PyizxYAAAAAJDWCLQAAAABwAMEWAAAAADiAYAsAAAAAHECwdRv79++Xzp07S7FixSQoKEhKlChhKsGEh4d7bbd161apU6eOZMyY0YzuPXjw4Fv2NX36dClTpozZpkKFCjJ37lyv9VqjRKsk5s+f3zxXgwYNZM+ePY6/Rjhn9OjRUrRoUfOZV6tWTdauXevrQ0IiGDRokDz44IOSNWtWCQkJkRYtWsiuXbu8trl+/bp0795dcufOLVmyZJHWrVvfMqD6wYMHpVmzZpIpUyazn969e0tERITXNkuXLpXKlSub6lMlS5aU8ePHJ8lrxN35+OOPJSAgQHr06OFexjnhf44cOSLt2rUzn7l+r+t3//r16+P1vX/27Flp27atGcQ2R44c5prk8uXL8b4GQfIQGRkp77zzjtd15cCBA825YOO8SKW0GiFu9euvv1rPP/+8NX/+fOvvv/+2fv75ZyskJMR688033dtcuHDByps3r9W2bVtr27Zt1g8//GAFBQVZX331lXublStXWmnTprUGDx5s7dixw+rXr5+VLl06KzQ01L3Nxx9/bGXPnt366aefrC1btlj/+te/rGLFilnXrl1L8teNuzdlyhQrffr01nfffWdt377d6tKli5UjRw7rxIkTvj403KVGjRpZ48aNM3/vmzdvtpo2bWoVLlzYunz5snubl19+2SpUqJC1aNEia/369Vb16tWtmjVrutdHRERY9913n9WgQQNr06ZN1ty5c63g4GCrb9++7m327t1rZcqUyerVq5f5f2PUqFHm/5F58+Yl+WtG3K1du9YqWrSodf/991tvvPGGeznnhH85e/asVaRIEXMNsWbNGvPZ6bXEX3/9Fa/v/caNG1sVK1a0/vjjD+v333+3SpYsaT377LPxugZB8vHhhx9auXPntmbPnm3t27fPmj59upUlSxZrxIgR7m04L1Ingq140IBJT3rbF198YeXMmdMKCwtzL3vrrbes0qVLu+effvppq1mzZl77qVatmvXSSy+Z+1FRUVa+fPmsIUOGuNefP3/eypAhg/kDQcrz0EMPWd27d3fPR0ZGWgUKFLAGDRrk0+NC4jt58qT+JGktW7bM/berP6bol6ht586dZpvVq1ebeb2QTpMmjXX8+HH3Nl9++aWVLVs29/8lffr0scqXL+/1XM8884wJ9pA8Xbp0ySpVqpS1cOFC65FHHnEHW5wT/kevA2rXrn3b9XH53teAWs+RdevWef0IHBAQYB05ciTO1yBIPvRasFOnTl7LWrVqZYIixXmRetGMMB4uXLgguXLlcs+vXr1aHn74YUmfPr17WaNGjUyzonPnzrm30TSwJ91Gl6t9+/aZQZY9t8mePbtpemZvg5RDm5lu2LDB6/NMkyaNmefzTJ3/Jyj7/wX97G/cuOH1+WsT4sKFC7s/f73VJkWeA6rr/wkXL16U7du3x+n/DSQ/2kxQmwFG/9w4J/zPrFmzpGrVqvLUU0+ZJqEPPPCAfP311+71cfne11ttIqb7sen2+n2yZs2aOF+DIPmoWbOmLFq0SHbv3m3mt2zZIitWrJAmTZqYec6L1ItgK47++usvGTVqlLz00kvuZfpH4fnlqOx5XRfbNp7rPR8X0zZIOU6fPm3aZfN5pn5RUVGmX06tWrXkvvvuM8v0M9YvOP0yjO1vPqH/b+jF97Vr1xx9XYi/KVOmyMaNG02fvug4J/zP3r175csvv5RSpUrJ/PnzpVu3bvL666/LhAkT4vy9r7caqHkKDAw0P+zE57xB8vH2229LmzZtzI8t6dKlM0G4fodo/yvFeZF6pfHHk107L8c2/fnnn7d0dG3cuLH5lapLly4+O3YAySuTsW3bNnOhDf916NAheeONN+T77783ndEB/SFGC5l89NFH5oK6a9eu5tphzJgxvj40+NC0adPM/xOTJ082P85o8P3pp5+6g3CkXn4XbL355puyc+fOWKfixYu7tz969KjUq1fPpH/Hjh3rta98+fLdUlHKntd1sW3jud7zcTFtg5QjODhY0qZNy+eZyr366qsye/ZsWbJkiRQsWNC9XD9jbUp6/vz5WP/mE/r/hlaf0gpVSD60meDJkyfNxbX+wqzTsmXLZOTIkea+/qLMOeFftJJcuXLlvJaVLVvWVJyM6/e+3up55UmrU2oluvicN0g+tMKond3SZsPt27eXnj17ujPinBepl98FW3ny5DEp3Ngmu52rZrTq1q0rVapUkXHjxpk2sZ5q1Kghy5cvN+3xbQsXLpTSpUtLzpw53dtoG11Puo0uV1oCVE9+z220WYi2vbW3Qcqh546eL56fp/7KqfN8nimfFhXSQGvmzJmyePFi8/frST97bR7i+flrO3m9yLI/f70NDQ31+sLU/xP0otm+QLvT/xtIPurXr28+z82bN7sn7U+hTYPs+5wT/kWbFkcfEkL76RQpUiTO3/t6qwG6BvM2/T9Hv0+0D09cr0GQfFy9evWW60j9cVY/U8V5kYr5ukJHcnX48GFTTrN+/frm/rFjx9yTZ5UYLa/Zvn17U15TS35rad7opd8DAwOtTz/91FSgevfdd2Ms/a6lwbW8/NatW63mzZtT+j0F0/NAqweNHz/eVA7q2rWr+Xw9K40hZerWrZspy7t06VKv/xOuXr3qVeZby8EvXrzYlPmuUaOGmaKX+W7YsKEpH6+lu/PkyRNjme/evXub/zdGjx5Nme8UxLMaoeKc8L8hAPR7X0t979mzx/r+++/NZzdp0qR4fe9rie8HHnjAlI9fsWKFqXbpWeI7LtcgSD46duxo3XPPPe7S7zNmzDBDPGilURvnRepEsHUbOpaOxqIxTZ50HAQt8aoX1/pHpH8o0U2bNs269957zdhLWrp3zpw5Xuu13Oc777xj/jh0Pxrg7dq1y/HXCOfoGDh6caWfuZaC1/EwkPLd7v8E/f/Cpl+Kr7zyiim9q19wLVu29PqRRu3fv99q0qSJGftEv2x1/L4bN254bbNkyRKrUqVK5hwqXry413MgZQVbnBP+55dffjEBtH6nlylTxho7dmy8v/fPnDljLqJ1LCYdBuCFF14wQwzE9xoEycPFixfN/wt6bZAxY0bzN/yf//zHq0Q750XqFKD/+Dq7BgAAAACpjd/12QIAAACApECwBQAAAAAOINgCAAAAAAcQbAEAAACAAwi2AAAAAMABBFsAAAAA4ACCLQAAAABwAMEWAAAAADiAYAsAkKyMHz9ecuTI4evDSNYWLVokZcuWlcjISDM/YMAAqVSpUqI/z9tvvy2vvfZaou8XAPwFwRYA4K6sXr1a0qZNK82aNYv3Y4sWLSqfffaZ17JnnnlGdu/eLU5aunSpBAQEuKe8efNK69atZe/evZIS9OnTR/r162fe9zu5fPmypEuXTqZMmeK1vE2bNua179+//5bP5J133jH3//3vf8uECRNSzPsCAMkNwRYA4K58++23JvuxfPlyOXr06F3vLygoSEJCQiQp7Nq1yxzz9OnTZfv27fLEE0+4s0W+duPGjRiXr1ixQv7++28THMZFlixZpGrVqibA9KTzhQoV8lq+b98+OXDggDz66KNmPjg4WBo1aiRffvnlXb0WAPBXBFsAgATTrMnUqVOlW7duJrOlTQCj++WXX+TBBx+UjBkzmov3li1bmuV169Y1F/Y9e/Z0Z5iiNyPUDJcu//PPP732OXz4cClRooR7ftu2bdKkSRMTWGiWqn379nL69Ok7Hr8Gdfnz55eHH35Y+vfvLzt27JC//vrLrNMAQ58jffr0Urp0afnvf//rfpxmfB5//HH3vGbn9DjnzZvnXlayZEn55ptv3PN6X5v+6ftQpkwZ+eKLL9zrNLukj9f38pFHHjHbfP/99zEes2aoHnvsMbPN7WgwVrx4cXn11VfFsiypV6+eV1C1c+dOuX79uvncPJfr/QwZMkiNGjXcyzQAjZ4VAwDEDcEWACDBpk2bZgIHDUbatWsn3333nbm4t82ZM8cEV02bNpVNmzaZvkYPPfSQWTdjxgwpWLCgvP/++3Ls2DEzRXfvvfearEz0wEPnn3vuOXP//PnzJhPzwAMPyPr1603Ac+LECXn66afjnVFT4eHhMnPmTHnjjTfkzTffNIHcSy+9JC+88IIsWbLEbKMBkWaY7CzYsmXLTCBpBy5HjhwxAY8GlPbxajD34YcfmkDno48+Mk31tIle9D5S+ry6jWaUYvL777+b9+R2tm7dKrVr1zbvz+eff26COA22NItnv8f6OnQbfd88gy1droGWZyCnn9fhw4dvaW4IAIgDCwCABKpZs6b12Wefmfs3btywgoODrSVLlrjX16hRw2rbtu1tH1+kSBFr+PDhXsvGjRtnZc+e3T2v60uUKOGe37Vrl0Zz1s6dO838wIEDrYYNG3rt49ChQ2Yb3TYmeoy6/ty5c2b+6NGj5rXcc889VlhYmLnfpUsXr8c89dRTVtOmTc19fVyaNGmsdevWWVFRUVauXLmsQYMGWdWqVTPrJ02aZPZl0+OfPHmy1/70uPX9Ufv27TPHY7+XsdH3ZuLEiV7L3n33XatixYrWypUrrZw5c1qffvqp1/orV65Y6dOndx+DvpbBgwebzyxz5szW3r17zfLChQtb7733ntdjL1y4YI5t6dKldzw2AIA3MlsAgATRTMnatWvl2WefNfOBgYGmuIX24bJt3rxZ6tevf1fPo4UcNKvyxx9/uLNElStXNhk1tWXLFpOR0SaE9mSv0+xSbDSzljlzZilQoIBcuXJFfvzxR9NsUDNLtWrV8tpW53W50maOFStWNFmh0NBQ85iuXbua7J02rdRMl2a/lO5Xj6Nz585ex/jBBx/ccnyxZaxs165di7EJ4cGDB03zQs2gaUbOU6ZMmUxTTjuLpcenWTf9zGrWrGmWaxEM3YdmwWLK+F29evWOxwYA8BYYbR4AgDjRoCoiIsIEKjZtQqh9frT5Wvbs2d0X6ncjX758prnb5MmTpXr16uZW+xrZNLjRfkWffPLJLY/V/lix0SZ52bJlM323smbNGq/j0mDF7uOkgVWuXLlMnyxtXqjBjB3w6PGpr7/+WqpVq+a1j+jVBDXwuxNtrnju3LlblufJk8d8Fj/88IN06tTJvC5PGkRpnzAtBKIBmwasSo9dg9WoqCgTlEU/xrNnz7r3DwCIHzJbAIB40yBr4sSJMnToUJO9sifNMtkX/Or+++83/bRuRzNCcan+17ZtWxMoaJl5zcBotsumQYMGEFqyXItSeE53Cl6KFStmimBED7Q0aFq5cqXXMp0vV66ce97ut6Wvz+6bpbf62rWwh71MC3boe6LHHf349PnjS/umaSGP6DSwnT17tsl6aX+vS5cu3RJs7dmzxwSr2l/LDvS0OIgGhxo4avZOPxNP2mdNS8eXL18+3scKAP6OYAsAEG96Ua/ZFW0ad99993lNWpLcbkr47rvvmuBDb7UJnja588xAaYCkJeO1oERs1QNbtWplggfNaGnQ4JlN6969u8m+aHPGdevWmaZ58+fPNwUtElrGvXfv3qYqolYk1ABl2LBhpqCHViG0aZCix6TvhWewpc0cNaOmxT1s7733ngwaNEhGjhxpAjF9H8aNG2f2G18aSGmQFxMNLrUoiTYP1OqMdlZNaXNBzcKNGjXK3cTRLoBx8uRJ+fnnn29pQmhn/+rUqZMoWUoA8DcEWwCAeNNgqkGDBqapYHQabGlVQK2Kp8GHjmE1a9YsqVSpkmkOqP28bFqJUPtjaXYptmZqmnnSpoKaOdMslycNvDTrpIFVw4YNpUKFCtKjRw/TrypNmoR9zbVo0UJGjBghn376qcnofPXVVyY4soMqlTNnTvNcetx2HzENwLQ5nmcwo1588UVT+l33oY/R9RrMJSSzpa9fM3naZy4m2h/s119/NU06tRy/9hlTmvHSZpgaIHq+Dg3A7OUxBVta9r1Lly7xPk4AgEiAVsnw9UEAAID4Zd4uXrxogkAnadCmfc80cNZsGQAgfshsAQCQwvznP/+RIkWKmCyakzQrptk4Ai0ASBgyWwAAAADgADJbAAAAAOAAgi0AAAAAcADBFgAAAAA4gGALAAAAABxAsAUAAAAADiDYAgAAAAAHEGwBAAAAgAMItgAAAADAAQRbAAAAACCJ7/8BtodpSrrP1BMAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Power Output Distribution\n", "plt.figure(figsize=(10, 5))\n", "sns.histplot(df[\"Plant.Active Power\"].dropna(), bins=50, kde=True, color='blue')\n", "plt.xlabel(\"Active Power (kW)\")\n", "plt.ylabel(\"Frequency\")\n", "plt.title(\"Distribution of Active Power Output\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Hourly Average Power Trend\n", "df['hour'] = df['time'].dt.hour\n", "hourly_avg = df.groupby('hour')[\"Plant.Active Power\"].mean()\n", "plt.figure(figsize=(10, 5))\n", "sns.lineplot(x=hourly_avg.index, y=hourly_avg.values, marker='o', color='green')\n", "plt.xlabel(\"Hour of the Day\")\n", "plt.ylabel(\"Average Active Power (kW)\")\n", "plt.title(\"Average Hourly Power Generation Trend\")\n", "plt.xticks(range(0, 24))\n", "plt.grid()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Result:                                                                                                             \n", "The hourly average power trend follows a natural solar generation curve, with near-zero output at night and a steady increase after sunrise. Power peaks between 10 AM – 2 PM and then gradually declines, confirming expected solar behavior. Variations in peak values may indicate weather effects, shading, or system inefficiencies. This analysis helps in identifying deviations from normal performance and optimizing solar panel efficiency"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20124\\3828835334.py:5: FutureWarning: \n", "\n", "Passing `palette` without assigning `hue` is deprecated and will be removed in v0.14.0. Assign the `x` variable to `hue` and set `legend=False` for the same effect.\n", "\n", "  sns.barplot(x=monthly_avg.index, y=monthly_avg.values, palette=\"Blues_r\")\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Monthly Average Power Trend\n", "df['month'] = df['time'].dt.month\n", "monthly_avg = df.groupby('month')[\"Plant.Active Power\"].mean()\n", "plt.figure(figsize=(10, 5))\n", "sns.barplot(x=monthly_avg.index, y=monthly_avg.values, palette=\"Blues_r\")\n", "plt.xlabel(\"Month\")\n", "plt.ylabel(\"Average Active Power (kW)\")\n", "plt.title(\"Average Monthly Power Generation Trend\")\n", "plt.xticks(range(1, 13), [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\", \"Jul\", \"Aug\", \"Sep\", \"Oct\", \"Nov\", \"Dec\"])\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Result:                                                                                                             \n", "The monthly average power trend shows higher energy generation during summer months (March–May) due to longer daylight hours and stronger sunlight. In contrast, power output decreases in winter months (June–December) because of shorter days and lower solar irradiance. This pattern aligns with seasonal variations and helps in predicting energy availability for efficient grid management and storage planning."]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\<PERSON><PERSON><PERSON><PERSON><PERSON>\\Wind_solar\\IntegrationUtilities.py:308: FutureWarning: Passing literal json to 'read_json' is deprecated and will be removed in a future version. To read from a literal string, wrap it in a 'StringIO' object.\n", "  dataResp = pd.read_json(resp, orient=\"split\")\n"]}], "source": ["category = ['Alarm']\n", "params = ['Inverter Alarm']\n", "startDate ='2024-10-01'\n", "endDate ='2024-11-11'\n", "alarmDf = m.fetchDataV2(plantName,category,params,None,startDate,endDate)"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>time</th>\n", "      <th>acknowledge</th>\n", "      <th>alarmid</th>\n", "      <th>alarmname</th>\n", "      <th>alarmtype</th>\n", "      <th>category</th>\n", "      <th>confirm</th>\n", "      <th>controllerid</th>\n", "      <th>controllername</th>\n", "      <th>lastModifiedTime</th>\n", "      <th>message</th>\n", "      <th>parameterid</th>\n", "      <th>parametername</th>\n", "      <th>raisedtime</th>\n", "      <th>raisedvalue</th>\n", "      <th>resolvedtime</th>\n", "      <th>resolvedvalue</th>\n", "      <th>severity</th>\n", "      <th>state</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-10-01T06:00:00Z+05:30</td>\n", "      <td>False</td>\n", "      <td>680104201</td>\n", "      <td>Inverter status</td>\n", "      <td>EnumAlarmType</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>680100000</td>\n", "      <td>INV-22</td>\n", "      <td>1.727790e+12</td>\n", "      <td>3 : Initializing or Generating</td>\n", "      <td>680104200</td>\n", "      <td>Inverter Status</td>\n", "      <td>1727762400000</td>\n", "      <td></td>\n", "      <td>1727808300000</td>\n", "      <td>0</td>\n", "      <td>MediumHigh</td>\n", "      <td>Inactive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-10-01T06:00:00Z+05:30</td>\n", "      <td>False</td>\n", "      <td>880104201</td>\n", "      <td>Inverter status</td>\n", "      <td>EnumAlarmType</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>880100000</td>\n", "      <td>INV-42</td>\n", "      <td>1.727790e+12</td>\n", "      <td>3 : Initializing or Generating</td>\n", "      <td>880104200</td>\n", "      <td>Inverter Status</td>\n", "      <td>1727762400000</td>\n", "      <td></td>\n", "      <td>1727808300000</td>\n", "      <td>0</td>\n", "      <td>MediumHigh</td>\n", "      <td>Inactive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-10-01T06:00:00Z+05:30</td>\n", "      <td>False</td>\n", "      <td>690104201</td>\n", "      <td>Inverter status</td>\n", "      <td>EnumAlarmType</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>690100000</td>\n", "      <td>INV-23</td>\n", "      <td>1.727790e+12</td>\n", "      <td>3 : Initializing or Generating</td>\n", "      <td>690104200</td>\n", "      <td>Inverter Status</td>\n", "      <td>1727762400000</td>\n", "      <td></td>\n", "      <td>1727808300000</td>\n", "      <td>0</td>\n", "      <td>MediumHigh</td>\n", "      <td>Inactive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-10-01T06:00:00Z+05:30</td>\n", "      <td>False</td>\n", "      <td>710104201</td>\n", "      <td>Inverter status</td>\n", "      <td>EnumAlarmType</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>710100000</td>\n", "      <td>INV-25</td>\n", "      <td>1.727790e+12</td>\n", "      <td>3 : Initializing or Generating</td>\n", "      <td>710104200</td>\n", "      <td>Inverter Status</td>\n", "      <td>1727762400000</td>\n", "      <td></td>\n", "      <td>1727808300000</td>\n", "      <td>0</td>\n", "      <td>MediumHigh</td>\n", "      <td>Inactive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-10-01T06:00:00Z+05:30</td>\n", "      <td>False</td>\n", "      <td>750104201</td>\n", "      <td>Inverter status</td>\n", "      <td>EnumAlarmType</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>750100000</td>\n", "      <td>INV-29</td>\n", "      <td>1.727790e+12</td>\n", "      <td>3 : Initializing or Generating</td>\n", "      <td>750104200</td>\n", "      <td>Inverter Status</td>\n", "      <td>1727762400000</td>\n", "      <td></td>\n", "      <td>1727808300000</td>\n", "      <td>0</td>\n", "      <td>MediumHigh</td>\n", "      <td>Inactive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>778</th>\n", "      <td>2024-11-11T07:45:00Z+05:30</td>\n", "      <td>False</td>\n", "      <td>850104201</td>\n", "      <td>Inverter status</td>\n", "      <td>EnumAlarmType</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>850100000</td>\n", "      <td>INV-39</td>\n", "      <td>1.731333e+12</td>\n", "      <td>3 : Initializing or Generating</td>\n", "      <td>850104200</td>\n", "      <td>Inverter Status</td>\n", "      <td>1731311100000</td>\n", "      <td></td>\n", "      <td>1731349800000</td>\n", "      <td>0</td>\n", "      <td>MediumHigh</td>\n", "      <td>Inactive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>779</th>\n", "      <td>2024-11-11T07:45:00Z+05:30</td>\n", "      <td>False</td>\n", "      <td>840104201</td>\n", "      <td>Inverter status</td>\n", "      <td>EnumAlarmType</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>840100000</td>\n", "      <td>INV-38</td>\n", "      <td>1.731333e+12</td>\n", "      <td>3 : Initializing or Generating</td>\n", "      <td>840104200</td>\n", "      <td>Inverter Status</td>\n", "      <td>1731311100000</td>\n", "      <td></td>\n", "      <td>1731349800000</td>\n", "      <td>0</td>\n", "      <td>MediumHigh</td>\n", "      <td>Inactive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>780</th>\n", "      <td>2024-11-11T07:45:00Z+05:30</td>\n", "      <td>False</td>\n", "      <td>560104201</td>\n", "      <td>Inverter status</td>\n", "      <td>EnumAlarmType</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>560100000</td>\n", "      <td>INV-10</td>\n", "      <td>1.731333e+12</td>\n", "      <td>3 : Initializing or Generating</td>\n", "      <td>560104200</td>\n", "      <td>Inverter Status</td>\n", "      <td>1731311100000</td>\n", "      <td></td>\n", "      <td>1731349800000</td>\n", "      <td>0</td>\n", "      <td>MediumHigh</td>\n", "      <td>Inactive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>781</th>\n", "      <td>2024-11-11T07:45:00Z+05:30</td>\n", "      <td>False</td>\n", "      <td>700104201</td>\n", "      <td>Inverter status</td>\n", "      <td>EnumAlarmType</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>700100000</td>\n", "      <td>INV-24</td>\n", "      <td>1.731333e+12</td>\n", "      <td>3 : Initializing or Generating</td>\n", "      <td>700104200</td>\n", "      <td>Inverter Status</td>\n", "      <td>1731311100000</td>\n", "      <td></td>\n", "      <td>1731349800000</td>\n", "      <td>0</td>\n", "      <td>MediumHigh</td>\n", "      <td>Inactive</td>\n", "    </tr>\n", "    <tr>\n", "      <th>782</th>\n", "      <td>2024-11-11T18:00:00Z+05:30</td>\n", "      <td>False</td>\n", "      <td>800104201</td>\n", "      <td>Inverter status</td>\n", "      <td>EnumAlarmType</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>800100000</td>\n", "      <td>INV-34</td>\n", "      <td>1.731333e+12</td>\n", "      <td>61461 : Fan fault Warning External</td>\n", "      <td>800104200</td>\n", "      <td>Inverter Status</td>\n", "      <td>1731348000000</td>\n", "      <td></td>\n", "      <td>1731349800000</td>\n", "      <td>0</td>\n", "      <td>MediumHigh</td>\n", "      <td>Inactive</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>783 rows × 19 columns</p>\n", "</div>"], "text/plain": ["                           time  acknowledge    alarmid        alarmname  \\\n", "0    2024-10-01T06:00:00Z+05:30        False  680104201  Inverter status   \n", "1    2024-10-01T06:00:00Z+05:30        False  880104201  Inverter status   \n", "2    2024-10-01T06:00:00Z+05:30        False  690104201  Inverter status   \n", "3    2024-10-01T06:00:00Z+05:30        False  710104201  Inverter status   \n", "4    2024-10-01T06:00:00Z+05:30        False  750104201  Inverter status   \n", "..                          ...          ...        ...              ...   \n", "778  2024-11-11T07:45:00Z+05:30        False  850104201  Inverter status   \n", "779  2024-11-11T07:45:00Z+05:30        False  840104201  Inverter status   \n", "780  2024-11-11T07:45:00Z+05:30        False  560104201  Inverter status   \n", "781  2024-11-11T07:45:00Z+05:30        False  700104201  Inverter status   \n", "782  2024-11-11T18:00:00Z+05:30        False  800104201  Inverter status   \n", "\n", "         alarmtype category  confirm  controllerid controllername  \\\n", "0    EnumAlarmType     None    False     680100000         INV-22   \n", "1    EnumAlarmType     None    False     880100000         INV-42   \n", "2    EnumAlarmType     None    False     690100000         INV-23   \n", "3    EnumAlarmType     None    False     710100000         INV-25   \n", "4    EnumAlarmType     None    False     750100000         INV-29   \n", "..             ...      ...      ...           ...            ...   \n", "778  EnumAlarmType     None    False     850100000         INV-39   \n", "779  EnumAlarmType     None    False     840100000         INV-38   \n", "780  EnumAlarmType     None    False     560100000         INV-10   \n", "781  EnumAlarmType     None    False     700100000         INV-24   \n", "782  EnumAlarmType     None    False     800100000         INV-34   \n", "\n", "     lastModifiedTime                             message  parameterid  \\\n", "0        1.727790e+12      3 : Initializing or Generating    680104200   \n", "1        1.727790e+12      3 : Initializing or Generating    880104200   \n", "2        1.727790e+12      3 : Initializing or Generating    690104200   \n", "3        1.727790e+12      3 : Initializing or Generating    710104200   \n", "4        1.727790e+12      3 : Initializing or Generating    750104200   \n", "..                ...                                 ...          ...   \n", "778      1.731333e+12      3 : Initializing or Generating    850104200   \n", "779      1.731333e+12      3 : Initializing or Generating    840104200   \n", "780      1.731333e+12      3 : Initializing or Generating    560104200   \n", "781      1.731333e+12      3 : Initializing or Generating    700104200   \n", "782      1.731333e+12  61461 : Fan fault Warning External    800104200   \n", "\n", "       parametername     raisedtime raisedvalue   resolvedtime  resolvedvalue  \\\n", "0    Inverter Status  1727762400000              1727808300000              0   \n", "1    Inverter Status  1727762400000              1727808300000              0   \n", "2    Inverter Status  1727762400000              1727808300000              0   \n", "3    Inverter Status  1727762400000              1727808300000              0   \n", "4    Inverter Status  1727762400000              1727808300000              0   \n", "..               ...            ...         ...            ...            ...   \n", "778  Inverter Status  1731311100000              1731349800000              0   \n", "779  Inverter Status  1731311100000              1731349800000              0   \n", "780  Inverter Status  1731311100000              1731349800000              0   \n", "781  Inverter Status  1731311100000              1731349800000              0   \n", "782  Inverter Status  1731348000000              1731349800000              0   \n", "\n", "       severity     state  \n", "0    MediumHigh  Inactive  \n", "1    MediumHigh  Inactive  \n", "2    MediumHigh  Inactive  \n", "3    MediumHigh  Inactive  \n", "4    MediumHigh  Inactive  \n", "..          ...       ...  \n", "778  MediumHigh  Inactive  \n", "779  MediumHigh  Inactive  \n", "780  MediumHigh  Inactive  \n", "781  MediumHigh  Inactive  \n", "782  MediumHigh  Inactive  \n", "\n", "[783 rows x 19 columns]"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["alarmDf"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'alarmDf' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[1], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[43malarmDf\u001b[49m\u001b[38;5;241m.\u001b[39mcolumns\n", "\u001b[1;31mNameError\u001b[0m: name 'alarmDf' is not defined"]}], "source": ["alarmDf.columns"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['MediumHigh'], dtype=object)"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["alarmDf['severity'].unique()"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['Inactive'], dtype=object)"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["alarmDf['state'].unique()"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["# Convert time columns to datetime\n", "alarmDf['raisedtime'] = pd.to_datetime(alarmDf['raisedtime'], unit='ms')\n", "\n", "# Resample alarms per day\n", "daily_alarms = alarmDf.resample('D', on='raisedtime').size()"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1400x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#Alarm Frequency Over Time\n", "#To visualize how frequently alarms are raised over time\n", "plt.figure(figsize=(14, 6))\n", "daily_alarms.plot(marker='o', linestyle='-')\n", "plt.title('Monthly Alarm Frequency')\n", "plt.xlabel('Date')\n", "plt.ylabel('Number of Alarms')\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/png": "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********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", "text/plain": ["<Figure size 600x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Active vs. Resolved Alarms\n", "# To compare how many alarms are still active vs. resolved:\n", "plt.figure(figsize=(6, 5))\n", "alarmDf['state'].value_counts().plot(kind='pie', autopct='%1.1f%%', startangle=90, colors=['lightblue', 'salmon'])\n", "plt.title('Active vs. Resolved Alarms')\n", "plt.ylabel('') \n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Number of Alarms Per Inverter\n", "plt.figure(figsize=(12, 5))  \n", "alarmDf[\"controllername\"].value_counts().nlargest(10).plot(kind=\"barh\", color=\"orange\")  \n", "plt.xlabel(\"Number of Alarms\")  \n", "plt.ylabel(\"Inverter Name\")  \n", "plt.title(\"Top 10 Inverters with the Most Alarms\")  \n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSV file saved successfully.\n"]}], "source": ["alarmDf = alarmDf[alarmDf[\"state\"].isin([\"Active\", \"Inactive\"])]\n", "\n", "# Save to CSV\n", "alarmDf.to_csv(\"Solar_alarm_data_2years.csv\", index=False)\n", "\n", "print(\"CSV file saved successfully.\")"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSV file saved successfully.\n"]}], "source": ["invAcPwrDf.to_csv(\"Solar_data_2years.csv\", index=False)\n", "print(\"CSV file saved successfully.\")"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSV file saved successfully.\n"]}], "source": ["import pandas as pd\n", "\n", "alarmDf = alarmDf[alarmDf[\"state\"].isin([\"Active\", \"Inactive\"])]\n", "\n", "# Define state order so 'Active' comes first\n", "state_order = [\"Active\", \"Inactive\"]\n", "alarmDf[\"state\"] = pd.Categorical(alarmDf[\"state\"], categories=state_order, ordered=True)\n", "\n", "# Sort by state (Active first)\n", "alarmDf = alarmDf.sort_values(\"state\")\n", "\n", "# Save to CSV\n", "alarmDf.to_csv(\"Solar_alarm_data_2years.csv\", index=False)\n", "\n", "print(\"CSV file saved successfully.\")\n"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSV file saved successfully.\n"]}], "source": ["\n", "\n", "# Strip spaces from 'state' column (fixes hidden space issues)\n", "alarmDf[\"state\"] = alarmDf[\"state\"].str.strip()\n", "\n", "# Filter only 'Active' and 'Inactive' states\n", "alarmDf = alarmDf[alarmDf[\"state\"].isin([\"Active\", \"Inactive\"])]\n", "\n", "# Define state order so 'Active' comes first\n", "state_order = [\"Active\", \"Inactive\"]\n", "alarmDf[\"state\"] = pd.Categorical(alarmDf[\"state\"], categories=state_order, ordered=True)\n", "\n", "# Sort by state (Active first)\n", "alarmDf = alarmDf.sort_values(\"state\")\n", "\n", "# Save to CSV\n", "alarmDf.to_csv(\"Solar_alarm_data_2years.csv\", index=False)\n", "\n", "print(\"CSV file saved successfully.\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["state_order = [\"Active\", \"Inactive\"]\n", "alarmDf[\"state\"] = pd.Categorical(alarmDf[\"state\"], categories=state_order, ordered=True)\n", "\n", "# Filter only 'Active' and 'Inactive' states\n", "alarmDf = alarmDf[alarmDf[\"state\"].isin(state_order)]\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}