from DB.models import SolarReport, WindReport, DgrBothDb, WhatsAppMessage
from DB.setup_db import session
from helper.logger_setup import setup_logger
from sqlalchemy.exc import SQLAlchemyError


# Initialize logger
logger = setup_logger("db_ops", "db_ops.log")

def insert_wind_data_db(records):
    """
    Inserts wind DGR records into the database.

    Args:
        records (List[Dict]): List of dictionaries containing wind DGR data.
    """
    try:
        logger.info(f"Inserting {len(records)} wind records into DB.")
        objs = [WindReport(**record) for record in records]
        session.add_all(objs)
        session.commit()
        logger.info("Wind data inserted successfully.")
    except Exception as e:
        session.rollback()
        logger.exception("Error inserting wind data:")
    finally:
        session.close()
        logger.debug("DB session closed for wind data.")

def insert_solar_data_db(records):
    """
    Inserts solar DGR records into the database.

    Args:
        records (List[Dict]): List of dictionaries containing solar DGR data.
    """
    try:
        logger.info(f"Inserting {len(records)} solar records into DB.")
        objs = [SolarReport(**record) for record in records]
        session.add_all(objs)
        session.commit()
        logger.info("Solar data inserted successfully.")
    except Exception as e:
        session.rollback()
        logger.exception("Error inserting solar data:")
    finally:
        session.close()
        logger.debug("DB session closed for solar data.")

def insert_both_data_db(records):
    """
    Inserts combined DGR records into the database.

    Args:
        records (List[Dict]): List of dictionaries containing combined DGR data.
    """
    try:
        logger.info(f"Inserting {len(records)} combined records into DB.")
        objs = [DgrBothDb(**record) for record in records]
        session.add_all(objs)
        session.commit()
        logger.info("Combined wind + solar data inserted successfully.")
    except Exception as e:
        session.rollback()
        logger.exception("Error inserting combined data:")
    finally:
        session.close()
        logger.debug("DB session closed for combined data.")

def insert_message_data_db(records: list[dict]):
    """
    Inserts WhatsApp message records into the database.

    Args:
        records (List[Dict]): List of dictionaries containing WhatsApp message data.
    """
    
    try:
        logger.info(f"Inserting {len(records)} WhatsApp message record(s) into DB.")
        objs = [WhatsAppMessage(**record) for record in records]
        session.add_all(objs)
        session.commit()
        logger.info("WhatsApp message data inserted successfully.")
    except Exception as e:
        session.rollback()
        logger.exception("Error inserting WhatsApp message data:")
    finally:
        session.close()
        logger.debug("DB session closed for WhatsApp message insert.")


def fetch_messages_by_message_id(message_id: str) -> list[dict]:
    """
    Fetches all WhatsApp message records that match a given message_id.

    Args:
        message_id (str): The WhatsApp message ID to filter by.

    Returns:
        List[dict]: List of message records as dictionaries.
    """
    try:
        logger.info(f"Fetching records for message_id: {message_id}")
        results = session.query(WhatsAppMessage).filter(WhatsAppMessage.message_id == message_id).all()
        logger.info(f"Found {len(results)} record(s) for message_id {message_id}")
        return [msg.to_dict() for msg in results]
    except Exception as e:
        logger.exception("Error fetching WhatsApp message records:")
        return []
    finally:
        session.close()
        logger.debug("DB session closed after fetching message records.")




def get_solar_report_data(plant_short_name, report_date):
    """
    Fetches POA, DGR path, PR, and Generation for a given plant and date from SolarReport.

    Args:
        plant_short_name (str): The short name of the plant.
        report_date (datetime.date): The report date.

    Returns:
        dict: A dictionary with 'poa', 'dgr_path', 'pr', and 'generation' if found, else None.
    """
    try:
        record = session.query(SolarReport).filter_by(
            plant_short_name=plant_short_name,
            date=report_date
        ).first()

        if record:
            return {
                "poa": record.poa,
                "dgr_path": getattr(record, 'dgr_path', None),
                "pr": record.pr,
                "generation": record.generation
            }
        else:
            print("⚠️ No matching solar report found.")
            return None

    except SQLAlchemyError as e:
        print("❌ Error fetching solar report data:", e)
        return None

    finally:
        session.close()





def get_dgr_path_by_id_solar(report_id):
    """
    Fetches the DGR path for a given SolarReport based on the report ID.

    Args:
        report_id (int): The ID of the solar report.

    Returns:
        str: The DGR path if found, else None.
    """
    try:
        record = session.query(SolarReport).filter_by(id=report_id).first()

        if record:
            return record.dgr_path
        else:
            print("⚠️ No matching solar report found with ID:", report_id)
            return None

    except SQLAlchemyError as e:
        print("❌ Error fetching DGR path:", e)
        return None

    finally:
        session.close()


def get_wind_report_data(plant_short_name, report_date):
    """
    Fetches wind_speed, DGR path and Generation for a given plant and date from WindReport.

    Args:
        plant_short_name (str): The short name of the plant.
        report_date (datetime.date): The report date.

    Returns:
        dict: A dictionary with 'wind_speed', 'dgr_path', and 'generation' if found, else None.
    """
    try:
        record = session.query(WindReport).filter_by(
            plant_short_name=plant_short_name,
            date=report_date
        ).first()

        if record:
            return {
                "wind_speed": record.wind_speed,
                "dgr_path": getattr(record, 'dgr_path', None),
                "generation": record.generation
            }
        else:
            print("⚠️ No matching solar report found.")
            return None

    except SQLAlchemyError as e:
        print("❌ Error fetching solar report data:", e)
        return None

    finally:
        session.close()




def get_combine_report_data(plant_short_name_wind, report_date):
    """
    Fetches wind_speed, DGR path and Generation for a given plant and date from WindReport.

    Args:
        plant_short_name (str): The short name of the plant.
        report_date (datetime.date): The report date.

    Returns:
        dict: A dictionary with 'wind_speed', 'dgr_path', and 'generation' if found, else None.
    """
    try:
        record = session.query(DgrBothDb).filter_by(
            plant_short_name_wind=plant_short_name_wind,
            date=report_date
        ).first()

        if record:
            return {
            "wind_speed": record.wind_speed,
            "dgr_path": getattr(record, 'dgr_path', None),
            'generation_solar': record.generation_solar,
            'pr': record.pr,
            'poa': record.poa,
            'generation_wind': record.generation_wind
            }
        else:
            print("⚠️ No matching solar report found.")
            return None

    except SQLAlchemyError as e:
        print("❌ Error fetching solar report data:", e)
        return None

    finally:
        session.close()