import os
from helper.logger_setup import setup_logger
# Import enhanced plotting functions with <PERSON>born + Matplotlib hybrid styling
from helper.solar_plots_enhanced import (
    plot_monthly_generation,
    plot_yearly_generation,
    plot_pr_monthly,
    generate_solar_plot
)
from helper.wind_plots_enhanced import (
    plot_plant_wise_generation_pdf,
    plot_wind_speed_last_12_months,
    plot_wind_speed_last_30_days,
    plot_daily_generation_last_30_days,
    generate_wind_plot
)
from helper.utils import (
    get_dynamic_dates,
    merge_pdfs_both_plants,
    generate_combined_both_pdf,
    fetch_data_total,
    get_capacity_from_csv
    )
from helper.storage_s3 import upload_file_s3
from DB.db_ops import insert_both_data_db, get_combine_report_data

# Set up logger
logger = setup_logger('both_plants_automation', 'both_plants_automation.log')


def fetch_all_data_solar(plant_name, start_date, current_month_start, last_30_days_start, current_year_start, yearly,
                               condition_poa, condition_pr, condition_generation, condition_monthly_pr):
    """
    Sequentially fetches solar data to reduce RAM usage.
    """
    logger.info(f"Starting sequential solar data fetch for plant: {plant_name}")

    # Fetch data sequentially instead of concurrently
    poa_data = fetch_data_total(plant_name, ['Daily POA Energy'], 'Plant', start_date, start_date, condition_poa)
    pr_data = fetch_data_total(plant_name, ['PR'], 'Plant', start_date, start_date, condition_pr)
    daily_generation = fetch_data_total(plant_name, ['Daily Energy'], 'Plant', start_date, start_date, condition_generation)
    generation_monthly_value = fetch_data_total(plant_name, ['Daily Energy'], 'Plant', current_month_start, start_date, condition_generation)
    generation_yearly_value = fetch_data_total(plant_name, ['Daily Energy'], 'Plant', current_year_start, start_date, condition_generation)
    pr_data_monthly_value = fetch_data_total(plant_name, ['PR'], 'Plant', current_month_start, start_date, condition_monthly_pr)
    pr_data_yearly_value = fetch_data_total(plant_name, ['PR'], 'Plant', current_year_start, start_date, condition_monthly_pr)
    poa_data_monthly_value = fetch_data_total(plant_name, ['Daily POA Energy'], 'Plant', current_month_start, start_date, condition_poa)
    poa_data_yearly_value = fetch_data_total(plant_name, ['Daily POA Energy'], 'Plant', current_year_start, start_date, condition_poa)
    generation_monthly = fetch_data_total(plant_name, ['Daily Energy'], 'Plant', last_30_days_start, start_date, condition_generation)
    generation_yearly = fetch_data_total(plant_name, ['Daily Energy'], 'Plant', yearly, start_date, condition_generation)
    pr_data_monthly = fetch_data_total(plant_name, ['PR'], 'Plant', last_30_days_start, start_date, condition_monthly_pr)
    pr_data_yearly = fetch_data_total(plant_name, ['PR'], 'Plant', yearly, start_date, condition_monthly_pr)

    logger.info(f"Completed sequential solar data fetch for plant: {plant_name}")

    return {
        "poa_data": poa_data,
        "pr_data": pr_data,
        "daily_generation": daily_generation,
        "generation_monthly_value": generation_monthly_value,
        "generation_yearly_value": generation_yearly_value,
        "pr_data_monthly_value": pr_data_monthly_value,
        "pr_data_yearly_value": pr_data_yearly_value,
        "poa_data_monthly_value": poa_data_monthly_value,
        "poa_data_yearly_value": poa_data_yearly_value,
        "generation_monthly": generation_monthly,
        "generation_yearly": generation_yearly,
        "pr_data_monthly": pr_data_monthly,
        "pr_data_yearly": pr_data_yearly,
    }


def fetch_all_data_wind(plant_name, start_date, current_month_start, last_30_days_start, current_year_start, yearly_date,
                              condition_wind, condition_generation):
    """
    Sequentially fetches wind data to reduce RAM usage.
    """
    logger.info(f"Starting sequential wind data fetch for plant: {plant_name}")

    # Fetch data sequentially instead of concurrently
    wind_speed_data_value = fetch_data_total(plant_name, ['WTUR.Wind-Speed'], 'Turbine', start_date, start_date, condition_wind)
    daily_generation_value = fetch_data_total(plant_name, ['WTUR.Generation today'], 'Turbine', start_date, start_date, condition_generation)
    wind_data_monthly_value = fetch_data_total(plant_name, ['WTUR.Wind-Speed'], 'Turbine', current_month_start, start_date, condition_wind)
    generation_monthly_value = fetch_data_total(plant_name, ['WTUR.Generation today'], 'Turbine', current_month_start, start_date, condition_generation)
    wind_data_yearly_value = fetch_data_total(plant_name, ['WTUR.Wind-Speed'], 'Turbine', current_year_start, start_date, condition_wind)
    yearly_generation_value = fetch_data_total(plant_name, ['WTUR.Generation today'], 'Turbine', current_year_start, start_date, condition_generation)
    wind_data_monthly_plot = fetch_data_total(plant_name, ['WTUR.Wind-Speed'], 'Turbine', last_30_days_start, start_date, condition_wind)
    generation_monthly_plot = fetch_data_total(plant_name, ['WTUR.Generation today'], 'Turbine', last_30_days_start, start_date, condition_generation)
    wind_data_yearly_plot = fetch_data_total(plant_name, ['WTUR.Wind-Speed'], 'Turbine', yearly_date, start_date, condition_wind)
    generation_yearly_plot = fetch_data_total(plant_name, ['WTUR.Generation today'], 'Turbine', yearly_date, start_date, condition_generation)

    logger.info(f"Completed sequential wind data fetch for plant: {plant_name}")

    return {
        "wind_speed_data_value": wind_speed_data_value,
        "daily_generation_value": daily_generation_value,
        "wind_data_monthly_value": wind_data_monthly_value,
        "generation_monthly_value": generation_monthly_value,
        "wind_data_yearly_value": wind_data_yearly_value,
        "yearly_generation_value": yearly_generation_value,
        "wind_data_monthly_plot": wind_data_monthly_plot,
        "generation_monthly_plot": generation_monthly_plot,
        "wind_data_yearly_plot": wind_data_yearly_plot,
        "generation_yearly_plot": generation_yearly_plot,
    }


def clean_temp_files(file_list):
    """
    Remove files in file_list if they exist.
    """
    for file in file_list:
        if file and os.path.exists(file):
            try:
                os.remove(file)
            except Exception as e:
                logger.error(f"Error cleaning file {file}: {e}", exc_info=True)


def generate_solar_automation_report(plant_name, start_date, customer_name, project):
    """
    Generates the solar automation report.
    """
    logger.info(f"Starting report generation for Plant: {plant_name}, Date: {start_date}, Customer: {customer_name}, Project: {project}")
    try:
        # Define conditions
        condition_poa = {"Daily POA Energy": "last"}
        condition_daily_pr = {"PR": "last"}
        condition_monthly_pr = {"PR": "mean"}
        condition_generation = {"Daily Energy": "max"}

        # Setup dates
        # yearly = '2024-04-01'
        (current_month_start, _, _,
         _, _, last_30_days_start, current_year_start, last_year_date) = get_dynamic_dates(start_date)

        # Run sequential fetching
        data = fetch_all_data_solar(
            plant_name, start_date, current_month_start, last_30_days_start,
            current_year_start, last_year_date, condition_poa, condition_daily_pr, condition_generation, condition_monthly_pr
        )

        # Extract data from results
        poa_data = data["poa_data"]
        pr_data = data["pr_data"]
        daily_generation = data["daily_generation"]
        generation_monthly_value = data["generation_monthly_value"]
        generation_yearly_value = data["generation_yearly_value"]
        pr_data_monthly_value = data["pr_data_monthly_value"]
        pr_data_yearly_value = data["pr_data_yearly_value"]
        poa_data_monthly_value = data["poa_data_monthly_value"]
        poa_data_yearly_value = data["poa_data_yearly_value"]
        generation_monthly = data["generation_monthly"]
        generation_yearly = data["generation_yearly"]
        pr_data_monthly = data["pr_data_monthly"]
        pr_data_yearly = data["pr_data_yearly"]

        # Calculate report metrics
        total_generation = daily_generation.iloc[:, 1:].sum().sum() if not daily_generation.empty else 0
        month_gen_value = generation_monthly_value.iloc[:, 1:].sum().sum() if not generation_monthly_value.empty else 0
        year_gen_value = generation_yearly_value.iloc[:, 1:].sum().sum() if not generation_yearly_value.empty else 0

        month_pr_value = pr_data_monthly_value.iloc[:, 1:].mean().mean() if not pr_data_monthly_value.empty else 0
        daily_pr_percentage = pr_data.iloc[:, 1:].mean().mean() if not pr_data.empty else 0
        year_pr_value = pr_data_yearly_value.iloc[:, 1:].mean().mean() if not pr_data_yearly_value.empty else 0

        monthly_poa_value = poa_data_monthly_value.iloc[:, 1:].mean().mean() if not poa_data_monthly_value.empty else 0
        yearly_poa_value = poa_data_yearly_value.iloc[:, 1:].mean().mean() if not poa_data_yearly_value.empty else 0
        avg_poa = poa_data.iloc[:, 1:].mean().mean() if not poa_data.empty else 0

        # Generate plots
        image_files = [
            plot_monthly_generation(generation_monthly, plant_name),
            plot_yearly_generation(generation_yearly, plant_name),
            plot_pr_monthly(pr_data_monthly, plant_name),
            plot_pr_monthly(pr_data_yearly, plant_name)
        ]
        custom_titles = [
            "Daily Generation Over Last 30 Days",
            "Last 12 Months Energy Generation",
            "AVG PR% Over Last 30 Days",
            "AVG PR % Over Last 12 Months"
        ]
        data_values = {
            "daily_generation.png": (round(float(total_generation), 2), "KWh"),
            "monthly_generation.png": (round(float(month_gen_value), 2), "KWh"),
            "yearly_generation.png": (round(float(year_gen_value), 2), "KWh"),
            "daily_pr.png": (round(float(daily_pr_percentage), 2), "%"),
            "monthly_pr.png": (round(float(month_pr_value), 2), "%"),
            "yearly_pr.png": (round(float(year_pr_value), 2), "%")
        }

        finished_plot_path = generate_solar_plot(
            image_files, data_values, f"{plant_name}_new_solar_report.pdf", custom_titles, customer_name, start_date
        )

        # Clean up temporary image files
        clean_temp_files(image_files)

        return (avg_poa, daily_pr_percentage, total_generation, month_gen_value,
                year_gen_value, month_pr_value, year_pr_value, monthly_poa_value,
                yearly_poa_value, finished_plot_path)

    except Exception as e:
        logger.error(f"Error generating solar report for {plant_name}: {e}", exc_info=True)
        raise


def generate_wind_automation_report(plant_name, start_date, customer_name, project, ma_percent):
    """
    Generates the wind automation report.
    """
    logger.info(f"Starting wind report generation for Plant: {plant_name}, Date: {start_date}")
    try:
        (current_month_start, _, _,
         _, _, last_30_days_start, current_year_start, last_year_date) = get_dynamic_dates(start_date)
        # yearly_date = '2024-04-01'
        condition_wind = {"Wind-Speed": "mean"}
        condition_generation = {"Generation today": "last"}

        data = fetch_all_data_wind(
            plant_name, start_date, current_month_start, last_30_days_start,
            current_year_start, last_year_date, condition_wind, condition_generation
        )

        # Extract data from results
        wind_speed_data_value = data["wind_speed_data_value"]
        daily_generation_value = data["daily_generation_value"]
        wind_data_monthly_value = data["wind_data_monthly_value"]
        generation_monthly_value = data["generation_monthly_value"]
        wind_data_yearly_value = data["wind_data_yearly_value"]
        yearly_generation_value = data["yearly_generation_value"]
        wind_data_monthly_plot = data["wind_data_monthly_plot"]
        generation_monthly_plot = data["generation_monthly_plot"]
        wind_data_yearly_plot = data["wind_data_yearly_plot"]
        generation_yearly_plot = data["generation_yearly_plot"]

        # Calculate metrics
        avg_wind_speed = wind_speed_data_value.iloc[:, 1:].mean().mean() if not wind_speed_data_value.empty else 0
        total_generation = daily_generation_value.iloc[:, 1:].sum().sum() if not daily_generation_value.empty else 0
        monthly_wind = wind_data_monthly_value.iloc[:, 1:].mean().mean() if not wind_data_monthly_value.empty else 0
        monthly_generation = generation_monthly_value.iloc[:, 1:].sum().sum() if not generation_monthly_value.empty else 0
        yearly_wind = wind_data_yearly_value.iloc[:, 1:].mean().mean() if not wind_data_yearly_value.empty else 0
        yearly_generation = yearly_generation_value.iloc[:, 1:].sum().sum() if not yearly_generation_value.empty else 0

        # Generate plots for wind data
        gen_plot_year = plot_plant_wise_generation_pdf(generation_yearly_plot, plant_name,date_col="time")
        wind_plot_year = plot_wind_speed_last_12_months(wind_data_yearly_plot, plant_name,date_col="time")
        wind_plot_30 = plot_wind_speed_last_30_days(wind_data_monthly_plot, plant_name, date_col="time")
        gen_plot_30 = plot_daily_generation_last_30_days(generation_monthly_plot, plant_name, date_col="time")
        combined_30 = generate_wind_plot(wind_plot_30, gen_plot_30, f"{plant_name}_combined_report.pdf")

        # Clean up temporary plot files (avoid duplicate removals)
        clean_temp_files([wind_plot_30, gen_plot_30])

        return (avg_wind_speed, total_generation, ma_percent, monthly_wind,
                monthly_generation, yearly_wind, yearly_generation, gen_plot_year,
                wind_plot_year, combined_30, wind_speed_data_value, daily_generation_value)

    except Exception as e:
        logger.error(f"Error generating wind report for {plant_name}: {e}", exc_info=True)
        raise


def combined_both_plants(plant_name_solar, plant_name_wind, start_date, customer_name, project, ma_percent):
    """
    Generates a combined report for both solar and wind plants.
    """
    try:
        # Generate individual reports
        (daily_wind_speed, daily_gen_wind, ma_percent, monthly_wind, monthly_gen_wind,
         yearly_wind, yearly_generation, gen_plot_year, wind_plot_year, combined_30,
         wind_speed_data_value, daily_generation_value) = generate_wind_automation_report(
            plant_name_wind, start_date, customer_name, project, ma_percent
        )

        (daily_poa, daily_pr, daily_gen_solar, month_gen_solar, month_pr_solar,
         month_pr_value, year_pr_value, monthly_poa_solar, yearly_poa_value,
         final_plot_path) = generate_solar_automation_report(plant_name_solar, start_date, customer_name, project)

        # Map capacities based on plant names
        # capacities_wind = {"IN.INTE.SPFL": "2.7", "IN.INTE.ANSP": "2.7", "IN.INTE.SABE": "2.7"}
        # capacite_wind = capacities_wind.get(plant_name_wind, "N/A")
        # capacities_solar = {"IN.INTE.KIDS": "4.8", "IN.INTE.SAAB": "1.9"}
        # capacite_solar = capacities_solar.get(plant_name_solar, "N/A")

        capacite_solar = get_capacity_from_csv(plant_name_solar)
        capacite_wind = get_capacity_from_csv(plant_name_wind)

        # Generate summary PDF and combine all reports
        summary_pdf = generate_combined_both_pdf(
            start_date, customer_name, project,
            daily_poa, daily_pr, daily_gen_solar, month_gen_solar, month_pr_value,
            monthly_poa_solar, daily_wind_speed, daily_gen_wind, monthly_wind, monthly_gen_wind,
            capacite_wind, capacite_solar, wind_speed_data_value, daily_generation_value,
            plant_name_solar, plant_name_wind
        )
        final_pdf_path = os.path.join("static", "both_final_report", f"{plant_name_solar}_DGR_{start_date}.pdf")
        final_report = merge_pdfs_both_plants(summary_pdf, gen_plot_year, wind_plot_year, combined_30, final_plot_path, final_pdf_path)

        # Clean up temporary files (avoid duplicate entries)
        clean_temp_files([summary_pdf, gen_plot_year, wind_plot_year, combined_30, final_plot_path])

        # Upload final report to S3 and return file URL or path
        s3_relative_path = os.path.join("both_final_report", f"{plant_name_solar}_DGR_{start_date}.pdf")
        upload_file_s3(final_report, s3_relative_path)


        result = get_combine_report_data(plant_name_wind, start_date)
        if result:
            data = [{
                'date': start_date,
                'plant_short_name_solar': plant_name_solar,
                'plant_long_name_solar': customer_name,
                'generation_solar': round(float(daily_gen_solar), 2),
                'pr': round(float(daily_pr), 2),
                'poa': round(float(daily_poa), 2),
                'plant_short_name_wind': plant_name_wind,
                'plant_long_name_wind': customer_name,
                'generation_wind': round(float(daily_gen_wind), 2),
                'wind_speed': round(float(daily_wind_speed), 2),
                'approved': 0,
                'review': 0,
                'action_performed': 0,
                "dgr_path": final_report,
                "status": "Regenerated"
            }]
        else:
            data = [{
                'date': start_date,
                'plant_short_name_solar': plant_name_solar,
                'plant_long_name_solar': customer_name,
                'generation_solar': round(float(daily_gen_solar), 2),
                'pr': round(float(daily_pr), 2),
                'poa': round(float(daily_poa), 2),
                'plant_short_name_wind': plant_name_wind,
                'plant_long_name_wind': customer_name,
                'generation_wind': round(float(daily_gen_wind), 2),
                'wind_speed': round(float(daily_wind_speed), 2),
                'approved': 0,
                'review': 0,
                'action_performed': 0,
                "dgr_path": final_report
            }]
        # Insert data into database
        insert_both_data_db(data)
        logger.info(f"Data inserted into database for {plant_name_solar} and {plant_name_wind}")

        return final_report

    except Exception as e:
        logger.error(f"Error generating combined report: {e}", exc_info=True)
        raise
