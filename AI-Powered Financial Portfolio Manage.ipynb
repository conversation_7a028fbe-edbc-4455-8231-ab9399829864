{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Install Required Libraries"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: numpy in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (2.2.3)\n", "Requirement already satisfied: pandas in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (2.2.3)\n", "Requirement already satisfied: matplotlib in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (3.10.0)\n", "Requirement already satisfied: seaborn in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (0.13.2)\n", "Collecting tensorflow\n", "  Downloading tensorflow-2.19.0-cp312-cp312-win_amd64.whl.metadata (4.1 kB)\n", "Requirement already satisfied: torch in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (2.6.0)\n", "Requirement already satisfied: transformers in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (4.49.0)\n", "Requirement already satisfied: scikit-learn in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (1.6.1)\n", "Collecting xgboost\n", "  Downloading xgboost-3.0.0-py3-none-win_amd64.whl.metadata (2.1 kB)\n", "Collecting prophet\n", "  Downloading prophet-1.1.6-py3-none-win_amd64.whl.metadata (3.6 kB)\n", "Collecting stable-baselines3\n", "  Downloading stable_baselines3-2.5.0-py3-none-any.whl.metadata (4.8 kB)\n", "Collecting gymnasium\n", "  Downloading gymnasium-1.1.1-py3-none-any.whl.metadata (9.4 kB)\n", "Collecting pettingzoo\n", "  Downloading pettingzoo-1.24.3-py3-none-any.whl.metadata (8.5 kB)\n", "Collecting alpaca-trade-api\n", "  Downloading alpaca_trade_api-3.2.0-py3-none-any.whl.metadata (29 kB)\n", "Collecting textblob\n", "  Downloading textblob-0.19.0-py3-none-any.whl.metadata (4.4 kB)\n", "Collecting spacy\n", "  Downloading spacy-3.8.4-cp312-cp312-win_amd64.whl.metadata (27 kB)\n", "Requirement already satisfied: beautifulsoup4 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (4.13.3)\n", "Collecting selenium\n", "  Downloading selenium-4.29.0-py3-none-any.whl.metadata (7.1 kB)\n", "Collecting lxml\n", "  Downloading lxml-5.3.1-cp312-cp312-win_amd64.whl.metadata (3.8 kB)\n", "Collecting yahoo-finance\n", "  Downloading yahoo-finance-1.4.0.tar.gz (8.9 kB)\n", "  Preparing metadata (setup.py): started\n", "  Preparing metadata (setup.py): finished with status 'done'\n", "Collecting mlflow\n", "  Downloading mlflow-2.21.0-py3-none-any.whl.metadata (30 kB)\n", "Collecting deap\n", "  Downloading deap-1.4.2-cp312-cp312-win_amd64.whl.metadata (13 kB)\n", "Collecting nevergrad\n", "  Downloading nevergrad-1.0.8-py3-none-any.whl.metadata (11 kB)\n", "Collecting ray[rllib]\n", "  Downloading ray-2.43.0-cp312-cp312-win_amd64.whl.metadata (20 kB)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from pandas) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pandas) (2025.1)\n", "Requirement already satisfied: tzdata>=2022.7 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pandas) (2025.1)\n", "Requirement already satisfied: contourpy>=1.0.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from matplotlib) (1.3.1)\n", "Requirement already satisfied: cycler>=0.10 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from matplotlib) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from matplotlib) (4.56.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from matplotlib) (1.4.8)\n", "Requirement already satisfied: packaging>=20.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from matplotlib) (24.2)\n", "Requirement already satisfied: pillow>=8 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from matplotlib) (11.1.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from matplotlib) (3.2.1)\n", "Collecting absl-py>=1.0.0 (from tensorflow)\n", "  Downloading absl_py-2.1.0-py3-none-any.whl.metadata (2.3 kB)\n", "Collecting astunparse>=1.6.0 (from tensorflow)\n", "  Downloading astunparse-1.6.3-py2.py3-none-any.whl.metadata (4.4 kB)\n", "Collecting flatbuffers>=24.3.25 (from tensorflow)\n", "  Downloading flatbuffers-25.2.10-py2.py3-none-any.whl.metadata (875 bytes)\n", "Collecting gast!=0.5.0,!=0.5.1,!=0.5.2,>=0.2.1 (from tensorflow)\n", "  Downloading gast-0.6.0-py3-none-any.whl.metadata (1.3 kB)\n", "Collecting google-pasta>=0.1.1 (from tensorflow)\n", "  Downloading google_pasta-0.2.0-py3-none-any.whl.metadata (814 bytes)\n", "Collecting libclang>=13.0.0 (from tensorflow)\n", "  Downloading libclang-18.1.1-py2.py3-none-win_amd64.whl.metadata (5.3 kB)\n", "Collecting opt-einsum>=2.3.2 (from tensorflow)\n", "  Downloading opt_einsum-3.4.0-py3-none-any.whl.metadata (6.3 kB)\n", "Collecting protobuf!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<6.0.0dev,>=3.20.3 (from tensorflow)\n", "  Downloading protobuf-5.29.4-cp310-abi3-win_amd64.whl.metadata (592 bytes)\n", "Requirement already satisfied: requests<3,>=2.21.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorflow) (2.32.3)\n", "Requirement already satisfied: setuptools in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorflow) (75.8.0)\n", "Requirement already satisfied: six>=1.12.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorflow) (1.17.0)\n", "Collecting termcolor>=1.1.0 (from tensorflow)\n", "  Downloading termcolor-2.5.0-py3-none-any.whl.metadata (6.1 kB)\n", "Requirement already satisfied: typing-extensions>=3.6.6 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorflow) (4.12.2)\n", "Requirement already satisfied: wrapt>=1.11.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorflow) (1.17.2)\n", "Collecting grpcio<2.0,>=1.24.3 (from tensorflow)\n", "  Downloading grpcio-1.71.0-cp312-cp312-win_amd64.whl.metadata (4.0 kB)\n", "Collecting tensorboard~=2.19.0 (from tensorflow)\n", "  Downloading tensorboard-2.19.0-py3-none-any.whl.metadata (1.8 kB)\n", "Collecting keras>=3.5.0 (from tensorflow)\n", "  Downloading keras-3.9.0-py3-none-any.whl.metadata (6.1 kB)\n", "Collecting numpy\n", "  Downloading numpy-2.1.3-cp312-cp312-win_amd64.whl.metadata (60 kB)\n", "Collecting h5py>=3.11.0 (from tensorflow)\n", "  Downloading h5py-3.13.0-cp312-cp312-win_amd64.whl.metadata (2.5 kB)\n", "Collecting ml-dtypes<1.0.0,>=0.5.1 (from tensorflow)\n", "  Downloading ml_dtypes-0.5.1-cp312-cp312-win_amd64.whl.metadata (22 kB)\n", "Requirement already satisfied: filelock in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from torch) (3.17.0)\n", "Requirement already satisfied: networkx in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from torch) (3.4.2)\n", "Requirement already satisfied: jinja2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from torch) (3.1.5)\n", "Requirement already satisfied: fsspec in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from torch) (2025.2.0)\n", "Requirement already satisfied: sympy==1.13.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from torch) (1.13.1)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from sympy==1.13.1->torch) (1.3.0)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.26.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from transformers) (0.28.1)\n", "Requirement already satisfied: pyyaml>=5.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from transformers) (6.0.2)\n", "Requirement already satisfied: regex!=2019.12.17 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from transformers) (2024.11.6)\n", "Requirement already satisfied: tokenizers<0.22,>=0.21 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from transformers) (0.21.0)\n", "Requirement already satisfied: safetensors>=0.4.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from transformers) (0.5.2)\n", "Requirement already satisfied: tqdm>=4.27 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from transformers) (4.67.1)\n", "Requirement already satisfied: scipy>=1.6.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from scikit-learn) (1.15.2)\n", "Requirement already satisfied: joblib>=1.2.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from scikit-learn) (1.4.2)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from scikit-learn) (3.5.0)\n", "Collecting cmdstanpy>=1.0.4 (from prophet)\n", "  Downloading cmdstanpy-1.2.5-py3-none-any.whl.metadata (4.0 kB)\n", "Collecting holidays<1,>=0.25 (from prophet)\n", "  Downloading holidays-0.69-py3-none-any.whl.metadata (28 kB)\n", "Collecting importlib-resources (from prophet)\n", "  Downloading importlib_resources-6.5.2-py3-none-any.whl.metadata (3.9 kB)\n", "Requirement already satisfied: click>=7.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from ray[rllib]) (8.1.8)\n", "Requirement already satisfied: jsonschema in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from ray[rllib]) (4.23.0)\n", "Collecting msgpack<2.0.0,>=1.0.0 (from ray[rllib])\n", "  Downloading msgpack-1.1.0-cp312-cp312-win_amd64.whl.metadata (8.6 kB)\n", "Requirement already satisfied: aiosignal in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from ray[rllib]) (1.3.2)\n", "Requirement already satisfied: frozenlist in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from ray[rllib]) (1.5.0)\n", "Collecting tensorboardX>=1.9 (from ray[rllib])\n", "  Downloading tensorboardX-2.6.2.2-py2.py3-none-any.whl.metadata (5.8 kB)\n", "Collecting pyarrow>=9.0.0 (from ray[rllib])\n", "  Downloading pyarrow-19.0.1-cp312-cp312-win_amd64.whl.metadata (3.4 kB)\n", "Collecting dm_tree (from ray[rllib])\n", "  Downloading dm_tree-0.1.9-cp312-cp312-win_amd64.whl.metadata (2.5 kB)\n", "Collecting gymnasium\n", "  Downloading gymnasium-1.0.0-py3-none-any.whl.metadata (9.5 kB)\n", "Collecting lz4 (from ray[rllib])\n", "  Downloading lz4-4.4.3-cp312-cp312-win_amd64.whl.metadata (3.9 kB)\n", "Collecting ormsgpack==1.7.0 (from ray[rllib])\n", "  Downloading ormsgpack-1.7.0-cp312-cp312-win_amd64.whl.metadata (44 kB)\n", "Collecting cloudpickle>=1.2.0 (from gymnasium)\n", "  Downloading cloudpickle-3.1.1-py3-none-any.whl.metadata (7.1 kB)\n", "Collecting farama-notifications>=0.0.1 (from gymnasium)\n", "  Downloading Farama_Notifications-0.0.4-py3-none-any.whl.metadata (558 bytes)\n", "Collecting urllib3<2,>1.24 (from alpaca-trade-api)\n", "  Downloading urllib3-1.26.20-py2.py3-none-any.whl.metadata (50 kB)\n", "Requirement already satisfied: websocket-client<2,>=0.56.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from alpaca-trade-api) (1.8.0)\n", "Collecting websockets<11,>=9.0 (from alpaca-trade-api)\n", "  Downloading websockets-10.4.tar.gz (84 kB)\n", "  Preparing metadata (setup.py): started\n", "  Preparing metadata (setup.py): finished with status 'done'\n", "Collecting msgpack<2.0.0,>=1.0.0 (from ray[rllib])\n", "  Downloading msgpack-1.0.3.tar.gz (123 kB)\n", "  Preparing metadata (setup.py): started\n", "  Preparing metadata (setup.py): finished with status 'done'\n", "Requirement already satisfied: aiohttp<4,>=3.8.3 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from alpaca-trade-api) (3.11.12)\n", "Collecting pyyaml>=5.1 (from transformers)\n", "  Downloading PyYAML-6.0.1-cp312-cp312-win_amd64.whl.metadata (2.1 kB)\n", "Collecting deprecation==2.1.0 (from alpaca-trade-api)\n", "  Downloading deprecation-2.1.0-py2.py3-none-any.whl.metadata (4.6 kB)\n", "Collecting nltk>=3.9 (from textblob)\n", "  Downloading nltk-3.9.1-py3-none-any.whl.metadata (2.9 kB)\n", "Collecting spacy-legacy<3.1.0,>=3.0.11 (from spacy)\n", "  Downloading spacy_legacy-3.0.12-py2.py3-none-any.whl.metadata (2.8 kB)\n", "Collecting spacy-loggers<2.0.0,>=1.0.0 (from spacy)\n", "  Downloading spacy_loggers-1.0.5-py3-none-any.whl.metadata (23 kB)\n", "Collecting murmurhash<1.1.0,>=0.28.0 (from spacy)\n", "  Downloading murmurhash-1.0.12-cp312-cp312-win_amd64.whl.metadata (2.2 kB)\n", "Collecting cymem<2.1.0,>=2.0.2 (from spacy)\n", "  Downloading cymem-2.0.11-cp312-cp312-win_amd64.whl.metadata (8.8 kB)\n", "Collecting preshed<3.1.0,>=3.0.2 (from spacy)\n", "  Downloading preshed-3.0.9-cp312-cp312-win_amd64.whl.metadata (2.2 kB)\n", "Collecting thinc<8.4.0,>=8.3.4 (from spacy)\n", "  Downloading thinc-8.3.4-cp312-cp312-win_amd64.whl.metadata (15 kB)\n", "Collecting wasabi<1.2.0,>=0.9.1 (from spacy)\n", "  Downloading wasabi-1.1.3-py3-none-any.whl.metadata (28 kB)\n", "Collecting srsly<3.0.0,>=2.4.3 (from spacy)\n", "  Downloading srsly-2.5.1-cp312-cp312-win_amd64.whl.metadata (20 kB)\n", "Collecting catalogue<2.1.0,>=2.0.6 (from spacy)\n", "  Downloading catalogue-2.0.10-py3-none-any.whl.metadata (14 kB)\n", "Collecting weasel<0.5.0,>=0.1.0 (from spacy)\n", "  Downloading weasel-0.4.1-py3-none-any.whl.metadata (4.6 kB)\n", "Collecting typer<1.0.0,>=0.3.0 (from spacy)\n", "  Downloading typer-0.15.2-py3-none-any.whl.metadata (15 kB)\n", "Requirement already satisfied: pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from spacy) (2.10.6)\n", "Collecting langcodes<4.0.0,>=3.2.0 (from spacy)\n", "  Downloading langcodes-3.5.0-py3-none-any.whl.metadata (29 kB)\n", "Requirement already satisfied: soupsieve>1.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from beautifulsoup4) (2.6)\n", "Collecting trio~=0.17 (from selenium)\n", "  Downloading trio-0.29.0-py3-none-any.whl.metadata (8.5 kB)\n", "Collecting trio-websocket~=0.9 (from selenium)\n", "  Downloading trio_websocket-0.12.2-py3-none-any.whl.metadata (5.1 kB)\n", "Requirement already satisfied: certifi>=2021.10.8 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from selenium) (2025.1.31)\n", "Collecting simple<PERSON><PERSON> (from yahoo-finance)\n", "  Downloading simplejson-3.20.1-cp312-cp312-win_amd64.whl.metadata (3.4 kB)\n", "Collecting mlflow-skinny==2.21.0 (from mlflow)\n", "  Downloading mlflow_skinny-2.21.0-py3-none-any.whl.metadata (31 kB)\n", "Requirement already satisfied: Flask<4 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from mlflow) (3.1.0)\n", "Collecting alembic!=1.10.0,<2 (from mlflow)\n", "  Downloading alembic-1.15.1-py3-none-any.whl.metadata (7.2 kB)\n", "Collecting docker<8,>=4.0.0 (from mlflow)\n", "  Downloading docker-7.1.0-py3-none-any.whl.metadata (3.8 kB)\n", "Collecting graphene<4 (from mlflow)\n", "  Downloading graphene-3.4.3-py2.py3-none-any.whl.metadata (6.9 kB)\n", "Collecting markdown<4,>=3.3 (from mlflow)\n", "  Downloading Markdown-3.7-py3-none-any.whl.metadata (7.0 kB)\n", "Requirement already satisfied: sqlalchemy<3,>=1.4.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from mlflow) (2.0.38)\n", "Collecting waitress<4 (from mlflow)\n", "  Downloading waitress-3.0.2-py3-none-any.whl.metadata (5.8 kB)\n", "Requirement already satisfied: cachetools<6,>=5.0.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from mlflow-skinny==2.21.0->mlflow) (5.5.2)\n", "Collecting databricks-sdk<1,>=0.20.0 (from mlflow-skinny==2.21.0->mlflow)\n", "  Downloading databricks_sdk-0.46.0-py3-none-any.whl.metadata (38 kB)\n", "Collecting fastapi<1 (from mlflow-skinny==2.21.0->mlflow)\n", "  Downloading fastapi-0.115.11-py3-none-any.whl.metadata (27 kB)\n", "Collecting gitpython<4,>=3.1.9 (from mlflow-skinny==2.21.0->mlflow)\n", "  Downloading GitPython-3.1.44-py3-none-any.whl.metadata (13 kB)\n", "Requirement already satisfied: importlib_metadata!=4.7.0,<9,>=3.7.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from mlflow-skinny==2.21.0->mlflow) (8.6.1)\n", "Requirement already satisfied: opentelemetry-api<3,>=1.9.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from mlflow-skinny==2.21.0->mlflow) (1.31.0)\n", "Collecting opentelemetry-sdk<3,>=1.9.0 (from mlflow-skinny==2.21.0->mlflow)\n", "  Downloading opentelemetry_sdk-1.31.0-py3-none-any.whl.metadata (1.6 kB)\n", "Collecting sqlparse<1,>=0.4.0 (from mlflow-skinny==2.21.0->mlflow)\n", "  Downloading sqlparse-0.5.3-py3-none-any.whl.metadata (3.9 kB)\n", "Requirement already satisfied: uvicorn<1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from mlflow-skinny==2.21.0->mlflow) (0.34.0)\n", "Collecting cma>=2.6.0 (from nevergrad)\n", "  Downloading cma-4.0.0-py3-none-any.whl.metadata (8.0 kB)\n", "Collecting bayesian-optimization==1.4.0 (from nevergrad)\n", "  Downloading bayesian_optimization-1.4.0-py3-none-any.whl.metadata (469 bytes)\n", "Collecting colorama==0.4.0 (from nevergrad)\n", "  Downloading colorama-0.4.0-py2.py3-none-any.whl.metadata (13 kB)\n", "Collecting directsearch (from nevergrad)\n", "  Downloading directsearch-1.0.tar.gz (13 kB)\n", "  Preparing metadata (setup.py): started\n", "  Preparing metadata (setup.py): finished with status 'done'\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from aiohttp<4,>=3.8.3->alpaca-trade-api) (2.4.6)\n", "Requirement already satisfied: attrs>=17.3.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from aiohttp<4,>=3.8.3->alpaca-trade-api) (25.1.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from aiohttp<4,>=3.8.3->alpaca-trade-api) (6.1.0)\n", "Requirement already satisfied: propcache>=0.2.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from aiohttp<4,>=3.8.3->alpaca-trade-api) (0.2.1)\n", "Requirement already satisfied: yarl<2.0,>=1.17.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from aiohttp<4,>=3.8.3->alpaca-trade-api) (1.18.3)\n", "Collecting <PERSON><PERSON> (from alembic!=1.10.0,<2->mlflow)\n", "  Downloading Mako-1.3.9-py3-none-any.whl.metadata (2.9 kB)\n", "Requirement already satisfied: wheel<1.0,>=0.23.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from astunparse>=1.6.0->tensorflow) (0.45.1)\n", "Collecting stanio<2.0.0,>=0.4.0 (from cmdstanpy>=1.0.4->prophet)\n", "  Downloading stanio-0.5.1-py3-none-any.whl.metadata (1.6 kB)\n", "Requirement already satisfied: pywin32>=304 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from docker<8,>=4.0.0->mlflow) (308)\n", "Requirement already satisfied: Werkzeug>=3.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from Flask<4->mlflow) (3.1.3)\n", "Requirement already satisfied: itsdangerous>=2.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from Flask<4->mlflow) (2.2.0)\n", "Requirement already satisfied: blinker>=1.9 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from Flask<4->mlflow) (1.9.0)\n", "Collecting graphql-core<3.3,>=3.1 (from graphene<4->mlflow)\n", "  Downloading graphql_core-3.2.6-py3-none-any.whl.metadata (11 kB)\n", "Collecting graphql-relay<3.3,>=3.1 (from graphene<4->mlflow)\n", "  Downloading graphql_relay-3.2.0-py3-none-any.whl.metadata (12 kB)\n", "Requirement already satisfied: MarkupSafe>=2.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from jinja2->torch) (3.0.2)\n", "Requirement already satisfied: rich in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from keras>=3.5.0->tensorflow) (13.9.4)\n", "Collecting namex (from keras>=3.5.0->tensorflow)\n", "  Downloading namex-0.0.8-py3-none-any.whl.metadata (246 bytes)\n", "Collecting optree (from keras>=3.5.0->tensorflow)\n", "  Downloading optree-0.14.1-cp312-cp312-win_amd64.whl.metadata (50 kB)\n", "Collecting language-data>=1.2 (from langcodes<4.0.0,>=3.2.0->spacy)\n", "  Downloading language_data-1.3.0-py3-none-any.whl.metadata (4.3 kB)\n", "Requirement already satisfied: annotated-types>=0.6.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4->spacy) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4->spacy) (2.27.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests<3,>=2.21.0->tensorflow) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests<3,>=2.21.0->tensorflow) (3.10)\n", "Requirement already satisfied: greenlet!=0.4.17 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from sqlalchemy<3,>=1.4.0->mlflow) (3.1.1)\n", "Collecting tensorboard-data-server<0.8.0,>=0.7.0 (from tensorboard~=2.19.0->tensorflow)\n", "  Downloading tensorboard_data_server-0.7.2-py3-none-any.whl.metadata (1.1 kB)\n", "Collecting blis<1.3.0,>=1.2.0 (from thinc<8.4.0,>=8.3.4->spacy)\n", "  Downloading blis-1.2.0-cp312-cp312-win_amd64.whl.metadata (7.9 kB)\n", "Collecting confection<1.0.0,>=0.0.1 (from thinc<8.4.0,>=8.3.4->spacy)\n", "  Downloading confection-0.1.5-py3-none-any.whl.metadata (19 kB)\n", "Collecting sortedcontainers (from trio~=0.17->selenium)\n", "  Downloading sortedcontainers-2.4.0-py2.py3-none-any.whl.metadata (10 kB)\n", "Collecting outcome (from trio~=0.17->selenium)\n", "  Downloading outcome-1.3.0.post0-py2.py3-none-any.whl.metadata (2.6 kB)\n", "Requirement already satisfied: sniffio>=1.3.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from trio~=0.17->selenium) (1.3.1)\n", "Requirement already satisfied: cffi>=1.14 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from trio~=0.17->selenium) (1.17.1)\n", "Collecting wsproto>=0.14 (from trio-websocket~=0.9->selenium)\n", "  Downloading wsproto-1.2.0-py3-none-any.whl.metadata (5.6 kB)\n", "Collecting shellingham>=1.3.0 (from typer<1.0.0,>=0.3.0->spacy)\n", "  Downloading shellingham-1.5.4-py2.py3-none-any.whl.metadata (3.5 kB)\n", "Collecting PySocks!=1.5.7,<2.0,>=1.5.6 (from urllib3[socks]<3,>=1.26->selenium)\n", "  Downloading PySocks-1.7.1-py3-none-any.whl.metadata (13 kB)\n", "INFO: pip is looking at multiple versions of wasabi to determine which version is compatible with other requirements. This could take a while.\n", "Collecting wasabi<1.2.0,>=0.9.1 (from spacy)\n", "  Downloading wasabi-1.1.2-py3-none-any.whl.metadata (28 kB)\n", "  Downloading wasabi-1.1.1-py3-none-any.whl.metadata (28 kB)\n", "  Downloading wasabi-0.10.1-py3-none-any.whl.metadata (28 kB)\n", "Collecting cloudpathlib<1.0.0,>=0.7.0 (from weasel<0.5.0,>=0.1.0->spacy)\n", "  Downloading cloudpathlib-0.21.0-py3-none-any.whl.metadata (14 kB)\n", "Collecting smart-open<8.0.0,>=5.2.1 (from weasel<0.5.0,>=0.1.0->spacy)\n", "  Downloading smart_open-7.1.0-py3-none-any.whl.metadata (24 kB)\n", "Requirement already satisfied: jsonschema-specifications>=2023.03.6 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from jsonschema->ray[rllib]) (2024.10.1)\n", "Requirement already satisfied: referencing>=0.28.4 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from jsonschema->ray[rllib]) (0.36.2)\n", "Requirement already satisfied: rpds-py>=0.7.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from jsonschema->ray[rllib]) (0.22.3)\n", "Requirement already satisfied: pycparser in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from cffi>=1.14->trio~=0.17->selenium) (2.22)\n", "Requirement already satisfied: google-auth~=2.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from databricks-sdk<1,>=0.20.0->mlflow-skinny==2.21.0->mlflow) (2.38.0)\n", "Requirement already satisfied: starlette<0.47.0,>=0.40.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from fastapi<1->mlflow-skinny==2.21.0->mlflow) (0.46.1)\n", "Collecting gitdb<5,>=4.0.1 (from gitpython<4,>=3.1.9->mlflow-skinny==2.21.0->mlflow)\n", "  Downloading gitdb-4.0.12-py3-none-any.whl.metadata (1.2 kB)\n", "Requirement already satisfied: zipp>=3.20 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from importlib_metadata!=4.7.0,<9,>=3.7.0->mlflow-skinny==2.21.0->mlflow) (3.21.0)\n", "Collecting marisa-trie>=1.1.0 (from language-data>=1.2->langcodes<4.0.0,>=3.2.0->spacy)\n", "  Downloading marisa_trie-1.2.1-cp312-cp312-win_amd64.whl.metadata (9.3 kB)\n", "Requirement already satisfied: deprecated>=1.2.6 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from opentelemetry-api<3,>=1.9.0->mlflow-skinny==2.21.0->mlflow) (1.2.18)\n", "Collecting opentelemetry-semantic-conventions==0.52b0 (from opentelemetry-sdk<3,>=1.9.0->mlflow-skinny==2.21.0->mlflow)\n", "  Downloading opentelemetry_semantic_conventions-0.52b0-py3-none-any.whl.metadata (2.5 kB)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from rich->keras>=3.5.0->tensorflow) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from rich->keras>=3.5.0->tensorflow) (2.19.1)\n", "Requirement already satisfied: h11>=0.8 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from uvicorn<1->mlflow-skinny==2.21.0->mlflow) (0.14.0)\n", "Collecting smmap<6,>=3.0.1 (from gitdb<5,>=4.0.1->gitpython<4,>=3.1.9->mlflow-skinny==2.21.0->mlflow)\n", "  Downloading smmap-5.0.2-py3-none-any.whl.metadata (4.3 kB)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==2.21.0->mlflow) (0.4.1)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==2.21.0->mlflow) (4.9)\n", "Requirement already satisfied: mdurl~=0.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from markdown-it-py>=2.2.0->rich->keras>=3.5.0->tensorflow) (0.1.2)\n", "Requirement already satisfied: anyio<5,>=3.6.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from starlette<0.47.0,>=0.40.0->fastapi<1->mlflow-skinny==2.21.0->mlflow) (4.8.0)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.4.6 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pyasn1-modules>=0.2.1->google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==2.21.0->mlflow) (0.6.1)\n", "Downloading tensorflow-2.19.0-cp312-cp312-win_amd64.whl (376.0 MB)\n", "   ---------------------------------------- 0.0/376.0 MB ? eta -:--:--\n", "   ---------------------------------------- 1.3/376.0 MB 7.4 MB/s eta 0:00:51\n", "   ---------------------------------------- 3.1/376.0 MB 8.4 MB/s eta 0:00:45\n", "    --------------------------------------- 5.8/376.0 MB 9.8 MB/s eta 0:00:38\n", "    --------------------------------------- 8.9/376.0 MB 11.3 MB/s eta 0:00:33\n", "   - -------------------------------------- 13.4/376.0 MB 13.8 MB/s eta 0:00:27\n", "   - -------------------------------------- 16.5/376.0 MB 13.7 MB/s eta 0:00:27\n", "   -- ------------------------------------- 22.8/376.0 MB 16.2 MB/s eta 0:00:22\n", "   --- ------------------------------------ 29.4/376.0 MB 18.6 MB/s eta 0:00:19\n", "   --- ------------------------------------ 32.0/376.0 MB 17.7 MB/s eta 0:00:20\n", "   --- ------------------------------------ 34.1/376.0 MB 16.9 MB/s eta 0:00:21\n", "   --- ------------------------------------ 37.0/376.0 MB 16.7 MB/s eta 0:00:21\n", "   ---- ----------------------------------- 39.1/376.0 MB 16.1 MB/s eta 0:00:21\n", "   ---- ----------------------------------- 41.4/376.0 MB 15.8 MB/s eta 0:00:22\n", "   ---- ----------------------------------- 43.8/376.0 MB 15.5 MB/s eta 0:00:22\n", "   ---- ----------------------------------- 46.1/376.0 MB 15.2 MB/s eta 0:00:22\n", "   ----- ---------------------------------- 48.8/376.0 MB 14.9 MB/s eta 0:00:22\n", "   ----- ---------------------------------- 51.4/376.0 MB 14.8 MB/s eta 0:00:22\n", "   ----- ---------------------------------- 53.2/376.0 MB 14.4 MB/s eta 0:00:23\n", "   ----- ---------------------------------- 55.1/376.0 MB 14.1 MB/s eta 0:00:23\n", "   ------ --------------------------------- 56.9/376.0 MB 13.9 MB/s eta 0:00:23\n", "   ------ --------------------------------- 59.0/376.0 MB 13.7 MB/s eta 0:00:24\n", "   ------ --------------------------------- 60.8/376.0 MB 13.5 MB/s eta 0:00:24\n", "   ------ --------------------------------- 62.9/376.0 MB 13.3 MB/s eta 0:00:24\n", "   ------ --------------------------------- 65.0/376.0 MB 13.2 MB/s eta 0:00:24\n", "   ------- -------------------------------- 66.8/376.0 MB 13.0 MB/s eta 0:00:24\n", "   ------- -------------------------------- 68.9/376.0 MB 12.9 MB/s eta 0:00:24\n", "   ------- -------------------------------- 70.8/376.0 MB 12.8 MB/s eta 0:00:24\n", "   ------- -------------------------------- 72.9/376.0 MB 12.7 MB/s eta 0:00:24\n", "   ------- -------------------------------- 75.0/376.0 MB 12.6 MB/s eta 0:00:24\n", "   -------- ------------------------------- 77.1/376.0 MB 12.5 MB/s eta 0:00:24\n", "   -------- ------------------------------- 79.2/376.0 MB 12.4 MB/s eta 0:00:24\n", "   -------- ------------------------------- 81.3/376.0 MB 12.3 MB/s eta 0:00:24\n", "   -------- ------------------------------- 83.4/376.0 MB 12.3 MB/s eta 0:00:24\n", "   --------- ------------------------------ 85.5/376.0 MB 12.2 MB/s eta 0:00:24\n", "   --------- ------------------------------ 87.6/376.0 MB 12.2 MB/s eta 0:00:24\n", "   --------- ------------------------------ 89.9/376.0 MB 12.2 MB/s eta 0:00:24\n", "   --------- ------------------------------ 92.3/376.0 MB 12.1 MB/s eta 0:00:24\n", "   ---------- ----------------------------- 94.4/376.0 MB 12.1 MB/s eta 0:00:24\n", "   ---------- ----------------------------- 96.2/376.0 MB 12.0 MB/s eta 0:00:24\n", "   ---------- ----------------------------- 97.5/376.0 MB 11.9 MB/s eta 0:00:24\n", "   ---------- ----------------------------- 98.3/376.0 MB 11.8 MB/s eta 0:00:24\n", "   ---------- ----------------------------- 98.8/376.0 MB 11.5 MB/s eta 0:00:25\n", "   ---------- ----------------------------- 98.8/376.0 MB 11.5 MB/s eta 0:00:25\n", "   ---------- ----------------------------- 98.8/376.0 MB 11.5 MB/s eta 0:00:25\n", "   ---------- ----------------------------- 99.1/376.0 MB 10.9 MB/s eta 0:00:26\n", "   ---------- ----------------------------- 99.1/376.0 MB 10.9 MB/s eta 0:00:26\n", "   ---------- ----------------------------- 99.1/376.0 MB 10.9 MB/s eta 0:00:26\n", "   ---------- ----------------------------- 99.4/376.0 MB 10.1 MB/s eta 0:00:28\n", "   ---------- ----------------------------- 99.4/376.0 MB 10.1 MB/s eta 0:00:28\n", "   ---------- ----------------------------- 99.6/376.0 MB 9.7 MB/s eta 0:00:29\n", "   ---------- ----------------------------- 99.6/376.0 MB 9.7 MB/s eta 0:00:29\n", "   ---------- ----------------------------- 99.9/376.0 MB 9.4 MB/s eta 0:00:30\n", "   ---------- ----------------------------- 100.1/376.0 MB 9.2 MB/s eta 0:00:31\n", "   ---------- ----------------------------- 100.1/376.0 MB 9.2 MB/s eta 0:00:31\n", "   ---------- ----------------------------- 100.4/376.0 MB 9.0 MB/s eta 0:00:31\n", "   ---------- ----------------------------- 100.9/376.0 MB 8.7 MB/s eta 0:00:32\n", "   ---------- ----------------------------- 100.9/376.0 MB 8.7 MB/s eta 0:00:32\n", "   ---------- ----------------------------- 101.4/376.0 MB 8.5 MB/s eta 0:00:33\n", "   ---------- ----------------------------- 101.7/376.0 MB 8.4 MB/s eta 0:00:33\n", "   ---------- ----------------------------- 102.0/376.0 MB 8.3 MB/s eta 0:00:34\n", "   ---------- ----------------------------- 102.5/376.0 MB 8.1 MB/s eta 0:00:34\n", "   ---------- ----------------------------- 102.8/376.0 MB 8.0 MB/s eta 0:00:34\n", "   ---------- ----------------------------- 103.0/376.0 MB 8.0 MB/s eta 0:00:35\n", "   ----------- ---------------------------- 103.5/376.0 MB 7.8 MB/s eta 0:00:35\n", "   ----------- ---------------------------- 104.1/376.0 MB 7.7 MB/s eta 0:00:36\n", "   ----------- ---------------------------- 104.3/376.0 MB 7.7 MB/s eta 0:00:36\n", "   ----------- ---------------------------- 105.1/376.0 MB 7.6 MB/s eta 0:00:36\n", "   ----------- ---------------------------- 105.4/376.0 MB 7.5 MB/s eta 0:00:36\n", "   ----------- ---------------------------- 105.9/376.0 MB 7.4 MB/s eta 0:00:37\n", "   ----------- ---------------------------- 106.4/376.0 MB 7.4 MB/s eta 0:00:37\n", "   ----------- ---------------------------- 107.0/376.0 MB 7.3 MB/s eta 0:00:37\n", "   ----------- ---------------------------- 107.7/376.0 MB 7.2 MB/s eta 0:00:38\n", "   ----------- ---------------------------- 108.3/376.0 MB 7.2 MB/s eta 0:00:38\n", "   ----------- ---------------------------- 108.8/376.0 MB 7.1 MB/s eta 0:00:38\n", "   ----------- ---------------------------- 109.6/376.0 MB 7.0 MB/s eta 0:00:38\n", "   ----------- ---------------------------- 110.1/376.0 MB 7.0 MB/s eta 0:00:39\n", "   ----------- ---------------------------- 110.9/376.0 MB 6.9 MB/s eta 0:00:39\n", "   ----------- ---------------------------- 111.4/376.0 MB 6.9 MB/s eta 0:00:39\n", "   ----------- ---------------------------- 112.2/376.0 MB 6.8 MB/s eta 0:00:39\n", "   ------------ --------------------------- 113.0/376.0 MB 6.8 MB/s eta 0:00:39\n", "   ------------ --------------------------- 113.8/376.0 MB 6.8 MB/s eta 0:00:39\n", "   ------------ --------------------------- 114.6/376.0 MB 6.7 MB/s eta 0:00:39\n", "   ------------ --------------------------- 115.3/376.0 MB 6.7 MB/s eta 0:00:39\n", "   ------------ --------------------------- 116.1/376.0 MB 6.7 MB/s eta 0:00:39\n", "   ------------ --------------------------- 117.2/376.0 MB 6.6 MB/s eta 0:00:39\n", "   ------------ --------------------------- 118.2/376.0 MB 6.6 MB/s eta 0:00:39\n", "   ------------ --------------------------- 119.3/376.0 MB 6.6 MB/s eta 0:00:39\n", "   ------------ --------------------------- 120.1/376.0 MB 6.5 MB/s eta 0:00:40\n", "   ------------ --------------------------- 121.4/376.0 MB 6.6 MB/s eta 0:00:39\n", "   ------------- -------------------------- 122.7/376.0 MB 6.6 MB/s eta 0:00:39\n", "   ------------- -------------------------- 124.5/376.0 MB 6.6 MB/s eta 0:00:39\n", "   ------------- -------------------------- 126.1/376.0 MB 6.6 MB/s eta 0:00:38\n", "   ------------- -------------------------- 128.2/376.0 MB 6.6 MB/s eta 0:00:38\n", "   ------------- -------------------------- 130.3/376.0 MB 6.6 MB/s eta 0:00:37\n", "   -------------- ------------------------- 132.4/376.0 MB 6.7 MB/s eta 0:00:37\n", "   -------------- ------------------------- 134.7/376.0 MB 6.7 MB/s eta 0:00:36\n", "   -------------- ------------------------- 137.4/376.0 MB 6.8 MB/s eta 0:00:36\n", "   -------------- ------------------------- 139.5/376.0 MB 6.8 MB/s eta 0:00:35\n", "   --------------- ------------------------ 142.1/376.0 MB 6.9 MB/s eta 0:00:35\n", "   --------------- ------------------------ 145.0/376.0 MB 6.9 MB/s eta 0:00:34\n", "   --------------- ------------------------ 148.1/376.0 MB 7.0 MB/s eta 0:00:33\n", "   ---------------- ----------------------- 151.3/376.0 MB 7.1 MB/s eta 0:00:32\n", "   ---------------- ----------------------- 154.7/376.0 MB 7.2 MB/s eta 0:00:31\n", "   ---------------- ----------------------- 158.3/376.0 MB 7.3 MB/s eta 0:00:30\n", "   ----------------- ---------------------- 162.5/376.0 MB 7.4 MB/s eta 0:00:29\n", "   ----------------- ---------------------- 167.0/376.0 MB 7.5 MB/s eta 0:00:28\n", "   ------------------ --------------------- 171.4/376.0 MB 7.7 MB/s eta 0:00:27\n", "   ------------------ --------------------- 176.2/376.0 MB 7.8 MB/s eta 0:00:26\n", "   ------------------- -------------------- 178.8/376.0 MB 7.8 MB/s eta 0:00:26\n", "   ------------------- -------------------- 180.9/376.0 MB 7.9 MB/s eta 0:00:25\n", "   ------------------- -------------------- 183.5/376.0 MB 7.9 MB/s eta 0:00:25\n", "   ------------------- -------------------- 186.1/376.0 MB 7.9 MB/s eta 0:00:24\n", "   -------------------- ------------------- 188.7/376.0 MB 8.0 MB/s eta 0:00:24\n", "   -------------------- ------------------- 191.4/376.0 MB 8.0 MB/s eta 0:00:24\n", "   -------------------- ------------------- 193.7/376.0 MB 8.1 MB/s eta 0:00:23\n", "   -------------------- ------------------- 196.1/376.0 MB 8.1 MB/s eta 0:00:23\n", "   --------------------- ------------------ 197.9/376.0 MB 8.1 MB/s eta 0:00:23\n", "   --------------------- ------------------ 200.0/376.0 MB 8.1 MB/s eta 0:00:22\n", "   --------------------- ------------------ 202.1/376.0 MB 8.1 MB/s eta 0:00:22\n", "   --------------------- ------------------ 204.2/376.0 MB 8.1 MB/s eta 0:00:22\n", "   --------------------- ------------------ 206.0/376.0 MB 8.1 MB/s eta 0:00:21\n", "   ---------------------- ----------------- 208.4/376.0 MB 8.2 MB/s eta 0:00:21\n", "   ---------------------- ----------------- 210.2/376.0 MB 8.2 MB/s eta 0:00:21\n", "   ---------------------- ----------------- 212.3/376.0 MB 8.2 MB/s eta 0:00:20\n", "   ---------------------- ----------------- 214.4/376.0 MB 8.2 MB/s eta 0:00:20\n", "   ----------------------- ---------------- 216.8/376.0 MB 8.2 MB/s eta 0:00:20\n", "   ----------------------- ---------------- 218.9/376.0 MB 8.2 MB/s eta 0:00:20\n", "   ----------------------- ---------------- 221.0/376.0 MB 8.2 MB/s eta 0:00:19\n", "   ----------------------- ---------------- 223.3/376.0 MB 8.3 MB/s eta 0:00:19\n", "   ------------------------ --------------- 225.7/376.0 MB 8.3 MB/s eta 0:00:19\n", "   ------------------------ --------------- 226.8/376.0 MB 8.3 MB/s eta 0:00:19\n", "   ------------------------ --------------- 229.1/376.0 MB 8.3 MB/s eta 0:00:18\n", "   ------------------------ --------------- 231.2/376.0 MB 8.3 MB/s eta 0:00:18\n", "   ------------------------ --------------- 233.6/376.0 MB 8.3 MB/s eta 0:00:18\n", "   ------------------------- -------------- 235.4/376.0 MB 8.3 MB/s eta 0:00:17\n", "   ------------------------- -------------- 237.8/376.0 MB 8.4 MB/s eta 0:00:17\n", "   ------------------------- -------------- 240.1/376.0 MB 8.4 MB/s eta 0:00:17\n", "   ------------------------- -------------- 242.0/376.0 MB 8.4 MB/s eta 0:00:16\n", "   ------------------------- -------------- 244.3/376.0 MB 8.4 MB/s eta 0:00:16\n", "   -------------------------- ------------- 247.2/376.0 MB 8.4 MB/s eta 0:00:16\n", "   -------------------------- ------------- 249.6/376.0 MB 8.4 MB/s eta 0:00:15\n", "   -------------------------- ------------- 251.7/376.0 MB 8.5 MB/s eta 0:00:15\n", "   --------------------------- ------------ 254.0/376.0 MB 8.5 MB/s eta 0:00:15\n", "   --------------------------- ------------ 256.6/376.0 MB 8.5 MB/s eta 0:00:15\n", "   --------------------------- ------------ 259.0/376.0 MB 8.5 MB/s eta 0:00:14\n", "   --------------------------- ------------ 260.6/376.0 MB 8.5 MB/s eta 0:00:14\n", "   --------------------------- ------------ 261.9/376.0 MB 8.5 MB/s eta 0:00:14\n", "   ---------------------------- ----------- 264.0/376.0 MB 8.4 MB/s eta 0:00:14\n", "   ---------------------------- ----------- 266.3/376.0 MB 8.3 MB/s eta 0:00:14\n", "   ---------------------------- ----------- 268.7/376.0 MB 8.2 MB/s eta 0:00:14\n", "   ---------------------------- ----------- 271.3/376.0 MB 8.1 MB/s eta 0:00:13\n", "   ----------------------------- ---------- 273.7/376.0 MB 8.1 MB/s eta 0:00:13\n", "   ----------------------------- ---------- 276.3/376.0 MB 8.1 MB/s eta 0:00:13\n", "   ----------------------------- ---------- 278.9/376.0 MB 8.1 MB/s eta 0:00:13\n", "   ----------------------------- ---------- 281.5/376.0 MB 8.1 MB/s eta 0:00:12\n", "   ------------------------------ --------- 284.7/376.0 MB 8.1 MB/s eta 0:00:12\n", "   ------------------------------ --------- 287.0/376.0 MB 8.1 MB/s eta 0:00:12\n", "   ------------------------------ --------- 289.7/376.0 MB 8.1 MB/s eta 0:00:11\n", "   ------------------------------- -------- 292.6/376.0 MB 8.1 MB/s eta 0:00:11\n", "   ------------------------------- -------- 295.2/376.0 MB 8.1 MB/s eta 0:00:10\n", "   ------------------------------- -------- 298.1/376.0 MB 8.1 MB/s eta 0:00:10\n", "   -------------------------------- ------- 300.9/376.0 MB 8.2 MB/s eta 0:00:10\n", "   -------------------------------- ------- 303.8/376.0 MB 8.2 MB/s eta 0:00:09\n", "   -------------------------------- ------- 306.4/376.0 MB 8.2 MB/s eta 0:00:09\n", "   -------------------------------- ------- 309.6/376.0 MB 8.2 MB/s eta 0:00:09\n", "   --------------------------------- ------ 312.7/376.0 MB 8.3 MB/s eta 0:00:08\n", "   --------------------------------- ------ 315.6/376.0 MB 8.3 MB/s eta 0:00:08\n", "   --------------------------------- ------ 316.9/376.0 MB 8.3 MB/s eta 0:00:08\n", "   ---------------------------------- ----- 320.1/376.0 MB 8.3 MB/s eta 0:00:07\n", "   ---------------------------------- ----- 323.2/376.0 MB 8.4 MB/s eta 0:00:07\n", "   ---------------------------------- ----- 326.4/376.0 MB 8.4 MB/s eta 0:00:06\n", "   ----------------------------------- ---- 329.5/376.0 MB 8.4 MB/s eta 0:00:06\n", "   ----------------------------------- ---- 332.4/376.0 MB 8.5 MB/s eta 0:00:06\n", "   ----------------------------------- ---- 335.5/376.0 MB 8.5 MB/s eta 0:00:05\n", "   ------------------------------------ --- 339.0/376.0 MB 8.5 MB/s eta 0:00:05\n", "   ------------------------------------ --- 342.1/376.0 MB 8.6 MB/s eta 0:00:04\n", "   ------------------------------------ --- 345.5/376.0 MB 8.6 MB/s eta 0:00:04\n", "   ------------------------------------- -- 348.7/376.0 MB 8.6 MB/s eta 0:00:04\n", "   ------------------------------------- -- 350.5/376.0 MB 8.6 MB/s eta 0:00:03\n", "   ------------------------------------- -- 352.8/376.0 MB 8.6 MB/s eta 0:00:03\n", "   ------------------------------------- -- 354.7/376.0 MB 8.6 MB/s eta 0:00:03\n", "   ------------------------------------- -- 356.3/376.0 MB 8.6 MB/s eta 0:00:03\n", "   -------------------------------------- - 358.4/376.0 MB 8.7 MB/s eta 0:00:03\n", "   -------------------------------------- - 360.4/376.0 MB 8.7 MB/s eta 0:00:02\n", "   -------------------------------------- - 362.3/376.0 MB 9.5 MB/s eta 0:00:02\n", "   -------------------------------------- - 364.4/376.0 MB 9.9 MB/s eta 0:00:02\n", "   --------------------------------------  366.5/376.0 MB 10.3 MB/s eta 0:00:01\n", "   --------------------------------------  368.6/376.0 MB 10.5 MB/s eta 0:00:01\n", "   --------------------------------------  368.8/376.0 MB 10.5 MB/s eta 0:00:01\n", "   --------------------------------------  370.1/376.0 MB 10.6 MB/s eta 0:00:01\n", "   --------------------------------------  371.5/376.0 MB 10.7 MB/s eta 0:00:01\n", "   --------------------------------------  373.3/376.0 MB 10.8 MB/s eta 0:00:01\n", "   --------------------------------------  374.9/376.0 MB 11.0 MB/s eta 0:00:01\n", "   --------------------------------------  375.9/376.0 MB 11.0 MB/s eta 0:00:01\n", "   --------------------------------------  375.9/376.0 MB 11.0 MB/s eta 0:00:01\n", "   --------------------------------------  375.9/376.0 MB 11.0 MB/s eta 0:00:01\n", "   --------------------------------------  375.9/376.0 MB 11.0 MB/s eta 0:00:01\n", "   --------------------------------------- 376.0/376.0 MB 10.7 MB/s eta 0:00:00\n", "Downloading numpy-2.1.3-cp312-cp312-win_amd64.whl (12.6 MB)\n", "   ---------------------------------------- 0.0/12.6 MB ? eta -:--:--\n", "   ----- ---------------------------------- 1.6/12.6 MB 7.6 MB/s eta 0:00:02\n", "   ---------- ----------------------------- 3.4/12.6 MB 8.0 MB/s eta 0:00:02\n", "   ---------------- ----------------------- 5.2/12.6 MB 8.4 MB/s eta 0:00:01\n", "   --------------------- ------------------ 6.8/12.6 MB 8.4 MB/s eta 0:00:01\n", "   --------------------------- ------------ 8.7/12.6 MB 8.4 MB/s eta 0:00:01\n", "   --------------------------------- ------ 10.5/12.6 MB 8.5 MB/s eta 0:00:01\n", "   -------------------------------------- - 12.1/12.6 MB 8.6 MB/s eta 0:00:01\n", "   ---------------------------------------- 12.6/12.6 MB 8.2 MB/s eta 0:00:00\n", "Downloading xgboost-3.0.0-py3-none-win_amd64.whl (150.0 MB)\n", "   ---------------------------------------- 0.0/150.0 MB ? eta -:--:--\n", "   ---------------------------------------- 1.6/150.0 MB 7.6 MB/s eta 0:00:20\n", "    --------------------------------------- 3.1/150.0 MB 8.0 MB/s eta 0:00:19\n", "   - -------------------------------------- 5.0/150.0 MB 8.4 MB/s eta 0:00:18\n", "   - -------------------------------------- 6.6/150.0 MB 8.2 MB/s eta 0:00:18\n", "   -- ------------------------------------- 8.4/150.0 MB 8.3 MB/s eta 0:00:18\n", "   -- ------------------------------------- 10.2/150.0 MB 8.4 MB/s eta 0:00:17\n", "   --- ------------------------------------ 12.3/150.0 MB 8.5 MB/s eta 0:00:17\n", "   --- ------------------------------------ 13.9/150.0 MB 8.5 MB/s eta 0:00:17\n", "   ---- ----------------------------------- 15.7/150.0 MB 8.5 MB/s eta 0:00:16\n", "   ---- ----------------------------------- 17.3/150.0 MB 8.5 MB/s eta 0:00:16\n", "   ----- ---------------------------------- 19.1/150.0 MB 8.5 MB/s eta 0:00:16\n", "   ----- ---------------------------------- 21.2/150.0 MB 8.6 MB/s eta 0:00:15\n", "   ------ --------------------------------- 23.1/150.0 MB 8.6 MB/s eta 0:00:15\n", "   ------ --------------------------------- 24.9/150.0 MB 8.7 MB/s eta 0:00:15\n", "   ------- -------------------------------- 27.3/150.0 MB 8.8 MB/s eta 0:00:14\n", "   ------- -------------------------------- 29.1/150.0 MB 8.8 MB/s eta 0:00:14\n", "   -------- ------------------------------- 31.2/150.0 MB 8.9 MB/s eta 0:00:14\n", "   -------- ------------------------------- 33.3/150.0 MB 9.0 MB/s eta 0:00:14\n", "   --------- ------------------------------ 35.4/150.0 MB 9.0 MB/s eta 0:00:13\n", "   --------- ------------------------------ 37.2/150.0 MB 9.1 MB/s eta 0:00:13\n", "   ---------- ----------------------------- 39.6/150.0 MB 9.2 MB/s eta 0:00:13\n", "   ----------- ---------------------------- 41.4/150.0 MB 9.2 MB/s eta 0:00:12\n", "   ----------- ---------------------------- 43.5/150.0 MB 9.2 MB/s eta 0:00:12\n", "   ------------ --------------------------- 45.6/150.0 MB 9.3 MB/s eta 0:00:12\n", "   ------------ --------------------------- 48.0/150.0 MB 9.3 MB/s eta 0:00:11\n", "   ------------- -------------------------- 50.3/150.0 MB 9.4 MB/s eta 0:00:11\n", "   ------------- -------------------------- 52.2/150.0 MB 9.4 MB/s eta 0:00:11\n", "   -------------- ------------------------- 54.3/150.0 MB 9.4 MB/s eta 0:00:11\n", "   --------------- ------------------------ 56.6/150.0 MB 9.5 MB/s eta 0:00:10\n", "   --------------- ------------------------ 59.2/150.0 MB 9.5 MB/s eta 0:00:10\n", "   ---------------- ----------------------- 61.3/150.0 MB 9.6 MB/s eta 0:00:10\n", "   ---------------- ----------------------- 62.9/150.0 MB 9.5 MB/s eta 0:00:10\n", "   ----------------- ---------------------- 65.3/150.0 MB 9.5 MB/s eta 0:00:09\n", "   ------------------ --------------------- 67.6/150.0 MB 9.6 MB/s eta 0:00:09\n", "   ------------------ --------------------- 70.0/150.0 MB 9.7 MB/s eta 0:00:09\n", "   ------------------- -------------------- 72.6/150.0 MB 9.7 MB/s eta 0:00:08\n", "   ------------------- -------------------- 75.0/150.0 MB 9.8 MB/s eta 0:00:08\n", "   -------------------- ------------------- 77.6/150.0 MB 9.8 MB/s eta 0:00:08\n", "   --------------------- ------------------ 80.0/150.0 MB 9.9 MB/s eta 0:00:08\n", "   --------------------- ------------------ 82.3/150.0 MB 9.9 MB/s eta 0:00:07\n", "   ---------------------- ----------------- 85.2/150.0 MB 10.0 MB/s eta 0:00:07\n", "   ----------------------- ---------------- 88.3/150.0 MB 10.1 MB/s eta 0:00:07\n", "   ----------------------- ---------------- 89.9/150.0 MB 10.1 MB/s eta 0:00:06\n", "   ------------------------ --------------- 92.5/150.0 MB 10.1 MB/s eta 0:00:06\n", "   ------------------------- -------------- 95.2/150.0 MB 10.2 MB/s eta 0:00:06\n", "   -------------------------- ------------- 98.0/150.0 MB 10.2 MB/s eta 0:00:06\n", "   -------------------------- ------------ 101.2/150.0 MB 10.3 MB/s eta 0:00:05\n", "   --------------------------- ----------- 104.3/150.0 MB 10.4 MB/s eta 0:00:05\n", "   --------------------------- ----------- 107.5/150.0 MB 10.5 MB/s eta 0:00:05\n", "   ---------------------------- ---------- 110.4/150.0 MB 10.6 MB/s eta 0:00:04\n", "   ----------------------------- --------- 113.5/150.0 MB 10.7 MB/s eta 0:00:04\n", "   ------------------------------ -------- 117.2/150.0 MB 10.8 MB/s eta 0:00:04\n", "   ------------------------------- ------- 121.1/150.0 MB 10.9 MB/s eta 0:00:03\n", "   -------------------------------- ------ 124.8/150.0 MB 11.1 MB/s eta 0:00:03\n", "   --------------------------------- ----- 128.2/150.0 MB 11.1 MB/s eta 0:00:02\n", "   ---------------------------------- ---- 131.6/150.0 MB 11.2 MB/s eta 0:00:02\n", "   ---------------------------------- ---- 134.2/150.0 MB 11.3 MB/s eta 0:00:02\n", "   ----------------------------------- --- 136.1/150.0 MB 11.2 MB/s eta 0:00:02\n", "   ----------------------------------- --- 138.1/150.0 MB 11.2 MB/s eta 0:00:02\n", "   ------------------------------------ -- 140.0/150.0 MB 11.2 MB/s eta 0:00:01\n", "   ------------------------------------ -- 142.1/150.0 MB 11.2 MB/s eta 0:00:01\n", "   ------------------------------------- - 144.2/150.0 MB 11.1 MB/s eta 0:00:01\n", "   --------------------------------------  146.3/150.0 MB 11.1 MB/s eta 0:00:01\n", "   --------------------------------------  148.1/150.0 MB 11.1 MB/s eta 0:00:01\n", "   --------------------------------------  149.9/150.0 MB 11.1 MB/s eta 0:00:01\n", "   --------------------------------------- 150.0/150.0 MB 10.9 MB/s eta 0:00:00\n", "Downloading prophet-1.1.6-py3-none-win_amd64.whl (13.3 MB)\n", "   ---------------------------------------- 0.0/13.3 MB ? eta -:--:--\n", "   ------ --------------------------------- 2.1/13.3 MB 10.7 MB/s eta 0:00:02\n", "   ------------- -------------------------- 4.5/13.3 MB 10.8 MB/s eta 0:00:01\n", "   --------------------- ------------------ 7.1/13.3 MB 10.9 MB/s eta 0:00:01\n", "   ----------------------------- ---------- 9.7/13.3 MB 11.4 MB/s eta 0:00:01\n", "   ------------------------------------ --- 12.1/13.3 MB 11.4 MB/s eta 0:00:01\n", "   ---------------------------------------- 13.3/13.3 MB 11.3 MB/s eta 0:00:00\n", "Downloading gymnasium-1.0.0-py3-none-any.whl (958 kB)\n", "   ---------------------------------------- 0.0/958.1 kB ? eta -:--:--\n", "   ---------------------------------------- 958.1/958.1 kB 8.9 MB/s eta 0:00:00\n", "Downloading ormsgpack-1.7.0-cp312-cp312-win_amd64.whl (125 kB)\n", "Downloading stable_baselines3-2.5.0-py3-none-any.whl (183 kB)\n", "Downloading pettingzoo-1.24.3-py3-none-any.whl (847 kB)\n", "   ---------------------------------------- 0.0/847.8 kB ? eta -:--:--\n", "   ---------------------------------------- 847.8/847.8 kB 9.3 MB/s eta 0:00:00\n", "Downloading alpaca_trade_api-3.2.0-py3-none-any.whl (34 kB)\n", "Downloading deprecation-2.1.0-py2.py3-none-any.whl (11 kB)\n", "Downloading PyYAML-6.0.1-cp312-cp312-win_amd64.whl (138 kB)\n", "Downloading textblob-0.19.0-py3-none-any.whl (624 kB)\n", "   ---------------------------------------- 0.0/624.3 kB ? eta -:--:--\n", "   --------------------------------------- 624.3/624.3 kB 11.3 MB/s eta 0:00:00\n", "Downloading spacy-3.8.4-cp312-cp312-win_amd64.whl (11.8 MB)\n", "   ---------------------------------------- 0.0/11.8 MB ? eta -:--:--\n", "   ------- -------------------------------- 2.1/11.8 MB 10.7 MB/s eta 0:00:01\n", "   ---------------- ----------------------- 4.7/11.8 MB 11.0 MB/s eta 0:00:01\n", "   ------------------------ --------------- 7.1/11.8 MB 11.5 MB/s eta 0:00:01\n", "   ------------------------------- -------- 9.2/11.8 MB 11.4 MB/s eta 0:00:01\n", "   ------------------------------------ --- 10.7/11.8 MB 10.3 MB/s eta 0:00:01\n", "   ---------------------------------------- 11.8/11.8 MB 10.1 MB/s eta 0:00:00\n", "Downloading selenium-4.29.0-py3-none-any.whl (9.5 MB)\n", "   ---------------------------------------- 0.0/9.5 MB ? eta -:--:--\n", "   -------- ------------------------------- 2.1/9.5 MB 10.7 MB/s eta 0:00:01\n", "   ------------------- -------------------- 4.7/9.5 MB 10.9 MB/s eta 0:00:01\n", "   ----------------------------- ---------- 7.1/9.5 MB 11.5 MB/s eta 0:00:01\n", "   -------------------------------------- - 9.2/9.5 MB 11.2 MB/s eta 0:00:01\n", "   ---------------------------------------- 9.5/9.5 MB 11.0 MB/s eta 0:00:00\n", "Downloading lxml-5.3.1-cp312-cp312-win_amd64.whl (3.8 MB)\n", "   ---------------------------------------- 0.0/3.8 MB ? eta -:--:--\n", "   ------------------------ --------------- 2.4/3.8 MB 11.2 MB/s eta 0:00:01\n", "   ---------------------------------------- 3.8/3.8 MB 9.9 MB/s eta 0:00:00\n", "Downloading mlflow-2.21.0-py3-none-any.whl (28.2 MB)\n", "   ---------------------------------------- 0.0/28.2 MB ? eta -:--:--\n", "   --- ------------------------------------ 2.6/28.2 MB 12.5 MB/s eta 0:00:03\n", "   ------- -------------------------------- 5.2/28.2 MB 12.3 MB/s eta 0:00:02\n", "   ----------- ---------------------------- 8.1/28.2 MB 12.6 MB/s eta 0:00:02\n", "   --------------- ------------------------ 10.7/28.2 MB 12.9 MB/s eta 0:00:02\n", "   ------------------- -------------------- 13.6/28.2 MB 12.8 MB/s eta 0:00:02\n", "   ---------------------- ----------------- 16.0/28.2 MB 12.7 MB/s eta 0:00:01\n", "   -------------------------- ------------- 18.9/28.2 MB 12.7 MB/s eta 0:00:01\n", "   ------------------------------ --------- 21.2/28.2 MB 12.5 MB/s eta 0:00:01\n", "   --------------------------------- ------ 23.6/28.2 MB 12.4 MB/s eta 0:00:01\n", "   ------------------------------------- -- 26.5/28.2 MB 12.5 MB/s eta 0:00:01\n", "   ---------------------------------------- 28.2/28.2 MB 12.2 MB/s eta 0:00:00\n", "Downloading mlflow_skinny-2.21.0-py3-none-any.whl (6.1 MB)\n", "   ---------------------------------------- 0.0/6.1 MB ? eta -:--:--\n", "   ------------------ --------------------- 2.9/6.1 MB 13.9 MB/s eta 0:00:01\n", "   ---------------------------------- ----- 5.2/6.1 MB 12.7 MB/s eta 0:00:01\n", "   ---------------------------------------- 6.1/6.1 MB 12.5 MB/s eta 0:00:00\n", "Downloading deap-1.4.2-cp312-cp312-win_amd64.whl (109 kB)\n", "Downloading nevergrad-1.0.8-py3-none-any.whl (497 kB)\n", "Downloading bayesian_optimization-1.4.0-py3-none-any.whl (17 kB)\n", "Downloading colorama-0.4.0-py2.py3-none-any.whl (21 kB)\n", "Downloading absl_py-2.1.0-py3-none-any.whl (133 kB)\n", "Downloading alembic-1.15.1-py3-none-any.whl (231 kB)\n", "Downloading astunparse-1.6.3-py2.py3-none-any.whl (12 kB)\n", "Downloading catalogue-2.0.10-py3-none-any.whl (17 kB)\n", "Downloading cloudpickle-3.1.1-py3-none-any.whl (20 kB)\n", "Downloading cma-4.0.0-py3-none-any.whl (283 kB)\n", "Downloading cmdstanpy-1.2.5-py3-none-any.whl (94 kB)\n", "Downloading cymem-2.0.11-cp312-cp312-win_amd64.whl (39 kB)\n", "Downloading docker-7.1.0-py3-none-any.whl (147 kB)\n", "Downloading Farama_Notifications-0.0.4-py3-none-any.whl (2.5 kB)\n", "Downloading flatbuffers-25.2.10-py2.py3-none-any.whl (30 kB)\n", "Downloading gast-0.6.0-py3-none-any.whl (21 kB)\n", "Downloading google_pasta-0.2.0-py3-none-any.whl (57 kB)\n", "Downloading graphene-3.4.3-py2.py3-none-any.whl (114 kB)\n", "Downloading grpcio-1.71.0-cp312-cp312-win_amd64.whl (4.3 MB)\n", "   ---------------------------------------- 0.0/4.3 MB ? eta -:--:--\n", "   ------------------------ --------------- 2.6/4.3 MB 12.6 MB/s eta 0:00:01\n", "   ---------------------------------------- 4.3/4.3 MB 12.9 MB/s eta 0:00:00\n", "Downloading h5py-3.13.0-cp312-cp312-win_amd64.whl (3.0 MB)\n", "   ---------------------------------------- 0.0/3.0 MB ? eta -:--:--\n", "   ----------------- ---------------------- 1.3/3.0 MB 6.1 MB/s eta 0:00:01\n", "   ---------------------------------------- 3.0/3.0 MB 8.6 MB/s eta 0:00:00\n", "Downloading holidays-0.69-py3-none-any.whl (863 kB)\n", "   ---------------------------------------- 0.0/863.9 kB ? eta -:--:--\n", "   --------------------------------------- 863.9/863.9 kB 12.8 MB/s eta 0:00:00\n", "Downloading keras-3.9.0-py3-none-any.whl (1.3 MB)\n", "   ---------------------------------------- 0.0/1.3 MB ? eta -:--:--\n", "   ---------------------------------------- 1.3/1.3 MB 11.5 MB/s eta 0:00:00\n", "Downloading langcodes-3.5.0-py3-none-any.whl (182 kB)\n", "Downloading libclang-18.1.1-py2.py3-none-win_amd64.whl (26.4 MB)\n", "   ---------------------------------------- 0.0/26.4 MB ? eta -:--:--\n", "   ---- ----------------------------------- 2.9/26.4 MB 15.2 MB/s eta 0:00:02\n", "   -------- ------------------------------- 5.8/26.4 MB 14.7 MB/s eta 0:00:02\n", "   ------------- -------------------------- 8.9/26.4 MB 14.6 MB/s eta 0:00:02\n", "   ------------------ --------------------- 12.1/26.4 MB 14.5 MB/s eta 0:00:01\n", "   ---------------------- ----------------- 14.9/26.4 MB 14.2 MB/s eta 0:00:01\n", "   -------------------------- ------------- 17.8/26.4 MB 14.2 MB/s eta 0:00:01\n", "   ------------------------------ --------- 20.4/26.4 MB 14.0 MB/s eta 0:00:01\n", "   ---------------------------------- ----- 23.1/26.4 MB 13.9 MB/s eta 0:00:01\n", "   ---------------------------------------  26.0/26.4 MB 13.9 MB/s eta 0:00:01\n", "   ---------------------------------------- 26.4/26.4 MB 13.6 MB/s eta 0:00:00\n", "Downloading Markdown-3.7-py3-none-any.whl (106 kB)\n", "Downloading ml_dtypes-0.5.1-cp312-cp312-win_amd64.whl (210 kB)\n", "Downloading murmurhash-1.0.12-cp312-cp312-win_amd64.whl (25 kB)\n", "Downloading nltk-3.9.1-py3-none-any.whl (1.5 MB)\n", "   ---------------------------------------- 0.0/1.5 MB ? eta -:--:--\n", "   ---------------------------------------- 1.5/1.5 MB 9.9 MB/s eta 0:00:00\n", "Downloading opt_einsum-3.4.0-py3-none-any.whl (71 kB)\n", "Downloading preshed-3.0.9-cp312-cp312-win_amd64.whl (122 kB)\n", "Downloading protobuf-5.29.4-cp310-abi3-win_amd64.whl (434 kB)\n", "Downloading pyarrow-19.0.1-cp312-cp312-win_amd64.whl (25.3 MB)\n", "   ---------------------------------------- 0.0/25.3 MB ? eta -:--:--\n", "   ---- ----------------------------------- 2.9/25.3 MB 13.9 MB/s eta 0:00:02\n", "   --------- ------------------------------ 5.8/25.3 MB 14.1 MB/s eta 0:00:02\n", "   ------------- -------------------------- 8.4/25.3 MB 13.7 MB/s eta 0:00:02\n", "   ------------------ --------------------- 11.5/25.3 MB 13.9 MB/s eta 0:00:01\n", "   ---------------------- ----------------- 14.4/25.3 MB 13.9 MB/s eta 0:00:01\n", "   -------------------------- ------------- 17.0/25.3 MB 13.8 MB/s eta 0:00:01\n", "   ------------------------------- -------- 19.9/25.3 MB 13.8 MB/s eta 0:00:01\n", "   ------------------------------------ --- 22.8/25.3 MB 13.9 MB/s eta 0:00:01\n", "   ---------------------------------------  25.2/25.3 MB 13.7 MB/s eta 0:00:01\n", "   ---------------------------------------- 25.3/25.3 MB 13.4 MB/s eta 0:00:00\n", "Downloading spacy_legacy-3.0.12-py2.py3-none-any.whl (29 kB)\n", "Downloading spacy_loggers-1.0.5-py3-none-any.whl (22 kB)\n", "Downloading srsly-2.5.1-cp312-cp312-win_amd64.whl (632 kB)\n", "   ---------------------------------------- 0.0/632.6 kB ? eta -:--:--\n", "   --------------------------------------- 632.6/632.6 kB 11.6 MB/s eta 0:00:00\n", "Downloading tensorboard-2.19.0-py3-none-any.whl (5.5 MB)\n", "   ---------------------------------------- 0.0/5.5 MB ? eta -:--:--\n", "   -------------------- ------------------- 2.9/5.5 MB 12.9 MB/s eta 0:00:01\n", "   -------------------------------------- - 5.2/5.5 MB 12.7 MB/s eta 0:00:01\n", "   ---------------------------------------- 5.5/5.5 MB 12.4 MB/s eta 0:00:00\n", "Downloading tensorboardX-2.6.2.2-py2.py3-none-any.whl (101 kB)\n", "Downloading termcolor-2.5.0-py3-none-any.whl (7.8 kB)\n", "Downloading thinc-8.3.4-cp312-cp312-win_amd64.whl (1.5 MB)\n", "   ---------------------------------------- 0.0/1.5 MB ? eta -:--:--\n", "   ---------------------------------------- 1.5/1.5 MB 12.8 MB/s eta 0:00:00\n", "Downloading trio-0.29.0-py3-none-any.whl (492 kB)\n", "Downloading trio_websocket-0.12.2-py3-none-any.whl (21 kB)\n", "Downloading typer-0.15.2-py3-none-any.whl (45 kB)\n", "Downloading urllib3-1.26.20-py2.py3-none-any.whl (144 kB)\n", "Downloading waitress-3.0.2-py3-none-any.whl (56 kB)\n", "Downloading wasabi-0.10.1-py3-none-any.whl (26 kB)\n", "Downloading weasel-0.4.1-py3-none-any.whl (50 kB)\n", "Downloading dm_tree-0.1.9-cp312-cp312-win_amd64.whl (102 kB)\n", "Downloading importlib_resources-6.5.2-py3-none-any.whl (37 kB)\n", "Downloading lz4-4.4.3-cp312-cp312-win_amd64.whl (99 kB)\n", "Downloading ray-2.43.0-cp312-cp312-win_amd64.whl (25.6 MB)\n", "   ---------------------------------------- 0.0/25.6 MB ? eta -:--:--\n", "   ---- ----------------------------------- 2.9/25.6 MB 13.9 MB/s eta 0:00:02\n", "   --------- ------------------------------ 5.8/25.6 MB 14.1 MB/s eta 0:00:02\n", "   ------------- -------------------------- 8.4/25.6 MB 13.3 MB/s eta 0:00:02\n", "   ----------------- ---------------------- 11.0/25.6 MB 13.5 MB/s eta 0:00:02\n", "   -------------------- ------------------- 13.4/25.6 MB 12.9 MB/s eta 0:00:01\n", "   ------------------------- -------------- 16.0/25.6 MB 12.9 MB/s eta 0:00:01\n", "   ----------------------------- ---------- 18.9/25.6 MB 12.9 MB/s eta 0:00:01\n", "   --------------------------------- ------ 21.5/25.6 MB 12.9 MB/s eta 0:00:01\n", "   -------------------------------------- - 24.4/25.6 MB 13.0 MB/s eta 0:00:01\n", "   ---------------------------------------- 25.6/25.6 MB 12.7 MB/s eta 0:00:00\n", "Downloading simplejson-3.20.1-cp312-cp312-win_amd64.whl (75 kB)\n", "Downloading blis-1.2.0-cp312-cp312-win_amd64.whl (6.3 MB)\n", "   ---------------------------------------- 0.0/6.3 MB ? eta -:--:--\n", "   ------------------ --------------------- 2.9/6.3 MB 13.9 MB/s eta 0:00:01\n", "   --------------------------------- ------ 5.2/6.3 MB 13.9 MB/s eta 0:00:01\n", "   ---------------------------------------- 6.3/6.3 MB 13.2 MB/s eta 0:00:00\n", "Downloading cloudpathlib-0.21.0-py3-none-any.whl (52 kB)\n", "Downloading confection-0.1.5-py3-none-any.whl (35 kB)\n", "Downloading databricks_sdk-0.46.0-py3-none-any.whl (677 kB)\n", "   ---------------------------------------- 0.0/677.5 kB ? eta -:--:--\n", "   ---------------------------------------- 677.5/677.5 kB 9.0 MB/s eta 0:00:00\n", "Downloading fastapi-0.115.11-py3-none-any.whl (94 kB)\n", "Downloading GitPython-3.1.44-py3-none-any.whl (207 kB)\n", "Downloading graphql_core-3.2.6-py3-none-any.whl (203 kB)\n", "Downloading graphql_relay-3.2.0-py3-none-any.whl (16 kB)\n", "Downloading language_data-1.3.0-py3-none-any.whl (5.4 MB)\n", "   ---------------------------------------- 0.0/5.4 MB ? eta -:--:--\n", "   ----------------- ---------------------- 2.4/5.4 MB 13.4 MB/s eta 0:00:01\n", "   -------------------------------------- - 5.2/5.4 MB 12.8 MB/s eta 0:00:01\n", "   ---------------------------------------- 5.4/5.4 MB 12.1 MB/s eta 0:00:00\n", "Downloading opentelemetry_sdk-1.31.0-py3-none-any.whl (118 kB)\n", "Downloading opentelemetry_semantic_conventions-0.52b0-py3-none-any.whl (183 kB)\n", "Downloading outcome-1.3.0.post0-py2.py3-none-any.whl (10 kB)\n", "Downloading PySocks-1.7.1-py3-none-any.whl (16 kB)\n", "Downloading shellingham-1.5.4-py2.py3-none-any.whl (9.8 kB)\n", "Downloading smart_open-7.1.0-py3-none-any.whl (61 kB)\n", "Downloading sqlparse-0.5.3-py3-none-any.whl (44 kB)\n", "Downloading stanio-0.5.1-py3-none-any.whl (8.1 kB)\n", "Downloading tensorboard_data_server-0.7.2-py3-none-any.whl (2.4 kB)\n", "Downloading wsproto-1.2.0-py3-none-any.whl (24 kB)\n", "Downloading Mako-1.3.9-py3-none-any.whl (78 kB)\n", "Downloading namex-0.0.8-py3-none-any.whl (5.8 kB)\n", "Downloading optree-0.14.1-cp312-cp312-win_amd64.whl (306 kB)\n", "Downloading sortedcontainers-2.4.0-py2.py3-none-any.whl (29 kB)\n", "Downloading gitdb-4.0.12-py3-none-any.whl (62 kB)\n", "Downloading marisa_trie-1.2.1-cp312-cp312-win_amd64.whl (150 kB)\n", "Downloading smmap-5.0.2-py3-none-any.whl (24 kB)\n", "Building wheels for collected packages: msgpack, yahoo-finance, websockets, directsearch\n", "  Building wheel for msgpack (setup.py): started\n", "  Building wheel for msgpack (setup.py): finished with status 'done'\n", "  Created wheel for msgpack: filename=msgpack-1.0.3-cp312-cp312-win_amd64.whl size=15782 sha256=33bb0988bf134c3f5fd4eb91d372a3ef2be0ceb488981622a58b304591af6f8d\n", "  Stored in directory: c:\\users\\<USER>\\appdata\\local\\pip\\cache\\wheels\\ba\\bd\\3f\\f043e8f634db9c90ae128d631f43ae9990eef01274a63291f9\n", "  Building wheel for yahoo-finance (setup.py): started\n", "  Building wheel for yahoo-finance (setup.py): finished with status 'done'\n", "  Created wheel for yahoo-finance: filename=yahoo_finance-1.4.0-py3-none-any.whl size=7300 sha256=dad7d4c46d566d9bcc9867c7922eb4d02f13ba6a260592d602e1cfcc68b179b0\n", "  Stored in directory: c:\\users\\<USER>\\appdata\\local\\pip\\cache\\wheels\\1a\\cd\\8c\\a3101eeae567561b8d87517bc615b13ed8d92f9b30ce737bdd\n", "  Building wheel for websockets (setup.py): started\n", "  Building wheel for websockets (setup.py): finished with status 'done'\n", "  Created wheel for websockets: filename=websockets-10.4-cp312-cp312-win_amd64.whl size=95082 sha256=5c5db6354a8c73ba9810bdf8a7fd2db6c25096d3aa143c4911a0e61e09e8e828\n", "  Stored in directory: c:\\users\\<USER>\\appdata\\local\\pip\\cache\\wheels\\80\\cf\\6d\\5d7e4c920cb41925a178b2d2621889c520d648bab487b1d7fd\n", "  Building wheel for directsearch (setup.py): started\n", "  Building wheel for directsearch (setup.py): finished with status 'done'\n", "  Created wheel for directsearch: filename=directsearch-1.0-py3-none-any.whl size=16380 sha256=f70e9b4bee67200b293a4fcce5f7ed56c54faf8522fca56ab42266968db03b82\n", "  Stored in directory: c:\\users\\<USER>\\appdata\\local\\pip\\cache\\wheels\\4a\\b2\\40\\2822577662abb6fb1c7793708ac93649343fc6fd7c4e85c9c8\n", "Successfully built msgpack yahoo-finance websockets directsearch\n", "Installing collected packages: wasabi, sortedcontainers, namex, msgpack, libclang, flatbuffers, farama-notifications, cymem, wsproto, websockets, waitress, urllib3, termcolor, tensorboard-data-server, sqlparse, spacy-loggers, spacy-legacy, smmap, smart-open, simplejson, shellingham, pyyaml, PySocks, pyarrow, protobuf, outcome, ormsgpack, optree, opt-einsum, numpy, murmurhash, markdown, marisa-trie, Mako, lz4, lxml, importlib-resources, grpcio, graphql-core, google-pasta, gast, deprecation, colorama, cloudpickle, cloudpathlib, catalogue, astunparse, absl-py, yahoo-finance, trio, tensorboardX, tensorboard, stanio, srsly, preshed, ml-dtypes, language-data, holidays, h5py, gymnasium, graphql-relay, gitdb, dm_tree, deap, cma, blis, alembic, xgboost, typer, trio-websocket, pettingzoo, opentelemetry-semantic-conventions, nltk, langcodes, keras, graphene, gitpython, fastapi, docker, directsearch, databricks-sdk, confection, cmdstanpy, alpaca-trade-api, weasel, thinc, textblob, tensorflow, stable-baselines3, selenium, ray, prophet, opentelemetry-sdk, bayesian-optimization, spacy, nevergrad, mlflow-skinny, mlflow\n", "  Attempting uninstall: urllib3\n", "    Found existing installation: urllib3 2.3.0\n", "    Uninstalling urllib3-2.3.0:\n", "      Successfully uninstalled urllib3-2.3.0\n", "  Attempting uninstall: pyyaml\n", "    Found existing installation: PyYAML 6.0.2\n", "    Uninstalling PyYAML-6.0.2:\n", "      Successfully uninstalled PyYAML-6.0.2\n", "  Attempting uninstall: numpy\n", "    Found existing installation: numpy 2.2.3\n", "    Uninstalling numpy-2.2.3:\n", "      Successfully uninstalled numpy-2.2.3\n", "  Attempting uninstall: colorama\n", "    Found existing installation: colorama 0.4.6\n", "    Uninstalling colorama-0.4.6:\n", "      Successfully uninstalled colorama-0.4.6\n", "Successfully installed Mako-1.3.9 PySocks-1.7.1 absl-py-2.1.0 alembic-1.15.1 alpaca-trade-api-3.2.0 astunparse-1.6.3 bayesian-optimization-1.4.0 blis-1.2.0 catalogue-2.0.10 cloudpathlib-0.21.0 cloudpickle-3.1.1 cma-4.0.0 cmdstanpy-1.2.5 colorama-0.4.0 confection-0.1.5 cymem-2.0.11 databricks-sdk-0.46.0 deap-1.4.2 deprecation-2.1.0 directsearch-1.0 dm_tree-0.1.9 docker-7.1.0 farama-notifications-0.0.4 fastapi-0.115.11 flatbuffers-25.2.10 gast-0.6.0 gitdb-4.0.12 gitpython-3.1.44 google-pasta-0.2.0 graphene-3.4.3 graphql-core-3.2.6 graphql-relay-3.2.0 grpcio-1.71.0 gymnasium-1.0.0 h5py-3.13.0 holidays-0.69 importlib-resources-6.5.2 keras-3.9.0 langcodes-3.5.0 language-data-1.3.0 libclang-18.1.1 lxml-5.3.1 lz4-4.4.3 marisa-trie-1.2.1 markdown-3.7 ml-dtypes-0.5.1 mlflow-2.21.0 mlflow-skinny-2.21.0 msgpack-1.0.3 murmurhash-1.0.12 namex-0.0.8 nevergrad-1.0.8 nltk-3.9.1 numpy-2.1.3 opentelemetry-sdk-1.31.0 opentelemetry-semantic-conventions-0.52b0 opt-einsum-3.4.0 optree-0.14.1 ormsgpack-1.7.0 outcome-1.3.0.post0 pettingzoo-1.24.3 preshed-3.0.9 prophet-1.1.6 protobuf-5.29.4 pyarrow-19.0.1 pyyaml-6.0.1 ray-2.43.0 selenium-4.29.0 shellingham-1.5.4 simplejson-3.20.1 smart-open-7.1.0 smmap-5.0.2 sortedcontainers-2.4.0 spacy-3.8.4 spacy-legacy-3.0.12 spacy-loggers-1.0.5 sqlparse-0.5.3 srsly-2.5.1 stable-baselines3-2.5.0 stanio-0.5.1 tensorboard-2.19.0 tensorboard-data-server-0.7.2 tensorboardX-2.6.2.2 tensorflow-2.19.0 termcolor-2.5.0 textblob-0.19.0 thinc-8.3.4 trio-0.29.0 trio-websocket-0.12.2 typer-0.15.2 urllib3-1.26.20 waitress-3.0.2 wasabi-0.10.1 weasel-0.4.1 websockets-10.4 wsproto-1.2.0 xgboost-3.0.0 yahoo-finance-1.4.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  WARNING: Failed to remove contents in a temporary directory 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\~umpy.libs'.\n", "  You can safely remove it manually.\n", "  WARNING: Failed to remove contents in a temporary directory 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\~umpy'.\n", "  You can safely remove it manually.\n", "ERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "types-requests 2.32.0.******** requires urllib3>=2, but you have urllib3 1.26.20 which is incompatible.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Collecting en-core-web-sm==3.8.0\n", "  Downloading https://github.com/explosion/spacy-models/releases/download/en_core_web_sm-3.8.0/en_core_web_sm-3.8.0-py3-none-any.whl (12.8 MB)\n", "     ---------------------------------------- 0.0/12.8 MB ? eta -:--:--\n", "     --- ------------------------------------ 1.0/12.8 MB 10.1 MB/s eta 0:00:02\n", "     ----------- ---------------------------- 3.7/12.8 MB 11.5 MB/s eta 0:00:01\n", "     --------------------- ------------------ 6.8/12.8 MB 12.7 MB/s eta 0:00:01\n", "     ------------------------------- ------- 10.2/12.8 MB 13.6 MB/s eta 0:00:01\n", "     --------------------------------------- 12.8/12.8 MB 14.1 MB/s eta 0:00:00\n", "Installing collected packages: en-core-web-sm\n", "Successfully installed en-core-web-sm-3.8.0\n", "✔ Download and installation successful\n", "You can now load the package via spacy.load('en_core_web_sm')\n"]}], "source": ["!pip install numpy pandas matplotlib seaborn tensorflow torch transformers scikit-learn xgboost prophet ray[rllib] stable-baselines3 gymnasium pettingzoo alpaca-trade-api textblob spacy beautifulsoup4 selenium lxml yahoo-finance mlflow deap nevergrad\n", "!python -m spacy download en_core_web_sm\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting yahoo_fin\n", "  Downloading yahoo_fin-*******-py3-none-any.whl.metadata (699 bytes)\n", "Collecting requests-html (from yahoo_fin)\n", "  Downloading requests_html-0.10.0-py3-none-any.whl.metadata (15 kB)\n", "Collecting feedparser (from yahoo_fin)\n", "  Downloading feedparser-6.0.11-py3-none-any.whl.metadata (2.4 kB)\n", "Requirement already satisfied: requests in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from yahoo_fin) (2.32.3)\n", "Requirement already satisfied: pandas in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from yahoo_fin) (2.2.3)\n", "Collecting sgmllib3k (from feedparser->yahoo_fin)\n", "  Downloading sgmllib3k-1.0.0.tar.gz (5.8 kB)\n", "  Preparing metadata (setup.py): started\n", "  Preparing metadata (setup.py): finished with status 'done'\n", "Requirement already satisfied: numpy>=1.26.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pandas->yahoo_fin) (2.1.3)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from pandas->yahoo_fin) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pandas->yahoo_fin) (2025.1)\n", "Requirement already satisfied: tzdata>=2022.7 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pandas->yahoo_fin) (2025.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests->yahoo_fin) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests->yahoo_fin) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests->yahoo_fin) (1.26.20)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests->yahoo_fin) (2025.1.31)\n", "Collecting pyquery (from requests-html->yahoo_fin)\n", "  Downloading pyquery-2.0.1-py3-none-any.whl.metadata (9.0 kB)\n", "Collecting fake-useragent (from requests-html->yahoo_fin)\n", "  Downloading fake_useragent-2.1.0-py3-none-any.whl.metadata (17 kB)\n", "Collecting parse (from requests-html->yahoo_fin)\n", "  Downloading parse-1.20.2-py2.py3-none-any.whl.metadata (22 kB)\n", "Collecting bs4 (from requests-html->yahoo_fin)\n", "  Using cached bs4-0.0.2-py2.py3-none-any.whl.metadata (411 bytes)\n", "Collecting w3lib (from requests-html->yahoo_fin)\n", "  Downloading w3lib-2.3.1-py3-none-any.whl.metadata (2.3 kB)\n", "Collecting pyppeteer>=0.0.14 (from requests-html->yahoo_fin)\n", "  Downloading pyppeteer-2.0.0-py3-none-any.whl.metadata (7.1 kB)\n", "Collecting appdirs<2.0.0,>=1.4.3 (from pyppeteer>=0.0.14->requests-html->yahoo_fin)\n", "  Downloading appdirs-1.4.4-py2.py3-none-any.whl.metadata (9.0 kB)\n", "Requirement already satisfied: importlib-metadata>=1.4 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pyppeteer>=0.0.14->requests-html->yahoo_fin) (8.6.1)\n", "Collecting pyee<12.0.0,>=11.0.0 (from pyppeteer>=0.0.14->requests-html->yahoo_fin)\n", "  Downloading pyee-11.1.1-py3-none-any.whl.metadata (2.8 kB)\n", "Requirement already satisfied: tqdm<5.0.0,>=4.42.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pyppeteer>=0.0.14->requests-html->yahoo_fin) (4.67.1)\n", "Requirement already satisfied: websockets<11.0,>=10.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pyppeteer>=0.0.14->requests-html->yahoo_fin) (10.4)\n", "Requirement already satisfied: six>=1.5 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from python-dateutil>=2.8.2->pandas->yahoo_fin) (1.17.0)\n", "Requirement already satisfied: beautifulsoup4 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from bs4->requests-html->yahoo_fin) (4.13.3)\n", "Requirement already satisfied: lxml>=2.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pyquery->requests-html->yahoo_fin) (5.3.1)\n", "Collecting cssselect>=1.2.0 (from pyquery->requests-html->yahoo_fin)\n", "  Downloading cssselect-1.3.0-py3-none-any.whl.metadata (2.6 kB)\n", "Requirement already satisfied: zipp>=3.20 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from importlib-metadata>=1.4->pyppeteer>=0.0.14->requests-html->yahoo_fin) (3.21.0)\n", "Requirement already satisfied: typing-extensions in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pyee<12.0.0,>=11.0.0->pyppeteer>=0.0.14->requests-html->yahoo_fin) (4.12.2)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tqdm<5.0.0,>=4.42.1->pyppeteer>=0.0.14->requests-html->yahoo_fin) (0.4.0)\n", "Requirement already satisfied: soupsieve>1.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from beautifulsoup4->bs4->requests-html->yahoo_fin) (2.6)\n", "Downloading yahoo_fin-*******-py3-none-any.whl (10 kB)\n", "Downloading feedparser-6.0.11-py3-none-any.whl (81 kB)\n", "Downloading requests_html-0.10.0-py3-none-any.whl (13 kB)\n", "Downloading pyppeteer-2.0.0-py3-none-any.whl (82 kB)\n", "Using cached bs4-0.0.2-py2.py3-none-any.whl (1.2 kB)\n", "Downloading fake_useragent-2.1.0-py3-none-any.whl (125 kB)\n", "Downloading parse-1.20.2-py2.py3-none-any.whl (20 kB)\n", "Downloading pyquery-2.0.1-py3-none-any.whl (22 kB)\n", "Downloading w3lib-2.3.1-py3-none-any.whl (21 kB)\n", "Downloading appdirs-1.4.4-py2.py3-none-any.whl (9.6 kB)\n", "Downloading cssselect-1.3.0-py3-none-any.whl (18 kB)\n", "Downloading pyee-11.1.1-py3-none-any.whl (15 kB)\n", "Building wheels for collected packages: sgmllib3k\n", "  Building wheel for sgmllib3k (setup.py): started\n", "  Building wheel for sgmllib3k (setup.py): finished with status 'done'\n", "  Created wheel for sgmllib3k: filename=sgmllib3k-1.0.0-py3-none-any.whl size=6104 sha256=7e4cdaf8ddd8cea5dc7845bbab21e0879a4168b6486df64383161b478313cbde\n", "  Stored in directory: c:\\users\\<USER>\\appdata\\local\\pip\\cache\\wheels\\03\\f5\\1a\\23761066dac1d0e8e683e5fdb27e12de53209d05a4a37e6246\n", "Successfully built sgmllib3k\n", "Installing collected packages: sgmllib3k, parse, appdirs, w3lib, pyee, feedparser, fake-useragent, cssselect, pyquery, pyppeteer, bs4, requests-html, yahoo_fin\n", "Successfully installed appdirs-1.4.4 bs4-0.0.2 cssselect-1.3.0 fake-useragent-2.1.0 feedparser-6.0.11 parse-1.20.2 pyee-11.1.1 pyppeteer-2.0.0 pyquery-2.0.1 requests-html-0.10.0 sgmllib3k-1.0.0 w3lib-2.3.1 yahoo_fin-*******\n"]}], "source": ["!pip install yahoo_fin"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: requests_html in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (0.10.0)\n", "Requirement already satisfied: requests in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests_html) (2.32.3)\n", "Requirement already satisfied: pyquery in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests_html) (2.0.1)\n", "Requirement already satisfied: fake-useragent in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests_html) (2.1.0)\n", "Requirement already satisfied: parse in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests_html) (1.20.2)\n", "Requirement already satisfied: bs4 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests_html) (0.0.2)\n", "Requirement already satisfied: w3lib in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests_html) (2.3.1)\n", "Requirement already satisfied: pyppeteer>=0.0.14 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests_html) (2.0.0)\n", "Requirement already satisfied: appdirs<2.0.0,>=1.4.3 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pyppeteer>=0.0.14->requests_html) (1.4.4)\n", "Requirement already satisfied: certifi>=2023 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pyppeteer>=0.0.14->requests_html) (2025.1.31)\n", "Requirement already satisfied: importlib-metadata>=1.4 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pyppeteer>=0.0.14->requests_html) (8.6.1)\n", "Requirement already satisfied: pyee<12.0.0,>=11.0.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pyppeteer>=0.0.14->requests_html) (11.1.1)\n", "Requirement already satisfied: tqdm<5.0.0,>=4.42.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pyppeteer>=0.0.14->requests_html) (4.67.1)\n", "Requirement already satisfied: urllib3<2.0.0,>=1.25.8 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pyppeteer>=0.0.14->requests_html) (1.26.20)\n", "Requirement already satisfied: websockets<11.0,>=10.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pyppeteer>=0.0.14->requests_html) (10.4)\n", "Requirement already satisfied: beautifulsoup4 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from bs4->requests_html) (4.13.3)\n", "Requirement already satisfied: lxml>=2.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pyquery->requests_html) (5.3.1)\n", "Requirement already satisfied: cssselect>=1.2.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pyquery->requests_html) (1.3.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests->requests_html) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests->requests_html) (3.10)\n", "Requirement already satisfied: zipp>=3.20 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from importlib-metadata>=1.4->pyppeteer>=0.0.14->requests_html) (3.21.0)\n", "Requirement already satisfied: typing-extensions in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pyee<12.0.0,>=11.0.0->pyppeteer>=0.0.14->requests_html) (4.12.2)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tqdm<5.0.0,>=4.42.1->pyppeteer>=0.0.14->requests_html) (0.4.0)\n", "Requirement already satisfied: soupsieve>1.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from beautifulsoup4->bs4->requests_html) (2.6)\n", "Collecting ipywidgets\n", "  Downloading ipywidgets-8.1.5-py3-none-any.whl.metadata (2.3 kB)\n", "Requirement already satisfied: comm>=0.1.3 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from ipywidgets) (0.2.2)\n", "Requirement already satisfied: ipython>=6.1.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from ipywidgets) (8.32.0)\n", "Requirement already satisfied: traitlets>=4.3.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from ipywidgets) (5.14.3)\n", "Collecting widgetsnbextension~=4.0.12 (from ipywidgets)\n", "  Downloading widgetsnbextension-4.0.13-py3-none-any.whl.metadata (1.6 kB)\n", "Collecting jupyterlab-widgets~=3.0.12 (from ipywidgets)\n", "  Downloading jupyterlab_widgets-3.0.13-py3-none-any.whl.metadata (4.1 kB)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from ipython>=6.1.0->ipywidgets) (0.4.0)\n", "Requirement already satisfied: decorator in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from ipython>=6.1.0->ipywidgets) (5.1.1)\n", "Requirement already satisfied: jedi>=0.16 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from ipython>=6.1.0->ipywidgets) (0.19.2)\n", "Requirement already satisfied: matplotlib-inline in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from ipython>=6.1.0->ipywidgets) (0.1.7)\n", "Requirement already satisfied: prompt_toolkit<3.1.0,>=3.0.41 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from ipython>=6.1.0->ipywidgets) (3.0.50)\n", "Requirement already satisfied: pygments>=2.4.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from ipython>=6.1.0->ipywidgets) (2.19.1)\n", "Requirement already satisfied: stack_data in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from ipython>=6.1.0->ipywidgets) (0.6.3)\n", "Requirement already satisfied: parso<0.9.0,>=0.8.4 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from jedi>=0.16->ipython>=6.1.0->ipywidgets) (0.8.4)\n", "Requirement already satisfied: wcwidth in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from prompt_toolkit<3.1.0,>=3.0.41->ipython>=6.1.0->ipywidgets) (0.2.13)\n", "Requirement already satisfied: executing>=1.2.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from stack_data->ipython>=6.1.0->ipywidgets) (2.2.0)\n", "Requirement already satisfied: asttokens>=2.1.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from stack_data->ipython>=6.1.0->ipywidgets) (3.0.0)\n", "Requirement already satisfied: pure-eval in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from stack_data->ipython>=6.1.0->ipywidgets) (0.2.3)\n", "Downloading ipywidgets-8.1.5-py3-none-any.whl (139 kB)\n", "Downloading jupyterlab_widgets-3.0.13-py3-none-any.whl (214 kB)\n", "Downloading widgetsnbextension-4.0.13-py3-none-any.whl (2.3 MB)\n", "   ---------------------------------------- 0.0/2.3 MB ? eta -:--:--\n", "   ------------------------------- -------- 1.8/2.3 MB 10.1 MB/s eta 0:00:01\n", "   ---------------------------------------- 2.3/2.3 MB 9.5 MB/s eta 0:00:00\n", "Installing collected packages: widgetsnbextension, jupyterlab-widgets, ipywidgets\n", "Successfully installed ipywidgets-8.1.5 jupyterlab-widgets-3.0.13 widgetsnbextension-4.0.13\n"]}], "source": ["!pip install requests_html\n", "!pip install -U ipywidgets"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Import Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import tensorflow as tf\n", "import torch\n", "from transformers import pipeline\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import MinMaxScaler\n", "from sklearn.ensemble import RandomForestRegressor\n", "from xgboost import XGBRegressor\n", "from prophet import Prophet\n", "from stable_baselines3 import PPO\n", "from gymnasium import Env\n", "from gymnasium.spaces import Discrete, Box\n", "from alpaca_trade_api import REST\n", "import spacy\n", "from textblob import TextBlob\n", "from bs4 import BeautifulSoup\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from yahoo_fin import stock_info\n", "import mlflow\n", "import deap\n", "import nevergrad as ng\n", "import os\n", "import json\n", "import time\n", "import ray\n", "from ray import tune, rllib\n", "from ray.tune.registry import register_env\n", "from datetime import datetime, timedelta\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Load Spacy model\n", "nlp = spacy.load(\"en_core_web_sm\")\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting yfinance\n", "  Downloading yfinance-0.2.54-py2.py3-none-any.whl.metadata (5.8 kB)\n", "Requirement already satisfied: pandas>=1.3.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from yfinance) (2.2.3)\n", "Requirement already satisfied: numpy>=1.16.5 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from yfinance) (2.1.3)\n", "Requirement already satisfied: requests>=2.31 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from yfinance) (2.32.3)\n", "Collecting multitasking>=0.0.7 (from yfinance)\n", "  Downloading multitasking-0.0.11-py3-none-any.whl.metadata (5.5 kB)\n", "Requirement already satisfied: platformdirs>=2.0.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from yfinance) (4.3.6)\n", "Requirement already satisfied: pytz>=2022.5 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from yfinance) (2025.1)\n", "Collecting frozendict>=2.3.4 (from yfinance)\n", "  Downloading frozendict-2.4.6-py312-none-any.whl.metadata (23 kB)\n", "Collecting peewee>=3.16.2 (from yfinance)\n", "  Downloading peewee-3.17.9.tar.gz (3.0 MB)\n", "     ---------------------------------------- 0.0/3.0 MB ? eta -:--:--\n", "     ------------------------ --------------- 1.8/3.0 MB 14.4 MB/s eta 0:00:01\n", "     ---------------------------------------- 3.0/3.0 MB 17.7 MB/s eta 0:00:00\n", "  Installing build dependencies: started\n", "  Installing build dependencies: finished with status 'done'\n", "  Getting requirements to build wheel: started\n", "  Getting requirements to build wheel: finished with status 'done'\n", "  Preparing metadata (pyproject.toml): started\n", "  Preparing metadata (pyproject.toml): finished with status 'done'\n", "Requirement already satisfied: beautifulsoup4>=4.11.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from yfinance) (4.13.3)\n", "Requirement already satisfied: soupsieve>1.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from beautifulsoup4>=4.11.1->yfinance) (2.6)\n", "Requirement already satisfied: typing-extensions>=4.0.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from beautifulsoup4>=4.11.1->yfinance) (4.12.2)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from pandas>=1.3.0->yfinance) (2.9.0.post0)\n", "Requirement already satisfied: tzdata>=2022.7 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pandas>=1.3.0->yfinance) (2025.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests>=2.31->yfinance) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests>=2.31->yfinance) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests>=2.31->yfinance) (1.26.20)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests>=2.31->yfinance) (2025.1.31)\n", "Requirement already satisfied: six>=1.5 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from python-dateutil>=2.8.2->pandas>=1.3.0->yfinance) (1.17.0)\n", "Downloading yfinance-0.2.54-py2.py3-none-any.whl (108 kB)\n", "Downloading frozendict-2.4.6-py312-none-any.whl (16 kB)\n", "Downloading multitasking-0.0.11-py3-none-any.whl (8.5 kB)\n", "Building wheels for collected packages: peewee\n", "  Building wheel for peewee (pyproject.toml): started\n", "  Building wheel for peewee (pyproject.toml): finished with status 'done'\n", "  Created wheel for peewee: filename=peewee-3.17.9-py3-none-any.whl size=139128 sha256=8c0f0190cf1e993d59dce94964d8716c848473f260825accb1957ea811f76066\n", "  Stored in directory: c:\\users\\<USER>\\appdata\\local\\pip\\cache\\wheels\\43\\ef\\2d\\2c51d496bf084945ffdf838b4cc8767b8ba1cc20eb41588831\n", "Successfully built peewee\n", "Installing collected packages: peewee, multitasking, frozendict, yfinance\n", "Successfully installed frozendict-2.4.6 multitasking-0.0.11 peewee-3.17.9 yfinance-0.2.54\n"]}], "source": ["!pip install yfinance"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[*********************100%***********************]  1 of 1 completed"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Fetched data for MSFT\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th>Price</th>\n", "      <th>Close</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Open</th>\n", "      <th>Volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Ticker</th>\n", "      <th>MSFT</th>\n", "      <th>MSFT</th>\n", "      <th>MSFT</th>\n", "      <th>MSFT</th>\n", "      <th>MSFT</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-01-03</th>\n", "      <td>235.240021</td>\n", "      <td>241.298250</td>\n", "      <td>233.099504</td>\n", "      <td>238.676618</td>\n", "      <td>25740000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-04</th>\n", "      <td>224.949875</td>\n", "      <td>228.651571</td>\n", "      <td>221.866756</td>\n", "      <td>228.072262</td>\n", "      <td>50623400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-05</th>\n", "      <td>218.282867</td>\n", "      <td>223.427951</td>\n", "      <td>217.742828</td>\n", "      <td>223.084285</td>\n", "      <td>39585600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-06</th>\n", "      <td>220.855392</td>\n", "      <td>221.670359</td>\n", "      <td>215.376487</td>\n", "      <td>218.960362</td>\n", "      <td>43613600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-01-09</th>\n", "      <td>223.005722</td>\n", "      <td>227.051099</td>\n", "      <td>222.308592</td>\n", "      <td>222.347861</td>\n", "      <td>27369800</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Price            Close        High         Low        Open    Volume\n", "Ticker            MSFT        MSFT        MSFT        MSFT      MSFT\n", "Date                                                                \n", "2023-01-03  235.240021  241.298250  233.099504  238.676618  25740000\n", "2023-01-04  224.949875  228.651571  221.866756  228.072262  50623400\n", "2023-01-05  218.282867  223.427951  217.742828  223.084285  39585600\n", "2023-01-06  220.855392  221.670359  215.376487  218.960362  43613600\n", "2023-01-09  223.005722  227.051099  222.308592  222.347861  27369800"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["import yfinance as yf\n", "\n", "def get_yahoo_data(symbol, start_date, end_date):\n", "    try:\n", "        data = yf.download(symbol, start=start_date, end=end_date)\n", "        print(f\"✅ Fetched data for {symbol}\")\n", "        return data\n", "    except Exception as e:\n", "        print(f\"❌ Error fetching data for {symbol}: {e}\")\n", "        return None\n", "\n", "symbol = \"MSFT\"\n", "start_date = \"2023-01-01\"\n", "end_date = \"2025-01-01\"\n", "data = get_yahoo_data(symbol, start_date, end_date)\n", "data.head()\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting finnhub-python\n", "  Downloading finnhub_python-2.4.23-py3-none-any.whl.metadata (9.2 kB)\n", "Requirement already satisfied: requests>=2.22.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from finnhub-python) (2.32.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests>=2.22.0->finnhub-python) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests>=2.22.0->finnhub-python) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests>=2.22.0->finnhub-python) (1.26.20)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests>=2.22.0->finnhub-python) (2025.1.31)\n", "Downloading finnhub_python-2.4.23-py3-none-any.whl (11 kB)\n", "Installing collected packages: finnhub-python\n", "Successfully installed finnhub-python-2.4.23\n"]}], "source": ["!pip install finnhub-python\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["import finnhub\n", "\n", "# Set up Finnhub client\n", "FINNHUB_API_KEY = 'cvdt2p1r01qm9khn4i60cvdt2p1r01qm9khn4i6g'\n", "finnhub_client = finnhub.Client(api_key=FINNHUB_API_KEY)\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Apple Stock Price: $215.24\n"]}], "source": ["def get_stock_price(symbol):\n", "    try:\n", "        quote = finnhub_client.quote(symbol)\n", "        return quote['c']  # Current price\n", "    except Exception as e:\n", "        print(f\"Error fetching data: {e}\")\n", "        return None\n", "price = get_stock_price('AAPL')\n", "print(f\"Apple Stock Price: ${price:.2f}\")"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["import websocket\n", "\n", "def get_real_time_data(symbol):\n", "    quote = finnhub_client.quote(symbol)\n", "    return {\n", "        'symbol': symbol,\n", "        'current_price': quote['c'],\n", "        'high_price': quote['h'],\n", "        'low_price': quote['l'],\n", "        'open_price': quote['o'],\n", "        'previous_close': quote['pc']\n", "    }\n", "\n", "# WebSocket for real-time updates\n", "def on_message(ws, message):\n", "    data = json.loads(message)\n", "    if 'type' in data and data['type'] == 'trade':\n", "        for trade in data['data']:\n", "            print(f\"{trade['s']} - Price: {trade['p']}, Volume: {trade['v']}\")\n", "\n", "def on_open(ws):\n", "    symbols = ['AAPL', 'TSLA']  # Add more symbols if needed\n", "    for symbol in symbols:\n", "        ws.send(json.dumps({'type': 'subscribe', 'symbol': symbol}))\n", "\n", "def on_close(ws, close_status_code, close_msg):\n", "    print(\"WebSocket closed\")\n", "\n", "def on_error(ws, error):\n", "    print(f\"Error: {error}\")\n", "\n", "def start_realtime_stream():\n", "    socket = f\"wss://ws.finnhub.io?token={FINNHUB_API_KEY}\"\n", "    ws = websocket.WebSocketApp(socket,\n", "                                on_open=on_open,\n", "                                on_message=on_message,\n", "                                on_close=on_close,\n", "                                on_error=on_error)\n", "    ws.run_forever()"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Real-time data for AAPL: {'symbol': 'AAPL', 'current_price': 215.24, 'high_price': 218.76, 'low_price': 213.75, 'open_price': 214.22, 'previous_close': 212.69}\n"]}], "source": ["symbol = 'AAPL'\n", "real_time_data = get_real_time_data(symbol)\n", "print(f\"Real-time data for {symbol}: {real_time_data}\")\n", "\n", "# Start WebSocket for streaming\n", "# print(\"\\nStarting WebSocket stream...\")\n", "# start_realtime_stream()"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GPT-4 Analysis:\n", "1. \"Apple reports record Q1 earnings, beats expectations\": This headline suggests that Apple has performed exceptionally well in the first quarter, exceeding their forecasted financial figures. This could indicate Apple's growing market presence and strong demand for its products and services. It could also mean that the company's strategies are working effectively. Positive financial results would likely boost the company's stock prices, signaling good news for its shareholders and investors. \n", "\n", "2. \"Fed signals interest rate hike in coming months\": The Federal Reserve's indication of a potential interest rate hike might have various implications. Borrowing costs, including for mortgages and credit cards, would become higher. Companies with high levels of debt might face increased financial burden, which could potentially decrease their profitability. On the other hand, this could be a sign that the economy is getting stronger. Additionally, higher interest rates might attract more foreign investment, resulting in a stronger national currency.\n", "\n", "3. \"Tech stocks rally as market shows signs of recovery\": This headline suggests a bullish trend in the technology stocks as market conditions improve. The tech sector seems to be recovering from a potential downturn or bearish cycle, hence rallying higher. This could be due to technological companies reporting positive earnings or perhaps due to a renewed investor confidence in the tech sector of the market. It may be a good time for investors to review their portfolio if they have significant positions in tech stocks or consider investing if they were previously waiting for the market to stabilize.\n"]}], "source": ["from openai import OpenAI\n", "\n", "# OPENAI_API_KEY = os.getenv(\"OPENAI_API_KEY\")\n", "\n", "client = OpenAI(api_key=\"********************************************************************************************************************************************************************\")\n", "\n", "def analyze_news_with_gpt(news_headlines):\n", "    \"\"\"\n", "    Use GPT-4 to analyze financial news and extract insights.\n", "    \"\"\"\n", "    prompt = f\"Analyze the following financial news headlines and provide insights:\\n\\n\"\n", "    for headline in news_headlines:\n", "        prompt += f\"- {headline}\\n\"\n", "    \n", "    response = client.chat.completions.create(\n", "        model=\"gpt-4\",\n", "        messages=[{\"role\": \"system\", \"content\": prompt}]\n", "    )\n", "    \n", "    return response.choices[0].message.content\n", "\n", "# Example: Simulated news headlines\n", "news_headlines = [\n", "    \"Apple reports record Q1 earnings, beats expectations.\",\n", "    \"Fed signals interest rate hike in coming months.\",\n", "    \"Tech stocks rally as market shows signs of recovery.\"\n", "]\n", "\n", "gpt_analysis = analyze_news_with_gpt(news_headlines)\n", "print(\"GPT-4 Analysis:\")\n", "print(gpt_analysis)\n"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'Date'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:3805\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3804\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 3805\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_engine\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcasted_key\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   3806\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[1;32mindex.pyx:167\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mindex.pyx:196\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\\\_libs\\\\hashtable_class_helper.pxi:7081\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\\\_libs\\\\hashtable_class_helper.pxi:7089\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'Date'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[52], line 4\u001b[0m\n\u001b[0;32m      1\u001b[0m df \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mDataFrame(data)\n\u001b[0;32m      3\u001b[0m \u001b[38;5;66;03m# Convert 'Date' column to datetime format\u001b[39;00m\n\u001b[1;32m----> 4\u001b[0m df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mDate\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mto_datetime(\u001b[43mdf\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mDate\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m)\n\u001b[0;32m      6\u001b[0m symbol \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mMSFT\u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m      8\u001b[0m plt\u001b[38;5;241m.\u001b[39mfigure(figsize\u001b[38;5;241m=\u001b[39m(\u001b[38;5;241m14\u001b[39m, \u001b[38;5;241m8\u001b[39m))\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\frame.py:4101\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   4099\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_single_key:\n\u001b[0;32m   4100\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mnlevels \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[1;32m-> 4101\u001b[0m         \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_getitem_multilevel\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   4102\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mget_loc(key)\n\u001b[0;32m   4103\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m is_integer(indexer):\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\frame.py:4159\u001b[0m, in \u001b[0;36mDataFrame._getitem_multilevel\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   4157\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m_getitem_multilevel\u001b[39m(\u001b[38;5;28mself\u001b[39m, key):\n\u001b[0;32m   4158\u001b[0m     \u001b[38;5;66;03m# self.columns is a MultiIndex\u001b[39;00m\n\u001b[1;32m-> 4159\u001b[0m     loc \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   4160\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(loc, (\u001b[38;5;28mslice\u001b[39m, np\u001b[38;5;241m.\u001b[39mndarray)):\n\u001b[0;32m   4161\u001b[0m         new_columns \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns[loc]\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py:3040\u001b[0m, in \u001b[0;36mMultiIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3037\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m mask\n\u001b[0;32m   3039\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(key, \u001b[38;5;28mtuple\u001b[39m):\n\u001b[1;32m-> 3040\u001b[0m     loc \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_level_indexer\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlevel\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m   3041\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m _maybe_to_slice(loc)\n\u001b[0;32m   3043\u001b[0m keylen \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlen\u001b[39m(key)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py:3391\u001b[0m, in \u001b[0;36mMultiIndex._get_level_indexer\u001b[1;34m(self, key, level, indexer)\u001b[0m\n\u001b[0;32m   3388\u001b[0m         \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mslice\u001b[39m(i, j, step)\n\u001b[0;32m   3390\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m-> 3391\u001b[0m     idx \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_loc_single_level_index\u001b[49m\u001b[43m(\u001b[49m\u001b[43mlevel_index\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   3393\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m level \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m0\u001b[39m \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_lexsort_depth \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[0;32m   3394\u001b[0m         \u001b[38;5;66;03m# Desired level is not sorted\u001b[39;00m\n\u001b[0;32m   3395\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(idx, \u001b[38;5;28mslice\u001b[39m):\n\u001b[0;32m   3396\u001b[0m             \u001b[38;5;66;03m# test_get_loc_partial_timestamp_multiindex\u001b[39;00m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py:2980\u001b[0m, in \u001b[0;36mMultiIndex._get_loc_single_level_index\u001b[1;34m(self, level_index, key)\u001b[0m\n\u001b[0;32m   2978\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m\n\u001b[0;32m   2979\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m-> 2980\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mlevel_index\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:3812\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3807\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(casted_key, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m (\n\u001b[0;32m   3808\u001b[0m         \u001b[38;5;28misinstance\u001b[39m(casted_key, abc\u001b[38;5;241m.\u001b[39mIterable)\n\u001b[0;32m   3809\u001b[0m         \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28many\u001b[39m(\u001b[38;5;28misinstance\u001b[39m(x, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m casted_key)\n\u001b[0;32m   3810\u001b[0m     ):\n\u001b[0;32m   3811\u001b[0m         \u001b[38;5;28;01<PERSON>raise\u001b[39;00m InvalidIndexError(key)\n\u001b[1;32m-> 3812\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01merr\u001b[39;00m\n\u001b[0;32m   3813\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m:\n\u001b[0;32m   3814\u001b[0m     \u001b[38;5;66;03m# If we have a listlike key, _check_indexing_error will raise\u001b[39;00m\n\u001b[0;32m   3815\u001b[0m     \u001b[38;5;66;03m#  InvalidIndexError. Otherwise we fall through and re-raise\u001b[39;00m\n\u001b[0;32m   3816\u001b[0m     \u001b[38;5;66;03m#  the TypeError.\u001b[39;00m\n\u001b[0;32m   3817\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_indexing_error(key)\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'Date'"]}], "source": ["df = pd.DataFrame(data)\n", "\n", "# Convert 'Date' column to datetime format\n", "df['Date'] = pd.to_datetime(df['Date'])\n", "\n", "symbol = 'MSFT'\n", "\n", "plt.figure(figsize=(14, 8))\n", "\n", "sns.lineplot(x=df['Date'], y=df['Close'], label=f\"{symbol} Close\", color='blue')\n", "sns.lineplot(x=df['Date'], y=df['High'], label=f\"{symbol} High\", color='green')\n", "sns.lineplot(x=df['Date'], y=df['Low'], label=f\"{symbol} Low\", color='red')\n", "sns.lineplot(x=df['Date'], y=df['Open'], label=f\"{symbol} Open\", color='purple')\n", "\n", "plt.title(f\"{symbol} Stock Performance\", fontsize=16)\n", "plt.xlabel('Date', fontsize=12)\n", "plt.ylabel('Price', fontsize=12)\n", "plt.legend()\n", "plt.grid(True)\n", "\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}