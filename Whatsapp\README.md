# Wind & Solar Automation Platform

A modular, production-ready platform for automating daily generation reports (DGR) for wind and solar plants, integrating with WhatsApp for report delivery, and supporting advanced data processing, plotting, and PDF generation.

---

## Table of Contents

- [Features](#features)
- [Project Structure](#project-structure)
- [Setup & Installation](#setup--installation)
- [Configuration](#configuration)
- [Usage](#usage)
- [Directory Overview](#directory-overview)
- [Deployment](#deployment)


---

## Features

- Automated generation of daily, monthly, and yearly reports for wind and solar plants.
- WhatsApp integration for sending DGR reports to customers.
- Modular codebase with clear separation of business logic, data access, utilities, and configuration.
- Asynchronous processing for efficient data handling.
- PDF and CSV report generation with professional formatting.
- Centralized configuration and environment management.
- Logging and error handling for robust operations.

---

## Project Structure

```
.
├── app/                # Main application logic (routes, handlers, scheduling)
├── DB/                 # Database models and operations
├── helper/             # Utilities: plotting, integrations, logging, S3, etc.
├── src/                # Core automation logic for wind/solar/both
├── whatsapp/           # WhatsApp integration (sending, extraction)
├── config/             # Configuration and settings
├── static/             # Static files (customer data, logos, reports, plots)
├── templates/          # HTML templates for web frontend
├── tests/              # (Recommended) Automated tests
├── main.py             # Application entry point
├── requirements.txt    # Python dependencies
├── .env                # Environment variables (not committed)
├── README.md           # Project documentation
```

---

## Setup & Installation

1. **Clone the repository:**
   ```bash
   git https://github.com/Logos-Labs-India/DGR-Generation.git
   cd <project-directory>
   ```

2. **Create and activate a virtual environment:**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables:**
   - Copy `.env.example` to `.env` and fill in the required values (see [Configuration](#configuration)).

---

## Configuration

All configuration is managed via environment variables and `config/settings.py`. Key settings include:

- `VERIFY_TOKEN` - Token for webhook verification.
- `PUBLIC_URL` - Publicly accessible URL for webhooks.
- `DEBUG` - Set to `True` for development.
- `CUSTOMER_DATA_CSV_PATH` - Path to the customer data CSV file.
- `API_TOKEN` - Token for external integrations (e.g., Prescinto API).

**Example `.env` file:**
```
VERIFY_TOKEN=your_verify_token
PUBLIC_URL=https://yourdomain.com
DEBUG=True
CUSTOMER_DATA_CSV_PATH=static/customer_data - Sheet1.csv
API_TOKEN=your_api_token
```

---

## Usage

**Start the application:**
```bash
python main.py
```

- The app will start the web server and background scheduler.
- Reports will be generated and sent via WhatsApp as per the schedule.

**Access the web frontend:**
- Visit `http://localhost:5000` (or your configured host/port).

---

## Directory Overview

- **app/**: Web routes, frontend handlers, scheduling, and webhook endpoints.
- **DB/**: SQLAlchemy models and database operations.
- **helper/**: Utilities for plotting, PDF/CSV generation, logging, S3, and integrations.
- **src/**: Core automation for wind, solar, and combined plants.
- **whatsapp/**: WhatsApp message sending and extraction logic.
- **config/**: Centralized configuration and environment variable loading.
- **static/**: Customer data, logos, generated reports, and plots.
- **templates/**: HTML templates for the web interface.



## Deployment

- Use environment variables to control deployment settings.
- For production, consider using a WSGI server (e.g., Gunicorn) and a process manager (e.g., Supervisor, systemd).
- Containerization (Docker) is recommended for scalable deployments.