

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - DGR Validation Tool</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">
    <style>
        body {
            min-height: 100vh;
            background: linear-gradient(135deg, #e0e7ff 0%, #f8fafc 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Inter', Arial, sans-serif;
        }
        .login-card {
            width: 100%;
            max-width: 400px;
            padding: 2.5rem 2rem 2rem 2rem;
            border-radius: 18px;
            box-shadow: 0 8px 32px rgba(31, 41, 55, 0.15);
            background: #fff;
            position: relative;
            transition: box-shadow 0.2s;
        }
        .login-card:hover {
            box-shadow: 0 12px 40px rgba(31, 41, 55, 0.22);
        }
        .login-logo {
            display: flex;
            justify-content: center;
            margin-bottom: 1.5rem;
        }
        .login-logo img {
            height: 56px;
            width: auto;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.07);
        }
        .login-card h2 {
            margin-bottom: 1.2rem;
            font-weight: 600;
            text-align: center;
            color: #1e293b;
            letter-spacing: 0.01em;
        }
        .form-label {
            font-weight: 500;
            color: #334155;
        }
        .form-control {
            border-radius: 8px;
            border: 1px solid #cbd5e1;
            transition: border-color 0.2s, box-shadow 0.2s;
            font-size: 1rem;
        }
        .form-control:focus {
            border-color: #6366f1;
            box-shadow: 0 0 0 2px #6366f133;
        }
        .btn-primary {
            background: linear-gradient(90deg, #6366f1 0%, #2563eb 100%);
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1.08rem;
            letter-spacing: 0.01em;
            box-shadow: 0 2px 8px rgba(99,102,241,0.08);
            transition: background 0.2s, box-shadow 0.2s;
        }
        .btn-primary:hover, .btn-primary:focus {
            background: linear-gradient(90deg, #4f46e5 0%, #1d4ed8 100%);
            box-shadow: 0 4px 16px rgba(99,102,241,0.13);
        }
        .forgot-link {
            display: block;
            text-align: right;
            margin-top: 0.5rem;
            font-size: 0.97rem;
            color: #6366f1;
            text-decoration: none;
            transition: color 0.2s;
        }
        .forgot-link:hover, .forgot-link:focus {
            color: #1e40af;
            text-decoration: underline;
        }
        .show-password-toggle {
            position: absolute;
            right: 1.2rem;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #64748b;
            font-size: 1.1rem;
            z-index: 2;
        }
        .position-relative {
            position: relative;
        }
        .alert {
            margin-bottom: 1rem;
        }
        @media (max-width: 500px) {
            .login-card {
                padding: 1.5rem 0.7rem 1.2rem 0.7rem;
                max-width: 98vw;
            }
            .login-logo img {
                height: 44px;
            }
        }
    </style>
</head>
<body>
    <div class="login-card">
        <div class="login-logo">
            <img src="static/logo_integrum.jpg" alt="Integrum Logo" loading="lazy">
        </div>
        <h2>Login to DGR Validation Tool</h2>
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        <form method="POST" action="{{ url_for('login') }}" autocomplete="on">
            <div class="mb-3">
                <label for="email" class="form-label">Email address</label>
                <input 
                    type="email" 
                    class="form-control" 
                    id="email" 
                    name="email" 
                    required 
                    autocomplete="username"
                    aria-label="Email address"
                    placeholder="<EMAIL>"
                >
            </div>
            <div class="mb-3">
                <label for="password" class="form-label">Password</label>
                <div class="d-flex align-items-center position-relative">
                    <input 
                        type="password" 
                        class="form-control pe-5" 
                        id="password" 
                        name="password" 
                        required 
                        autocomplete="current-password"
                        aria-label="Password"
                        placeholder="Enter your password"
                        style="padding-right: 2.5rem;"
                    >
                    <span class="show-password-toggle" tabindex="0" aria-label="Show password" onclick="togglePassword()" onkeypress="if(event.key==='Enter'){togglePassword();}" style="right: 1.2rem; top: 50%; transform: translateY(-50%); position: absolute; display: flex; align-items: center; height: 100%;">
                        <svg id="eyeIcon" xmlns="http://www.w3.org/2000/svg" width="22" height="22" fill="none" viewBox="0 0 24 24"><path stroke="#64748b" stroke-width="2" d="M1.5 12S5.5 5.5 12 5.5 22.5 12 22.5 12 18.5 18.5 12 18.5 1.5 12 1.5 12Z"/><circle cx="12" cy="12" r="3.5" stroke="#64748b" stroke-width="2"/></svg>
                    </span>
                </div>
            </div>
            <button type="submit" class="btn btn-primary w-100 mt-3">Login</button>
        </form>
    </div>
    <script>
        function togglePassword() {
            const pwd = document.getElementById('password');
            const icon = document.getElementById('eyeIcon');
            if (pwd.type === 'password') {
                pwd.type = 'text';
                icon.innerHTML = '<path stroke="#64748b" stroke-width="2" d="M1.5 12S5.5 5.5 12 5.5 22.5 12 22.5 12 18.5 18.5 12 18.5 1.5 12 1.5 12Z"/><circle cx="12" cy="12" r="3.5" stroke="#64748b" stroke-width="2"/><line x1="4" y1="20" x2="20" y2="4" stroke="#64748b" stroke-width="2"/>';
            } else {
                pwd.type = 'password';
                icon.innerHTML = '<path stroke="#64748b" stroke-width="2" d="M1.5 12S5.5 5.5 12 5.5 22.5 12 22.5 12 18.5 18.5 12 18.5 1.5 12 1.5 12Z"/><circle cx="12" cy="12" r="3.5" stroke="#64748b" stroke-width="2"/>';
            }
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
