from app.process_tasks import process_plant_solar, process_plant_wind, process_plant_both
from helper.logger_setup import setup_logger
from DB.setup_db import session as db_session
from DB.models import WindReport, SolarReport, DgrBothDb

logging = setup_logger('regenerate_tasks', 'regenerate_tasks.log')

def regenerate_task_solar(data):
    """
    Sequentially schedule the solar tasks for all plants to reduce RAM usage.
    """
    yesterday = data['date']
    plantName = data['plant_short_name']
    customerName = data['plant_long_name']

    logging.info(f"Scheduled solar tasks starting for {yesterday}.")

    report_type = 'SOLAR INTEGRUM'

    # Process plants sequentially instead of concurrently
    for plant, customer in zip(plantName, customerName):
        logging.info(f"Processing {plant} - {customer}")
        try:
            process_plant_solar(plant, customer, yesterday, report_type)
            logging.info(f"Successfully processed {plant} - {customer}")
        except Exception as e:
            logging.error(f"Error processing plant {plant}: {e}", exc_info=True)

    logging.info(f"Scheduled solar tasks executed for {yesterday}.")

    # Delete the original row after regeneration (SolarReport)

    original_id = data.get("original_id")
    if original_id:
        db = db_session()
        try:
            report = db.get(SolarReport, original_id)
            if report:
                db.delete(report)
                db.commit()
                logging.info(f"Deleted original solar report with id {original_id} after regeneration.")
        except Exception as e:
            logging.error(f"Failed to delete original solar report with id {original_id}: {e}")
        finally:
            db_session.remove()

def regenerate_task_wind(data):

    yesterday = data['date']
    plantName = data['plant_short_name']
    customerName = data['plant_long_name']


    logging.info(f"Scheduled wind tasks starting for {yesterday}.")

    report_type = 'WIND INTEGRUM'

    # Process plants sequentially instead of concurrently
    for plant, customer in zip(plantName, customerName):
        logging.info(f"Processing {plant} - {customer}")
        try:
            process_plant_wind(plant, customer, yesterday, report_type)
            logging.info(f"Successfully processed {plant} - {customer}")
        except Exception as e:
            logging.error(f"Error processing plant {plant}: {e}", exc_info=True)

    logging.info(f"Scheduled wind tasks executed for {yesterday}.")

    # Delete the original row after regeneration (WindReport)

    original_id = data.get("original_id")
    if original_id:
        db = db_session()
        try:
            report = db.get(WindReport, original_id)
            if report:
                db.delete(report)
                db.commit()
                logging.info(f"Deleted original wind report with id {original_id} after regeneration.")
        except Exception as e:
            logging.error(f"Failed to delete original wind report with id {original_id}: {e}")
        finally:
            db_session.remove()

def regenerate_task_both(data):
    yesterday = data['date']
    logging.info(f"Regenerated both-plants tasks starting for {yesterday}.")

    plant_name_wind = data['plant_short_name_wind']
    plant_name_solar = data['plant_short_name_solar']
    customer_name = data['plant_long_name_wind']

    report_type = 'INTEGRUM'

    # Process plants sequentially instead of concurrently
    for plant_solar, plant_wind, customer in zip(plant_name_solar, plant_name_wind, customer_name):
        logging.info(f"Processing {plant_solar} & {plant_wind} - {customer}")
        try:
            process_plant_both(plant_solar, plant_wind, customer, yesterday, report_type)
            logging.info(f"Successfully processed {plant_solar} & {plant_wind} - {customer}")
        except Exception as e:
            logging.error(f"Error processing plants {plant_solar} & {plant_wind}: {e}", exc_info=True)

    logging.info(f"Scheduled both-plants tasks executed for {yesterday}.")

    # Delete the original row after regeneration (DgrBothDb)
    from DB.setup_db import session as db_session
    from DB.models import DgrBothDb
    original_id = data.get("original_id")
    if original_id:
        db = db_session()
        try:
            report = db.get(DgrBothDb, original_id)
            if report:
                db.delete(report)
                db.commit()
                logging.info(f"Deleted original both report with id {original_id} after regeneration.")
        except Exception as e:
            logging.error(f"Failed to delete original both report with id {original_id}: {e}")
        finally:
            db_session.remove()
