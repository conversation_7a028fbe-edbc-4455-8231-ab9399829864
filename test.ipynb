{"cells": [{"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Eventbrite API endpoint: https://www.eventbriteapi.com/v3/events/search/\n", "Meetup API endpoint: https://api.meetup.com/find/upcoming_events\n", "Parsed RSS Feed Events from TimeOut New York:\n", "[]\n"]}, {"ename": "TypeError", "evalue": "fake_requests_get() got an unexpected keyword argument 'params'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mTypeError\u001b[0m                                 Trace<PERSON> (most recent call last)", "Cell \u001b[1;32mIn[7], line 118\u001b[0m\n\u001b[0;32m    115\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m summary\n\u001b[0;32m    117\u001b[0m \u001b[38;5;66;03m# Test the function\u001b[39;00m\n\u001b[1;32m--> 118\u001b[0m wiki_summary \u001b[38;5;241m=\u001b[39m \u001b[43mfetch_wikipedia_summary\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mNew York City\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m    119\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mWikipedia Summary for New York City:\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m    120\u001b[0m \u001b[38;5;28mprint\u001b[39m(wiki_summary)\n", "Cell \u001b[1;32mIn[7], line 107\u001b[0m, in \u001b[0;36mfetch_wikipedia_summary\u001b[1;34m(title)\u001b[0m\n\u001b[0;32m     97\u001b[0m url \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhttps://en.wikipedia.org/w/api.php\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m     98\u001b[0m params \u001b[38;5;241m=\u001b[39m {\n\u001b[0;32m     99\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124maction\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mquery\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m    100\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mprop\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mextracts\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    104\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mformat\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mjson\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    105\u001b[0m }\n\u001b[1;32m--> 107\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[43mrequests\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    108\u001b[0m response\u001b[38;5;241m.\u001b[39mraise_for_status()  \u001b[38;5;66;03m# Raise error if request fails\u001b[39;00m\n\u001b[0;32m    109\u001b[0m data \u001b[38;5;241m=\u001b[39m response\u001b[38;5;241m.\u001b[39mjson()\n", "\u001b[1;31mTypeError\u001b[0m: fake_requests_get() got an unexpected keyword argument 'params'"]}], "source": ["# %% [markdown]\n", "# # Local Event Recommendations & Personalized Agenda Planning - Testing Notebook\n", "\n", "import requests\n", "import json\n", "import feedparser\n", "import spacy\n", "import random\n", "from datetime import datetime\n", "from transformers import pipeline\n", "from unittest.mock import patch\n", "\n", "# %% [markdown]\n", "# ## 1. Data Collection\n", "\n", "# %% [markdown]\n", "# ### 1.1 Eventbrite API Fetcher\n", "def fetch_eventbrite_events(api_key, query_params=None):\n", "    \"\"\"\n", "    Fetch events from Eventbrite using the API.\n", "    \"\"\"\n", "    url = \"https://www.eventbriteapi.com/v3/events/search/\"\n", "    headers = {\"Authorization\": f\"Bearer {api_key}\"}\n", "    response = requests.get(url, headers=headers, params=query_params)\n", "    response.raise_for_status()\n", "    return response.json()\n", "\n", "print(\"Eventbrite API endpoint: https://www.eventbriteapi.com/v3/events/search/\")\n", "\n", "# %% [markdown]\n", "# ### 1.2 Meetup API Fetcher\n", "def fetch_meetup_events(query_params=None):\n", "    \"\"\"\n", "    Fetch upcoming events from Meetup API.\n", "    \"\"\"\n", "    url = \"https://api.meetup.com/find/upcoming_events\"\n", "    response = requests.get(url, params=query_params)\n", "    response.raise_for_status()\n", "    return response.json()\n", "\n", "print(\"Meetup API endpoint: https://api.meetup.com/find/upcoming_events\")\n", "\n", "# %% [markdown]\n", "# ### 1.3 RSS Feed Parser\n", "def parse_rss_feed(feed_url):\n", "    \"\"\"\n", "    Parse events from an RSS feed.\n", "    \"\"\"\n", "    feed = feedparser.parse(feed_url)\n", "    events = [{\"title\": entry.title, \"link\": entry.link, \"published\": entry.published} for entry in feed.entries]\n", "    return events\n", "\n", "rss_url = \"https://www.timeout.com/newyork/events/rss\"\n", "rss_events = parse_rss_feed(rss_url)\n", "print(\"Parsed RSS Feed Events from TimeOut New York:\")\n", "print(rss_events)\n", "\n", "# %% [markdown]\n", "# ### 1.4 Wikipedia API for Contextual Information\n", "# def fetch_wikipedia_summary(title):\n", "#     \"\"\"\n", "#     Fetch a summary from Wikipedia API.\n", "#     \"\"\"\n", "#     url = \"https://en.wikipedia.org/w/api.php\"\n", "#     params = {\n", "#         \"action\": \"query\",\n", "#         \"prop\": \"extracts\",\n", "#         \"exintro\": True,\n", "#         \"explaintext\": True,\n", "#         \"titles\": title,\n", "#         \"format\": \"json\"\n", "#     }\n", "#     response = requests.get(url, params=params)\n", "#     response.raise_for_status()\n", "#     data = response.json()\n", "#     pages = data.get(\"query\", {}).get(\"pages\", {})\n", "#     return next(iter(pages.values()), {}).get(\"extract\", \"\")\n", "\n", "# wiki_summary = fetch_wikipedia_summary(\"New York City\")\n", "# print(\"Wikipedia Summary for New York City:\")\n", "# print(wiki_summary)\n", "\n", "\n", "# Restore the original requests.get before making API calls\n", "\n", "\n", "def fetch_wikipedia_summary(title):\n", "    \"\"\"\n", "    Fetch a summary from Wikipedia API.\n", "    \"\"\"\n", "    # Restore requests.get to its original state\n", "    import builtins\n", "    if \"requests\" in builtins.__dict__:\n", "        import importlib\n", "        importlib.reload(requests)\n", "\n", "    url = \"https://en.wikipedia.org/w/api.php\"\n", "    params = {\n", "        \"action\": \"query\",\n", "        \"prop\": \"extracts\",\n", "        \"exintro\": True,\n", "        \"explaintext\": True,\n", "        \"titles\": title,\n", "        \"format\": \"json\"\n", "    }\n", "\n", "    response = requests.get(url, params=params)\n", "    response.raise_for_status()  # Raise error if request fails\n", "    data = response.json()\n", "\n", "    # Extract the page content\n", "    pages = data.get(\"query\", {}).get(\"pages\", {})\n", "    summary = next(iter(pages.values()), {}).get(\"extract\", \"No summary available.\")\n", "\n", "    return summary\n", "\n", "# Test the function\n", "wiki_summary = fetch_wikipedia_summary(\"New York City\")\n", "print(\"Wikipedia Summary for New York City:\")\n", "print(wiki_summary)\n", "\n", "# %% [markdown]\n", "# ## 2. NLP Processing & Categorization\n", "\n", "# %% [markdown]\n", "# ### 2.1 Preprocessing with spaCy\n", "nlp = spacy.load(\"en_core_web_sm\")\n", "\n", "def preprocess_text(text):\n", "    \"\"\"\n", "    Preprocess text by lemmatizing and removing stop words.\n", "    \"\"\"\n", "    doc = nlp(text)\n", "    return \" \".join([token.lemma_ for token in doc if not token.is_stop and not token.is_punct])\n", "\n", "print(\"Preprocessed Text:\")\n", "print(preprocess_text(\"This is an example text that will be preprocessed.\"))\n", "\n", "# %% [markdown]\n", "# ### 2.2 Event Classification (Zero-Shot)\n", "classifier = pipeline(\"zero-shot-classification\", model=\"facebook/bart-large-mnli\")\n", "\n", "def classify_event(text, candidate_labels=[\"music\", \"sports\", \"education\", \"networking\"]):\n", "    \"\"\"\n", "    Classify event text using zero-shot classification.\n", "    \"\"\"\n", "    return classifier(text, candidate_labels)\n", "\n", "classification_result = classify_event(\"Join us for an evening of live jazz music.\")\n", "print(\"Event Classification Result:\")\n", "print(classification_result)\n", "\n", "# %% [markdown]\n", "# ### 2.3 Event Summarization\n", "summarizer = pipeline(\"summarization\")\n", "\n", "def summarize_event(text):\n", "    \"\"\"\n", "    Summarize a long event description.\n", "    \"\"\"\n", "    summary = summarizer(text, max_length=50, min_length=25, do_sample=False)\n", "    return summary[0]['summary_text']\n", "\n", "print(\"Event Summary:\")\n", "print(summarize_event(\"This is a long description of an event expected to cover multiple topics and last several hours.\"))\n", "\n", "# %% [markdown]\n", "# ### 2.4 Sentiment Analysis\n", "sentiment_pipeline = pipeline(\"sentiment-analysis\")\n", "\n", "def analyze_sentiment(text):\n", "    \"\"\"\n", "    Analyze sentiment of event text.\n", "    \"\"\"\n", "    return sentiment_pipeline(text)\n", "\n", "print(\"Sentiment Analysis Result:\")\n", "print(analyze_sentiment(\"I absolutely loved the event! It was fantastic.\"))\n", "\n", "# %% [markdown]\n", "# ## 3. Recommendation Engine\n", "\n", "# %% [markdown]\n", "# ### 3.1 Content-Based Filtering\n", "class UserProfile:\n", "    def __init__(self, user_id):\n", "        self.user_id = user_id\n", "        self.interests = []\n", "        self.history = []\n", "\n", "    def update_interests(self, new_interest):\n", "        if new_interest not in self.interests:\n", "            self.interests.append(new_interest)\n", "\n", "    def add_history(self, event):\n", "        self.history.append(event)\n", "\n", "    def get_profile(self):\n", "        return {\"user_id\": self.user_id, \"interests\": self.interests, \"history\": self.history}\n", "\n", "def content_based_recommendation(user_profile, events):\n", "    return [event for event in events if any(interest.lower() in event[\"title\"].lower() for interest in user_profile[\"interests\"])]\n", "\n", "user = UserProfile(\"user123\")\n", "user.update_interests(\"music\")\n", "print(\"Content-Based Recommendations:\", content_based_recommendation(user.get_profile(), [{\"title\": \"Jazz Concert\"}, {\"title\": \"Tech Meetup\"}]))\n", "\n", "# %% [markdown]\n", "# ### 3.2 Collaborative Filtering\n", "def collaborative_filtering(user_id, event_ratings):\n", "    similar_users = [uid for uid in event_ratings if uid != user_id]\n", "    return list({event for uid in similar_users for event, rating in event_ratings[uid].items() if rating >= 4})\n", "\n", "ratings = {\"user123\": {\"Jazz Concert\": 5, \"Tech Meetup\": 2}, \"user456\": {\"Jazz Concert\": 4, \"Art Expo\": 5}}\n", "print(\"Collaborative Filtering Recommendations:\", collaborative_filtering(\"user123\", ratings))\n", "\n", "# %% [markdown]\n", "# ### 3.3 RL-Based Recommender\n", "class RLRecommender:\n", "    def __init__(self):\n", "        self.q_table = {}\n", "\n", "    def get_action(self, state, actions):\n", "        if state not in self.q_table:\n", "            self.q_table[state] = {action: 0 for action in actions}\n", "        return random.choice(actions) if random.random() < 0.1 else max(self.q_table[state], key=self.q_table[state].get)\n", "\n", "rl_recommender = RLRecommender()\n", "print(\"RL Recommended Action:\", rl_recommender.get_action(\"user_state\", [\"Jazz Concert\", \"Tech Meetup\"]))\n", "\n", "# %% [markdown]\n", "# ### 3.4 Scheduling Agent\n", "def schedule_events(user_availability, events):\n", "    return [event for event in events if any(datetime.strptime(event[\"start_time\"], \"%Y-%m-%d %H:%M\") >= datetime.strptime(slot[\"start\"], \"%Y-%m-%d %H:%M\") and datetime.strptime(event[\"end_time\"], \"%Y-%m-%d %H:%M\") <= datetime.strptime(slot[\"end\"], \"%Y-%m-%d %H:%M\") for slot in user_availability)]\n", "\n", "print(\"Scheduled Events:\", schedule_events([{\"start\": \"2025-04-01 10:00\", \"end\": \"2025-04-01 18:00\"}], [{\"title\": \"Jazz Concert\", \"start_time\": \"2025-04-01 12:00\", \"end_time\": \"2025-04-01 14:00\"}]))\n", "\n", "# %% [markdown]\n", "# ### API Health Check\n", "def health_check():\n", "    return {\"status\": \"OK\"}\n", "\n", "print(\"API Health Check:\", health_check())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}