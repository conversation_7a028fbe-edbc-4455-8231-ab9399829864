{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting pydantic_ai\n", "  Downloading pydantic_ai-0.0.42-py3-none-any.whl.metadata (11 kB)\n", "Collecting pydantic-ai-slim==0.0.42 (from pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading pydantic_ai_slim-0.0.42-py3-none-any.whl.metadata (3.4 kB)\n", "Collecting eval-type-backport>=0.2.0 (from pydantic-ai-slim==0.0.42->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading eval_type_backport-0.2.2-py3-none-any.whl.metadata (2.2 kB)\n", "Collecting griffe>=1.3.2 (from pydantic-ai-slim==0.0.42->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading griffe-1.6.1-py3-none-any.whl.metadata (5.0 kB)\n", "Requirement already satisfied: httpx>=0.27 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pydantic-ai-slim==0.0.42->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (0.28.1)\n", "Collecting opentelemetry-api>=1.28.0 (from pydantic-ai-slim==0.0.42->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading opentelemetry_api-1.31.0-py3-none-any.whl.metadata (1.6 kB)\n", "Collecting pydantic-graph==0.0.42 (from pydantic-ai-slim==0.0.42->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading pydantic_graph-0.0.42-py3-none-any.whl.metadata (3.8 kB)\n", "Requirement already satisfied: pydantic>=2.10 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pydantic-ai-slim==0.0.42->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (2.10.6)\n", "Collecting typing-inspection>=0.4.0 (from pydantic-ai-slim==0.0.42->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading typing_inspection-0.4.0-py3-none-any.whl.metadata (2.6 kB)\n", "Collecting anthropic>=0.49.0 (from pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading anthropic-0.49.0-py3-none-any.whl.metadata (24 kB)\n", "Collecting boto3>=1.34.116 (from pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading boto3-1.37.16-py3-none-any.whl.metadata (6.7 kB)\n", "Collecting argcomplete>=3.5.0 (from pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading argcomplete-3.6.0-py3-none-any.whl.metadata (16 kB)\n", "Requirement already satisfied: prompt-toolkit>=3 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (3.0.50)\n", "Collecting rich>=13 (from pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading rich-13.9.4-py3-none-any.whl.metadata (18 kB)\n", "Collecting cohere>=5.13.11 (from pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading cohere-5.14.0-py3-none-any.whl.metadata (3.4 kB)\n", "Collecting groq>=0.15.0 (from pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading groq-0.20.0-py3-none-any.whl.metadata (15 kB)\n", "Collecting mcp>=1.4.1 (from pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading mcp-1.4.1-py3-none-any.whl.metadata (18 kB)\n", "Collecting mistralai>=1.2.5 (from pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading mistralai-1.5.2-py3-none-any.whl.metadata (29 kB)\n", "Collecting openai>=1.65.1 (from pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading openai-1.67.0-py3-none-any.whl.metadata (24 kB)\n", "Collecting google-auth>=2.36.0 (from pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading google_auth-2.38.0-py2.py3-none-any.whl.metadata (4.8 kB)\n", "Requirement already satisfied: requests>=2.32.3 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (2.32.3)\n", "Collecting logfire-api>=1.2.0 (from pydantic-graph==0.0.42->pydantic-ai-slim==0.0.42->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading logfire_api-3.9.0-py3-none-any.whl.metadata (971 bytes)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from anthropic>=0.49.0->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (4.8.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from anthropic>=0.49.0->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (1.9.0)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from anthropic>=0.49.0->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (0.8.2)\n", "Requirement already satisfied: sniffio in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from anthropic>=0.49.0->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (1.3.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.10 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from anthropic>=0.49.0->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (4.12.2)\n", "Collecting botocore<1.38.0,>=1.37.16 (from boto3>=1.34.116->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading botocore-1.37.16-py3-none-any.whl.metadata (5.7 kB)\n", "Collecting jmespath<2.0.0,>=0.7.1 (from boto3>=1.34.116->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading jmespath-1.0.1-py3-none-any.whl.metadata (7.6 kB)\n", "Collecting s3transfer<0.12.0,>=0.11.0 (from boto3>=1.34.116->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading s3transfer-0.11.4-py3-none-any.whl.metadata (1.7 kB)\n", "Collecting fastavro<2.0.0,>=1.9.4 (from cohere>=5.13.11->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading fastavro-1.10.0-cp312-cp312-win_amd64.whl.metadata (5.7 kB)\n", "Requirement already satisfied: httpx-sse==0.4.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from cohere>=5.13.11->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (0.4.0)\n", "Requirement already satisfied: pydantic-core<3.0.0,>=2.18.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from cohere>=5.13.11->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (2.27.2)\n", "Requirement already satisfied: tokenizers<1,>=0.15 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from cohere>=5.13.11->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (0.21.0)\n", "Collecting types-requests<3.0.0,>=2.0.0 (from cohere>=5.13.11->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading types_requests-2.32.0.20250306-py3-none-any.whl.metadata (2.3 kB)\n", "Collecting cachetools<6.0,>=2.0.0 (from google-auth>=2.36.0->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading cachetools-5.5.2-py3-none-any.whl.metadata (5.4 kB)\n", "Collecting pyasn1-modules>=0.2.1 (from google-auth>=2.36.0->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading pyasn1_modules-0.4.1-py3-none-any.whl.metadata (3.5 kB)\n", "Collecting rsa<5,>=3.1.4 (from google-auth>=2.36.0->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading rsa-4.9-py3-none-any.whl.metadata (4.2 kB)\n", "Requirement already satisfied: colorama>=0.4 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from griffe>=1.3.2->pydantic-ai-slim==0.0.42->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (0.4.6)\n", "Requirement already satisfied: certifi in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from httpx>=0.27->pydantic-ai-slim==0.0.42->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (2025.1.31)\n", "Requirement already satisfied: httpcore==1.* in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from httpx>=0.27->pydantic-ai-slim==0.0.42->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (1.0.7)\n", "Requirement already satisfied: idna in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from httpx>=0.27->pydantic-ai-slim==0.0.42->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (3.10)\n", "Requirement already satisfied: h11<0.15,>=0.13 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from httpcore==1.*->httpx>=0.27->pydantic-ai-slim==0.0.42->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (0.14.0)\n", "Requirement already satisfied: pydantic-settings>=2.5.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from mcp>=1.4.1->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (2.7.1)\n", "Collecting sse-starlette>=1.6.1 (from mcp>=1.4.1->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading sse_starlette-2.2.1-py3-none-any.whl.metadata (7.8 kB)\n", "Collecting starlette>=0.27 (from mcp>=1.4.1->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading starlette-0.46.1-py3-none-any.whl.metadata (6.2 kB)\n", "Collecting uvicorn>=0.23.1 (from mcp>=1.4.1->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading uvicorn-0.34.0-py3-none-any.whl.metadata (6.5 kB)\n", "Collecting jsonpath-python>=1.0.6 (from mistralai>=1.2.5->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading jsonpath_python-1.0.6-py3-none-any.whl.metadata (12 kB)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from mistralai>=1.2.5->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (2.9.0.post0)\n", "Requirement already satisfied: typing-inspect>=0.9.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from mistralai>=1.2.5->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (0.9.0)\n", "Requirement already satisfied: tqdm>4 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from openai>=1.65.1->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (4.67.1)\n", "Collecting deprecated>=1.2.6 (from opentelemetry-api>=1.28.0->pydantic-ai-slim==0.0.42->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading Deprecated-1.2.18-py2.py3-none-any.whl.metadata (5.7 kB)\n", "Collecting importlib-metadata<8.7.0,>=6.0 (from opentelemetry-api>=1.28.0->pydantic-ai-slim==0.0.42->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading importlib_metadata-8.6.1-py3-none-any.whl.metadata (4.7 kB)\n", "Requirement already satisfied: wcwidth in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from prompt-toolkit>=3->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (0.2.13)\n", "Requirement already satisfied: annotated-types>=0.6.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pydantic>=2.10->pydantic-ai-slim==0.0.42->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (0.7.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests>=2.32.3->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (3.4.1)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests>=2.32.3->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (2.3.0)\n", "Collecting markdown-it-py>=2.2.0 (from rich>=13->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading markdown_it_py-3.0.0-py3-none-any.whl.metadata (6.9 kB)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from rich>=13->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (2.19.1)\n", "Collecting wrapt<2,>=1.10 (from deprecated>=1.2.6->opentelemetry-api>=1.28.0->pydantic-ai-slim==0.0.42->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading wrapt-1.17.2-cp312-cp312-win_amd64.whl.metadata (6.5 kB)\n", "Collecting zipp>=3.20 (from importlib-metadata<8.7.0,>=6.0->opentelemetry-api>=1.28.0->pydantic-ai-slim==0.0.42->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading zipp-3.21.0-py3-none-any.whl.metadata (3.7 kB)\n", "Collecting mdurl~=0.1 (from markdown-it-py>=2.2.0->rich>=13->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading mdurl-0.1.2-py3-none-any.whl.metadata (1.6 kB)\n", "Collecting pyasn1<0.7.0,>=0.4.6 (from pyasn1-modules>=0.2.1->google-auth>=2.36.0->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai)\n", "  Downloading pyasn1-0.6.1-py3-none-any.whl.metadata (8.4 kB)\n", "Requirement already satisfied: python-dotenv>=0.21.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pydantic-settings>=2.5.2->mcp>=1.4.1->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (1.0.1)\n", "Requirement already satisfied: six>=1.5 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from python-dateutil>=2.8.2->mistralai>=1.2.5->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (1.17.0)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.16.4 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tokenizers<1,>=0.15->cohere>=5.13.11->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (0.28.1)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from typing-inspect>=0.9.0->mistralai>=1.2.5->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (1.0.0)\n", "Requirement already satisfied: click>=7.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from uvicorn>=0.23.1->mcp>=1.4.1->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (8.1.8)\n", "Requirement already satisfied: filelock in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers<1,>=0.15->cohere>=5.13.11->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (3.17.0)\n", "Requirement already satisfied: fsspec>=2023.5.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers<1,>=0.15->cohere>=5.13.11->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (2025.2.0)\n", "Requirement already satisfied: packaging>=20.9 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers<1,>=0.15->cohere>=5.13.11->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (24.2)\n", "Requirement already satisfied: pyyaml>=5.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers<1,>=0.15->cohere>=5.13.11->pydantic-ai-slim[anthropic,bedrock,cli,cohere,groq,mcp,mistral,openai,vertexai]==0.0.42->pydantic_ai) (6.0.2)\n", "Downloading pydantic_ai-0.0.42-py3-none-any.whl (9.7 kB)\n", "Downloading pydantic_ai_slim-0.0.42-py3-none-any.whl (140 kB)\n", "Downloading pydantic_graph-0.0.42-py3-none-any.whl (25 kB)\n", "Downloading anthropic-0.49.0-py3-none-any.whl (243 kB)\n", "Downloading argcomplete-3.6.0-py3-none-any.whl (43 kB)\n", "Downloading boto3-1.37.16-py3-none-any.whl (139 kB)\n", "Downloading cohere-5.14.0-py3-none-any.whl (253 kB)\n", "Downloading eval_type_backport-0.2.2-py3-none-any.whl (5.8 kB)\n", "Downloading google_auth-2.38.0-py2.py3-none-any.whl (210 kB)\n", "Downloading griffe-1.6.1-py3-none-any.whl (128 kB)\n", "Downloading groq-0.20.0-py3-none-any.whl (124 kB)\n", "Downloading mcp-1.4.1-py3-none-any.whl (72 kB)\n", "Downloading mistralai-1.5.2-py3-none-any.whl (278 kB)\n", "Downloading openai-1.67.0-py3-none-any.whl (580 kB)\n", "   ---------------------------------------- 0.0/580.2 kB ? eta -:--:--\n", "   ------------------ --------------------- 262.1/580.2 kB ? eta -:--:--\n", "   ------------------------------------ --- 524.3/580.2 kB 1.4 MB/s eta 0:00:01\n", "   ---------------------------------------- 580.2/580.2 kB 1.1 MB/s eta 0:00:00\n", "Downloading opentelemetry_api-1.31.0-py3-none-any.whl (65 kB)\n", "Downloading rich-13.9.4-py3-none-any.whl (242 kB)\n", "Downloading typing_inspection-0.4.0-py3-none-any.whl (14 kB)\n", "Downloading botocore-1.37.16-py3-none-any.whl (13.4 MB)\n", "   ---------------------------------------- 0.0/13.4 MB ? eta -:--:--\n", "    --------------------------------------- 0.3/13.4 MB ? eta -:--:--\n", "   - -------------------------------------- 0.5/13.4 MB 1.7 MB/s eta 0:00:08\n", "   --- ------------------------------------ 1.0/13.4 MB 1.8 MB/s eta 0:00:07\n", "   --- ------------------------------------ 1.3/13.4 MB 1.8 MB/s eta 0:00:07\n", "   ---- ----------------------------------- 1.6/13.4 MB 1.8 MB/s eta 0:00:07\n", "   ------ --------------------------------- 2.1/13.4 MB 1.8 MB/s eta 0:00:07\n", "   ------- -------------------------------- 2.4/13.4 MB 1.8 MB/s eta 0:00:07\n", "   -------- ------------------------------- 2.9/13.4 MB 1.8 MB/s eta 0:00:06\n", "   --------- ------------------------------ 3.1/13.4 MB 1.8 MB/s eta 0:00:06\n", "   ---------- ----------------------------- 3.7/13.4 MB 1.8 MB/s eta 0:00:06\n", "   ----------- ---------------------------- 3.9/13.4 MB 1.8 MB/s eta 0:00:06\n", "   ------------- -------------------------- 4.5/13.4 MB 1.8 MB/s eta 0:00:05\n", "   -------------- ------------------------- 4.7/13.4 MB 1.8 MB/s eta 0:00:05\n", "   --------------- ------------------------ 5.2/13.4 MB 1.8 MB/s eta 0:00:05\n", "   ---------------- ----------------------- 5.5/13.4 MB 1.8 MB/s eta 0:00:05\n", "   ----------------- ---------------------- 6.0/13.4 MB 1.8 MB/s eta 0:00:05\n", "   ------------------ --------------------- 6.3/13.4 MB 1.8 MB/s eta 0:00:04\n", "   -------------------- ------------------- 6.8/13.4 MB 1.8 MB/s eta 0:00:04\n", "   --------------------- ------------------ 7.1/13.4 MB 1.8 MB/s eta 0:00:04\n", "   ---------------------- ----------------- 7.6/13.4 MB 1.9 MB/s eta 0:00:04\n", "   ----------------------- ---------------- 7.9/13.4 MB 1.9 MB/s eta 0:00:03\n", "   ------------------------ --------------- 8.4/13.4 MB 1.9 MB/s eta 0:00:03\n", "   ------------------------- -------------- 8.7/13.4 MB 1.9 MB/s eta 0:00:03\n", "   --------------------------- ------------ 9.2/13.4 MB 1.9 MB/s eta 0:00:03\n", "   ---------------------------- ----------- 9.4/13.4 MB 1.9 MB/s eta 0:00:03\n", "   ----------------------------- ---------- 10.0/13.4 MB 1.9 MB/s eta 0:00:02\n", "   ------------------------------ --------- 10.2/13.4 MB 1.8 MB/s eta 0:00:02\n", "   ------------------------------- -------- 10.5/13.4 MB 1.8 MB/s eta 0:00:02\n", "   ------------------------------- -------- 10.5/13.4 MB 1.8 MB/s eta 0:00:02\n", "   -------------------------------- ------- 10.7/13.4 MB 1.8 MB/s eta 0:00:02\n", "   -------------------------------- ------- 11.0/13.4 MB 1.8 MB/s eta 0:00:02\n", "   --------------------------------- ------ 11.3/13.4 MB 1.7 MB/s eta 0:00:02\n", "   ---------------------------------- ----- 11.5/13.4 MB 1.7 MB/s eta 0:00:02\n", "   ----------------------------------- ---- 11.8/13.4 MB 1.7 MB/s eta 0:00:01\n", "   ----------------------------------- ---- 12.1/13.4 MB 1.7 MB/s eta 0:00:01\n", "   ------------------------------------ --- 12.3/13.4 MB 1.7 MB/s eta 0:00:01\n", "   ------------------------------------- -- 12.6/13.4 MB 1.6 MB/s eta 0:00:01\n", "   -------------------------------------- - 12.8/13.4 MB 1.6 MB/s eta 0:00:01\n", "   ---------------------------------------  13.1/13.4 MB 1.6 MB/s eta 0:00:01\n", "   ---------------------------------------- 13.4/13.4 MB 1.6 MB/s eta 0:00:00\n", "Downloading cachetools-5.5.2-py3-none-any.whl (10 kB)\n", "Downloading Deprecated-1.2.18-py2.py3-none-any.whl (10.0 kB)\n", "Downloading fastavro-1.10.0-cp312-cp312-win_amd64.whl (487 kB)\n", "Downloading importlib_metadata-8.6.1-py3-none-any.whl (26 kB)\n", "Downloading jmespath-1.0.1-py3-none-any.whl (20 kB)\n", "Downloading jsonpath_python-1.0.6-py3-none-any.whl (7.6 kB)\n", "Downloading logfire_api-3.9.0-py3-none-any.whl (77 kB)\n", "Downloading markdown_it_py-3.0.0-py3-none-any.whl (87 kB)\n", "Downloading pyasn1_modules-0.4.1-py3-none-any.whl (181 kB)\n", "Downloading rsa-4.9-py3-none-any.whl (34 kB)\n", "Downloading s3transfer-0.11.4-py3-none-any.whl (84 kB)\n", "Downloading sse_starlette-2.2.1-py3-none-any.whl (10 kB)\n", "Downloading starlette-0.46.1-py3-none-any.whl (71 kB)\n", "Downloading types_requests-2.32.0.20250306-py3-none-any.whl (20 kB)\n", "Downloading uvicorn-0.34.0-py3-none-any.whl (62 kB)\n", "Downloading mdurl-0.1.2-py3-none-any.whl (10.0 kB)\n", "Downloading pyasn1-0.6.1-py3-none-any.whl (83 kB)\n", "Downloading wrapt-1.17.2-cp312-cp312-win_amd64.whl (38 kB)\n", "Downloading zipp-3.21.0-py3-none-any.whl (9.6 kB)\n", "Installing collected packages: zipp, wrapt, typing-inspection, types-requests, pyasn1, mdurl, logfire-api, jsonpath-python, jmespath, griffe, fastavro, eval-type-backport, cachetools, argcomplete, uvicorn, starlette, rsa, pyasn1-modules, markdown-it-py, importlib-metadata, deprecated, botocore, sse-starlette, s3transfer, rich, pydantic-graph, opentelemetry-api, openai, mistralai, groq, google-auth, anthropic, pydantic-ai-slim, mcp, cohere, boto3, pydantic_ai\n", "  Attempting uninstall: openai\n", "    Found existing installation: openai 1.63.2\n", "    Uninstalling openai-1.63.2:\n", "      Successfully uninstalled openai-1.63.2\n", "Successfully installed anthropic-0.49.0 argcomplete-3.6.0 boto3-1.37.16 botocore-1.37.16 cachetools-5.5.2 cohere-5.14.0 deprecated-1.2.18 eval-type-backport-0.2.2 fastavro-1.10.0 google-auth-2.38.0 griffe-1.6.1 groq-0.20.0 importlib-metadata-8.6.1 jmespath-1.0.1 jsonpath-python-1.0.6 logfire-api-3.9.0 markdown-it-py-3.0.0 mcp-1.4.1 mdurl-0.1.2 mistralai-1.5.2 openai-1.67.0 opentelemetry-api-1.31.0 pyasn1-0.6.1 pyasn1-modules-0.4.1 pydantic-ai-slim-0.0.42 pydantic-graph-0.0.42 pydantic_ai-0.0.42 rich-13.9.4 rsa-4.9 s3transfer-0.11.4 sse-starlette-2.2.1 starlette-0.46.1 types-requests-2.32.0.20250306 typing-inspection-0.4.0 uvicorn-0.34.0 wrapt-1.17.2 zipp-3.21.0\n"]}], "source": ["!pip install pydantic_ai "]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["import os\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "\n", "\n", "GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\selector_events.py:315: RuntimeWarning: coroutine 'main' was never awaited\n", "  def _add_writer(self, fd, callback, *args):\n", "RuntimeWarning: Enable tracemalloc to get the object allocation traceback\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\"Hello, world!\" originated as a sample program in the 1978 book *The C Programming Language*.\n", "\n"]}], "source": ["import nest_asyncio\n", "nest_asyncio.apply()\n", "\n", "from pydantic_ai import Agent\n", "\n", "agent = Agent(\n", "    'google-gla:gemini-1.5-flash',\n", "    system_prompt='Be concise, reply with one sentence.'\n", ")\n", "\n", "result = agent.run_sync('Where does \"hello world\" come from?')\n", "print(result.data)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}