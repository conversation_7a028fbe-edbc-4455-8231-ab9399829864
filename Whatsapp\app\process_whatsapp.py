import time
from helper.logger_setup import setup_logger
from whatsapp.sender_whatsapp import *
from DB.db_ops import get_solar_report_data, get_wind_report_data, get_combine_report_data
import os

logging = setup_logger('process_whatsapp', 'process_whatsapp.log')

PUBLIC_URL =os.getenv('PUBLIC_URL')

def send_yes_solar(plant, customer, yesterday, report_type, number):
    try:
        logging.info(f"Processing report for {plant} - {customer}")

        report_data = get_solar_report_data(plant, yesterday)
        poa = report_data["poa"]
        dgr_path = report_data["dgr_path"]
        pr = report_data["pr"]
        generation = report_data["generation"]

        # Upload the media
        media_id = upload_whatsapp_media(dgr_path)
        if not media_id:
            logging.error("Media upload failed, media_id is None")
            return

        caption = f"Hello {customer},\nAttached is the *Daily Generation Report (DGR)* of Date *{yesterday}*."
        file_name = f"{plant}_Solar_DGR_{yesterday}.pdf"
        send_whatsapp_document(number, media_id, caption, file_name)
        time.sleep(3)
        delete_whatsapp_media(media_id)

    except Exception as e:
        logging.error(f"Error processing report for {plant}: {e}", exc_info=True)







def send_yes_wind(plant, customer, yesterday, report_type, number):
    try:
        logging.info(f"Processing report for {plant} - {customer}")
        
        report_data = get_wind_report_data(plant, yesterday)
        wind_speed = report_data["wind_speed"]
        dgr_path = report_data["dgr_path"]
        generation = report_data["generation"]

        # Upload the media
        media_id = upload_whatsapp_media(dgr_path)
        if not media_id:
            logging.error("Media upload failed, media_id is None")
            return

        
        caption = f"Hello *{customer}*,\nAttached is the *Daily Generation Report (DGR)* of Date *{yesterday}*."
        file_name = f"{plant}_Wind_DGR_{yesterday}.pdf"
        
        send_whatsapp_document(number, media_id, caption, file_name)
        time.sleep(3)
        delete_whatsapp_media(media_id)
    
    except Exception as e:
        logging.error(f"Error processing report for {plant}: {e}", exc_info=True)






def send_yes_combined(plant, customer, yesterday, report_type, number):
    try:
        logging.info(f"Processing report for {plant} - {customer}")
        
        report_data = get_combine_report_data(plant, yesterday)
        
        dgr_path = report_data["dgr_path"]
        
        # Upload the media
        media_id = upload_whatsapp_media(dgr_path)
        if not media_id:
            logging.error("Media upload failed, media_id is None")
            return
        caption = f"Hello *{customer}*,\nAttached is the *Daily Generation Report (DGR)* of Date *{yesterday}*."
        file_name = f"{plant}_Combined_DGR_{yesterday}.pdf"
        
        send_whatsapp_document(number, media_id, caption, file_name)
        time.sleep(3)
        delete_whatsapp_media(media_id)
    
    except Exception as e:
        logging.error(f"Error processing report for {plant}: {e}", exc_info=True)

   