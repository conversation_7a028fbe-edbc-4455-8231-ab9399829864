{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ![Prescinto](https://prescinto.ai/wp-content/uploads/2023/06/Frame-7-3-1-1.svg)\n", "#### pAPI Wrapper Function's Sample Notebook"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "u93-l4Aww3ZJ"}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "markdown", "metadata": {"id": "iz5Wtjwww3ZX"}, "source": ["# Prescinto API (pAPI) access control\n", "\n", "* pAPI access is controlled using API token.\n", "- pAPI Validates each incoming request by API token.\n", "* API token can be aquire from [Prescinto Cloud](https://cloud.prescinto.ai)\n", "* Prescinto API token has a expiry as well."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Prescinto Library\n", "## *PrescintoIntegrationUtilities* is a utility that has  wrapper function for each pAPI request.\n", "### User can directly consume these functions/methods rather than direct restapi call.\n", "##### This file is being documented only for Wind Domain. So In this document we will go through diffrent-different function calls for Wind Domain."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\anaconda3\\envs\\jupyterlab\\lib\\site-packages\\requests\\__init__.py:102: RequestsDependencyWarning: urllib3 (1.26.12) or chardet (5.2.0)/charset_normalizer (2.0.12) doesn't match a supported version!\n", "  warnings.warn(\"urllib3 ({}) or chardet ({})/charset_normalizer ({}) doesn't match a supported \"\n"]}], "source": ["from IntegrationUtilities import PrescintoIntegrationUtilities"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### PrescintoIntegrationUtilities has two paramerters\n", "- ```Server(str,optional):``` Name of the Server for fetching data. [**Prescinto**](https://prescinto.ai) has its servers in different regions in all over the world. If No server choice is given, It will automatically connect with your nearby server. i.e.\n", "   ```python\n", "       server = 'IN' or'EU' or 'US' \n", "   ```\n", "- ```token(str, required):``` Api token for validating the api request. i.e.\n", "   ````python\n", "    token = 'API token'\n", "    ````\n", "```Note:``` Without ````API Token```` Request will not validated.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "eP5Yl3C5w3Zh"}, "outputs": [], "source": ["m = PrescintoIntegrationUtilities(server = 'IN',token ='370E0979-EEA7-4715-88FC-9xxxxxx')"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["### Common Variables -\n", "- ```Plant Name:``` Consist Plant Name for which we will fetch information\n", "- ```Start Date:``` Minimum Date for Data filter\n", "- ```End Date:``` Maximum Date for Data filter\n", "\n", "**```Note:```** *Start date and End date is being used where need to pull data for a specified daterange.*"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "GucEULl5w3Zm"}, "outputs": [], "source": ["plantName = 'IN.DEMO.SIND'\n", "startDate = '2024-08-01'\n", "endDate = '2024-08-30'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Wind plant is configured in the following way:\n", "### There's Turbine, inside each turbine there are components of turbine, and there's signals/tags associated with each component\n", "- Turbine1\n", "    - Generator\n", "        - Active power\n", "    - <PERSON><PERSON><PERSON>\n", "        - Wind Speed"]}, {"cell_type": "markdown", "metadata": {"id": "T5_Rz9FVw3Zu"}, "source": ["## getWindPlantInfo(): provides details of devices and parameters of the Wind plant\n", "### This method is used to retrieve information about a plant template.\n", "#### This function consume plant name as parameters.\n", "#### Returns Plant categories, parameters and devices. \n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "_hKbF1Yaw3Zx"}, "outputs": [], "source": ["categories = None\n", "deviceDict = None\n", "parameterDict = None\n", "try:\n", "    categories, deviceDict, parameterDict,componentTagsDict = m.getWindPlantInfo(plantName)\n", "except Exception as e:\n", "    print(e)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['Converter', 'Generator', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Rotor', 'Tower', 'Transmission', 'General', 'Yawing', '<PERSON>'])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["componentTagsDict.keys()"]}, {"cell_type": "markdown", "metadata": {"id": "bdiIXp_Qw3Zz"}, "source": ["## Categories\n", "### Devices are segregated into categories. Most useful categories are:\n", "- *````Turbine :````* Turbines\n", "- *````Wind Plant :````* Consolidated plant level measurements just before interconnection\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"id": "o6g6wdt_w3Z0", "outputId": "de19a444-06fb-4815-c92e-99dccecf805b"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Plant', 'Turbine']\n"]}], "source": ["if categories is not None:\n", "    print(categories)"]}, {"cell_type": "markdown", "metadata": {"id": "wFeJTbLIw3Z4"}, "source": ["## Category devices\n", "### *````There is defined devices for each of the category````*"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "t_696t9_w3Z5", "outputId": "e1d9f031-84c6-44d7-9cf7-6b52639090c5"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Devices of Turbine:\n", "['GGM05', 'GGM06', 'GGM100', 'GGM101', 'GGM102', 'GGM105', 'GGM111', 'GGM142', 'GGM99', 'PTT1']\n"]}], "source": ["# Dictionary of devices based on categories\n", "if deviceDict is not None:\n", "    print(f'Devices of Turbine:\\n{deviceDict[\"Turbine\"]}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {"id": "SpYMHU05w3Z7"}, "source": ["## Category Parameters\n", "### *````There is defined parameters for each of the turbine````*"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"id": "hZlsL9hKw3Z8", "outputId": "54e8de84-1598-4cdc-dd99-963461755ff7"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Parameters of Turbine:\n", "\n", "['Converter', 'Generator', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', 'Transmission', 'General', 'Yawing']\n", "\n", "\n"]}], "source": ["# Dictionary of parameters\n", "if parameterDict is not None:\n", "    print(f'Parameters of Turbine:\\n')\n", "    print(parameterDict['GGM06'])\n", "    print('\\n')"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'Converter': ['Active-Power',\n", "  'Ambient-Temperature',\n", "  'Cabinate 1 Temp',\n", "  'Cabinate 1 Temp Max',\n", "  'Cabinate 1 Temp Min',\n", "  'Cabinate 2 Temp',\n", "  'Cabinate 2 Temp Max',\n", "  'Cabinate 2 Temp Min',\n", "  'Converter Status Max',\n", "  'Converter Status Min',\n", "  'DC Bus Voltage',\n", "  'DC Bus Voltage Max',\n", "  'DC Bus Voltage Min',\n", "  'Wind-Direction',\n", "  'Wind-Speed'],\n", " 'Generator': ['Active-Power',\n", "  'Ambient-Temperature',\n", "  'Choke Temperature',\n", "  'Choke Temperature Max',\n", "  'Common Mode Inductor Temp',\n", "  'Common Mode Inductor Temp Max',\n", "  'DE BEaring Temperature',\n", "  'DE Bearing Temperature Max',\n", "  'Generator RPM',\n", "  'Generator RPM Max',\n", "  'Generator RPM Min',\n", "  'Generator speed',\n", "  'Generator Winding U Temp',\n", "  'Generator Winding U Temp Max',\n", "  'Generator Winding V Temp',\n", "  'Generator Winding V Temp Max',\n", "  'Generator Winding W Temp',\n", "  'Generator Winding W Temp Max',\n", "  'NDE BEaring Temperature',\n", "  'NDE Bearing Temperature Max',\n", "  'Water Coolent Plant Temp ',\n", "  'Water Coolent Plant Temp Max',\n", "  'Water Temp Input',\n", "  'Water Temp Input Max',\n", "  'Wind-Direction',\n", "  'Wind-Speed'],\n", " 'Grid': ['Active-Power',\n", "  'Ambient-Temperature',\n", "  'Grid Frequency',\n", "  'Grid Frequency Max',\n", "  'Grid Frequency Max',\n", "  'Grid Voltage Phase AB',\n", "  'Grid Voltage Phase AB Max',\n", "  'Grid Voltage Phase AB Min',\n", "  'Grid Voltage Phase BC',\n", "  'Grid Voltage Phase BC Max',\n", "  'Grid Voltage Phase BC Min',\n", "  'Grid Voltage Phase CA',\n", "  'Grid Voltage Phase CA Max',\n", "  'Grid Voltage Phase CA Min',\n", "  'GSC Active Current',\n", "  'GSC Active Current Max',\n", "  'GSC Active Current Min',\n", "  'GSC IGBT Temp',\n", "  'GSC IGBT Temp Max',\n", "  'GSC Reactive Current',\n", "  'GSC Reactive Current Max',\n", "  'GSC Reactive Current Min',\n", "  'Line Choke Temp',\n", "  'Line Choke Temp',\n", "  'LSC Active Current',\n", "  'LSC Active Current Max',\n", "  'LSC Active Current Min',\n", "  'LSC IGBT Temp',\n", "  'LSC IGBT Temp Max',\n", "  'LSC Reactive Current',\n", "  'LSC Reactive Current Max',\n", "  'LSC Reactive Current Min',\n", "  'Wind-Direction',\n", "  'Wind-Speed'],\n", " 'Nacelle': ['Active-Power',\n", "  'Ambient Temperature',\n", "  'Cabinate 1 Temp',\n", "  'Cabinate 1 Temp Max',\n", "  'Cabinate 2 Temp',\n", "  'Cabinate 2 Temp Max',\n", "  'Nacelle Direction',\n", "  'Na<PERSON><PERSON>',\n", "  'Na<PERSON>e Po<PERSON> Error Max',\n", "  'Nacelle Position Error Min',\n", "  '<PERSON><PERSON><PERSON>',\n", "  '<PERSON><PERSON><PERSON>',\n", "  'Temperature Nacelle',\n", "  'Wind Direction',\n", "  'Wind Direction Max',\n", "  'Wind Direction Min',\n", "  'Wind Direction North',\n", "  'Wind Direction North Max',\n", "  'Wind Direction North Min',\n", "  'Wind Direction Sensor 1',\n", "  'Wind Direction Sensor 1 Max',\n", "  'Wind Direction Sensor 1 Min',\n", "  'Wind Direction Sensor 2',\n", "  'Wind Direction Sensor 2 Max',\n", "  'Wind Direction Sensor 2 Min',\n", "  'Wind Speed',\n", "  'Wind Speed Max',\n", "  'Wind Speed Min',\n", "  'Wind Speed Sensor 1',\n", "  'Wind Speed Sensor 1 Max',\n", "  'Wind Speed Sensor 1 Min',\n", "  'Wind Speed Sensor 2',\n", "  'Wind Speed Sensor 2 Max',\n", "  'Wind Speed Sensor 2 Min',\n", "  'Wind Speed Turbulence Intensity',\n", "  'Wind Speed Turbulence Intensity Max',\n", "  'Wind Speed Turbulence Intensity Min'],\n", " 'Rotor': ['Active-Power',\n", "  'Ambient-Temperature',\n", "  'Average Pitch Angle',\n", "  'Blade 1 Battery Status Min',\n", "  'Blade 1 Battery Votage Max',\n", "  'Blade 1 Battery Votage Min',\n", "  'Blade 1 Cabinate Temperature',\n", "  'Blade 1 Converter Internal Temperature',\n", "  'Blade 1 Diagnostic Voltage',\n", "  'Blade 1 Motor Temperature',\n", "  'Blade 1 Position',\n", "  'Blade 1 Position Max',\n", "  'Blade 1 Position Min',\n", "  'Blade 1 Status Min',\n", "  'Blade 1 Torque rms',\n", "  'Blade 2 Battery Status Min',\n", "  'Blade 2 Battery Votage Max',\n", "  'Blade 2 Battery Votage Min',\n", "  'Blade 2 Cabinate Temperature',\n", "  'Blade 2 Converter Internal Temperature',\n", "  'Blade 2 Diagnostic Voltage',\n", "  'Blade 2 Motor Temperature',\n", "  'Blade 2 Position',\n", "  'Blade 2 Position Max',\n", "  'Blade 2 Position Min',\n", "  'Blade 2 Status Min',\n", "  'Blade 2 Torque rms',\n", "  'Blade 3 Battery Status Min',\n", "  'Blade 3 Battery Votage Max',\n", "  'Blade 3 Battery Votage Min',\n", "  'Blade 3 Cabinate Temperature',\n", "  'Blade 3 Converter Internal Temperature',\n", "  'Blade 3 Diagnostic Voltage',\n", "  'Blade 3 Motor Temperature',\n", "  'Blade 3 Position',\n", "  'Blade 3 Position Max',\n", "  'Blade 3 Position Min',\n", "  'Blade 3 Status Min',\n", "  'Blade 3 Torque rms',\n", "  'Hub Control Stall Detection Power',\n", "  'Hub Control Stall Detection Power Max',\n", "  'Hub Control Stall Detection Power Min',\n", "  'Pitch Angle Blade 1',\n", "  'Pitch Angle Blade 2',\n", "  'Pitch Angle Blade 3',\n", "  'Rotor RPM Max',\n", "  'Rotor RPM Min',\n", "  'Rotor Speed RPM',\n", "  'Stall Detection',\n", "  'Torque Control Avg',\n", "  'Torque Control Avg Max',\n", "  'Torque Control Avg Min',\n", "  'Torque Control Drive Swing',\n", "  'Torque Control Drive Swing Max',\n", "  'Torque Control Drive Swing Min',\n", "  'Wind-Direction',\n", "  'Wind-Speed'],\n", " 'Tower': ['Active-Power',\n", "  'Ambient-Temperature',\n", "  'Drive End Vibration',\n", "  'Drive End Vibration Max',\n", "  'Drive End Vibration Min',\n", "  'Non Drive End Vibration',\n", "  'Non Drive End Vibration Max',\n", "  'Non Drive End Vibration Min',\n", "  'Tower Cable temp',\n", "  'Tower Cable temp Max',\n", "  'Tower Temparature',\n", "  'Tower Temparature Max',\n", "  'Tower Temparature Min',\n", "  'Wind-Direction',\n", "  'Wind-Speed'],\n", " 'Transmission': ['Active-Power',\n", "  'Ambient-Temperature',\n", "  'Gear Oil External Heater Temperature',\n", "  'Gear Oil External Heater Temperature Max',\n", "  'Gear Oil Inlet Pressure',\n", "  'Gear Oil Inlet Pressure Max',\n", "  'Gear Oil Inlet Pressure Min',\n", "  'Gear Oil Inlet Temperature',\n", "  'Gear Oil Inlet Temperature Max',\n", "  'Gear Oil Tank Temperature',\n", "  'Gear Oil Tank Temperature Max',\n", "  'Gear Rotor Bearing Temperature',\n", "  'Gear Rotor Bearing Temperature Max',\n", "  'Main Bearing 1 Temp',\n", "  'Main Bearing 1 Temp Max',\n", "  'Main Bearing 2 Temp',\n", "  'Main Bearing 2 Temp Max',\n", "  'Main Bearing 3 Temp',\n", "  'Main Bearing 3 Temp Max',\n", "  'Wind-Direction',\n", "  'Wind-Speed'],\n", " 'General': ['Active power',\n", "  'Active Power Max',\n", "  'Active Power Min',\n", "  'Ambient Temp Max',\n", "  'Ambient Temp Min',\n", "  'Ambient-Temperature',\n", "  'Aux Power',\n", "  'Export Energy',\n", "  'Generation today',\n", "  'Import Energy',\n", "  'Instantaneous Energy',\n", "  'PLF',\n", "  'Power Curve Validation Min',\n", "  'Power Limit Information',\n", "  'Reactive Energy',\n", "  'Reactive power',\n", "  'Reactive Power Max',\n", "  'Reactive Power Min',\n", "  'Status',\n", "  'Turbine status',\n", "  'Turbine Status Max',\n", "  'Turbine Status Max - N',\n", "  'Turbine Status Min',\n", "  'Turbine Status Min - N',\n", "  'Turbine Status Old Max',\n", "  'Turbine Status Old Max - N',\n", "  'Turbine Status Old Min',\n", "  'Turbine Status Old Min- N',\n", "  'Turbine Substatus Max',\n", "  'Turbine Substatus Min',\n", "  'Wind Curve Validation Min',\n", "  'Wind-Direction',\n", "  'Wind-Speed'],\n", " 'Yawing': ['Active-Power',\n", "  'Ambient-Temperature',\n", "  'Power Consumption',\n", "  'Power Consumption Max',\n", "  'Wind-Direction',\n", "  'Wind-Speed',\n", "  'Yaw position Error Max',\n", "  'Yaw Status Max',\n", "  'Yaw Status Min'],\n", " 'Plant': ['Ambient Temperature',\n", "  'Avg Wind Speed of the farm',\n", "  'Generation today',\n", "  'PLF',\n", "  'Total Active Power',\n", "  'Total Generation',\n", "  'Total Reactive Power',\n", "  'Wind Direction']}"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["componentTagsDict"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### ComponentTagsDict gives tags inside each component of turbine. This is going to be common across all Turbines"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["['Ambient Temperature',\n", " 'Avg Wind Speed of the farm',\n", " 'Generation today',\n", " 'PLF',\n", " 'Total Active Power',\n", " 'Total Generation',\n", " 'Total Reactive Power',\n", " 'Wind Direction']"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["componentTagsDict['Plant']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {"id": "TRiCLXsaw3Z-"}, "source": ["## Data Fetching \n", "### fetchDataV2() is used to fetch time series data from database using pAPI\n", "#### ````Input Parameters:````\n", "   - *````Plant Name :````* (string, required)\n", "   - *````Category :````* (list, required)\n", "   - *````Parameter :````* (list, required)\n", "   - *````Devices :````* (Select devices should be passed).\n", "   - *````Start Date :````* (string, format: 'YYYY-MM-DD HH:MM:SS' or 'YYYY-MM-DD' or 'YYYY-MM-DD HH:MM:SS±hh:mm'): The start date and time for data retrieval.       \n", "   - *````End Date :````* (string, format: 'YYYY-MM-DD HH:MM:SS' or 'YYYY-MM-DD' or 'YYYY-MM-DD HH:MM:SS±hh:mm'): The end date and time for data retrieval.\n", "   - *````granularity :````* (String, optional):  The time granularity of the retrieved data.\n", "        - Seconds - '1s', '2s'\n", "        - Minute - '1m', '5m' \n", "        - Hour - '1h', '2h'\n", "        - Day - '1d', '2d'\n", "        - Week - '1w', '2w'\n", "        -  Month - '30d' or '31d'\n", "   Default would be ***5m***\n", "   - *````condition :````* (Dictionary, optional)\n", "        - Should pass a dictionary where key is the parameter and value is the aggregation to be performed\n", "        - Aggregation supported:\n", "            - mean\n", "            - median\n", "            - mode\n", "            - sum\n", "            - first\n", "            - last\n", "            - max\n", "            - min\n", "            - st<PERSON><PERSON>\n", "            - spread\n", "            - count\n", "            - distinct\n", "            - integral\n", "        - default would be first\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Note**: Same parameters will be used for fetchDatav2. Only values for these parameters will be change."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### For Parameters,component short name should be passed alongwith the Parameter name. \n", "#### Mapping of Component Short names with Components::\n", "- Converter - WCONV\n", "- Generator - WGEN\n", "- Grid - WGRD\n", "- Nacelle - WNAC\n", "- Rotor - WROT\n", "- Tower - WTOW\n", "- Transmission - WTRM\n", "- General - WTUR\n", "- Yawing - WYAW\n", "- Container - WCNT\n", "- Gearbox - WGBX\n", "- <PERSON><PERSON> - WGRC\n", "- Transformer - WTRF\n", "- Counter - WCTR\n", "\n", "**For Example**, If user requires Wind Direction which comes from Nacelle, then **```WNAC.Wind Direction```** needs to be passed as Parameter\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### For Plant related tags, use Wind Plant as category. For Turbine, use Turbine. For Wind, data can only be fetched for one category at a time"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Note : For optimal performance and to avoid overloading the system, it is recommended to fetch data for one month at a time for up to 400 devices, using a 5-minute granularity per tag. If the input exceeds these thresholds, the API should be called in batches."]}, {"cell_type": "markdown", "metadata": {"id": "TRiCLXsaw3Z-"}, "source": ["## Output:\n", "- Dataframe based on the parameters\n", "- Column names would be of the format 'Device.Parameter'\n", "- There would be a 'time' column with Timestamp in UTC format"]}, {"cell_type": "markdown", "metadata": {"id": "3q3cVi3kw3aA"}, "source": ["## Fetch Turbine Active Power\n", "- **````Category````**\n", "    - Static Category\n", "    ```python\n", "       category = ['Turbine']\n", "    ```\n", "    - Dynamic Category\n", "    ```python\n", "       category = [cat for cat in categories  if cat =='Turbine']\n", "    ```\n", "- **````Parameter````**\n", "    - Parameter\n", "    ```python\n", "    params = ['WTUR.Active power']\n", "    ```\n", "    \n", "- **````<PERSON><PERSON><PERSON><PERSON>````**\n", "    - Static Device List\n", "    ```python\n", "    deviceList = ['BBV50', 'BBV53', 'BBV54', 'BBV55']\n", "    ```\n", "    - Dynamic Device List\n", "    ```python\n", "    deviceList = deviceDict[\"Turbine\"]\n", "    \n", "    ```\n", "- **````Start Date````**\n", "    - Only Date\n", "    ```python \n", "    startDate ='2023-08-10'\n", "    ```\n", "   - Date Time\n", "    ```python \n", "    startDate ='2023-08-10 13:10:00'\n", "    ```\n", "    - Date Time and Timezone\n", "    ```python \n", "    startDate ='2023-08-10 13:10:00+05:30'\n", "    ```\n", "- **````End Date````**\n", "    - Only Date\n", "    ```python \n", "    endDate ='2023-08-10'\n", "    ```\n", "   - Date Time\n", "   ```python \n", "    endDate ='2023-08-10 15:00:00'\n", "   ```\n", "   - Date Time and Timezone\n", "   ```python \n", "    endDate ='2023-08-10 15:00:00+05:30'\n", "   ```\n", "- **````Condition````**\n", "   - Mean\n", "   ```python \n", "   conditon ={\"Active Power\":\"mean\"}\n", "   ```\n", "   - Median\n", "   ```python \n", "   conditon ={\"Active Power\":\"median\"}\n", "   ```\n", "   - Mode\n", "   ```python \n", "   conditon ={\"Active Power\":\"mode\"}\n", "   ```\n", "   - Sum\n", "   ```python \n", "   conditon ={\"Active Power\":\"sum\"}\n", "   ```\n", "   - Last\n", "   ```python \n", "   conditon ={\"Active Power\":\"last\"}\n", "   ```\n", "   - Count\n", "   ```python \n", "   conditon ={\"Active Power\":\"count\"}\n", "   ```\n", "   - Max\n", "   ```python \n", "   conditon ={\"Active Power\":\"max\"}\n", "   ```\n", "   - Min\n", "   ```python \n", "   conditon ={\"Active Power\":\"min\"}\n", "   ```\n", "   - De<PERSON>ult\n", "   ```python \n", "   conditon ={\"Active Power\":\"first\"}\n", "   ```\n", "    \n", "- **````Granularity````**\n", "   - 1 Second Granularity\n", "   ```python \n", "   granularity ='1s'\n", "   ```\n", "   - 10 Second Granularity\n", "   ```python \n", "   granularity ='10s'\n", "   ```\n", "   - 1 Minute Granularity\n", "   ```python \n", "   granularity ='1m'\n", "   ```\n", "   - 10 Minute Granularity\n", "   ```python \n", "   granularity ='1s'\n", "   ```\n", "   - 1 Hour Granularity\n", "   ```python \n", "   granularity ='1h'\n", "   ```\n", "   - 1 Day Granularity\n", "   ```python \n", "   granularity ='1d'\n", "   ```\n", "   - Default <PERSON>\n", "   ```python \n", "   granularity ='5m'\n", "   ```\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Turbine Active Power at 10 min interval. Active power is present in General Category. \n", "#### So parameter should be passed as WTUR.Active power"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"id": "28UnCAYMw3aB"}, "outputs": [], "source": ["params = ['WTUR.Active power']\n", "category = 'Turbine'\n", "# device = ['BSW_T0001']\n", "genDf = m.fetchDataV2(plantName,category,params,None,'2024-09-15','2024-09-15',granularity = '10m')"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>time</th>\n", "      <th>GGM05.Active power_x</th>\n", "      <th>GGM05.Active power_y</th>\n", "      <th>GGM100.Active power</th>\n", "      <th>GGM101_.Active power</th>\n", "      <th>GGM102.Active power</th>\n", "      <th>GGM105.Active power</th>\n", "      <th>GGM111.Active power</th>\n", "      <th>GGM142.Active power</th>\n", "      <th>GGM99.Active power</th>\n", "      <th>PTT1.Active power</th>\n", "      <th>GGM05.Active power</th>\n", "      <th>GGM05.Active power</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-09-15T00:00:00Z+05:30</td>\n", "      <td>983.603577</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-09-15T00:10:00Z+05:30</td>\n", "      <td>764.496277</td>\n", "      <td>447.746765</td>\n", "      <td>400.739014</td>\n", "      <td>769.067993</td>\n", "      <td>199.990875</td>\n", "      <td>360.498779</td>\n", "      <td>200.000717</td>\n", "      <td>-0.045662</td>\n", "      <td>935.335144</td>\n", "      <td>492.216736</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-09-15T00:20:00Z+05:30</td>\n", "      <td>1059.477173</td>\n", "      <td>435.158142</td>\n", "      <td>494.012634</td>\n", "      <td>613.855835</td>\n", "      <td>199.995560</td>\n", "      <td>826.810242</td>\n", "      <td>200.020798</td>\n", "      <td>435.339874</td>\n", "      <td>889.864136</td>\n", "      <td>356.661346</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-09-15T00:30:00Z+05:30</td>\n", "      <td>891.285706</td>\n", "      <td>501.326080</td>\n", "      <td>-0.477051</td>\n", "      <td>564.078613</td>\n", "      <td>199.987274</td>\n", "      <td>559.334778</td>\n", "      <td>199.958374</td>\n", "      <td>205.163300</td>\n", "      <td>761.969543</td>\n", "      <td>624.727661</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-09-15T00:40:00Z+05:30</td>\n", "      <td>733.655884</td>\n", "      <td>485.915924</td>\n", "      <td>776.971375</td>\n", "      <td>650.751709</td>\n", "      <td>200.007538</td>\n", "      <td>638.784363</td>\n", "      <td>200.043930</td>\n", "      <td>-0.036023</td>\n", "      <td>728.519653</td>\n", "      <td>630.348389</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>139</th>\n", "      <td>2024-09-15T23:10:00Z+05:30</td>\n", "      <td>348.321625</td>\n", "      <td>397.101562</td>\n", "      <td>-0.553163</td>\n", "      <td>454.434265</td>\n", "      <td>424.415222</td>\n", "      <td>236.167602</td>\n", "      <td>416.430023</td>\n", "      <td>423.942871</td>\n", "      <td>577.082947</td>\n", "      <td>413.044006</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>140</th>\n", "      <td>2024-09-15T23:20:00Z+05:30</td>\n", "      <td>299.778351</td>\n", "      <td>361.636749</td>\n", "      <td>-0.558653</td>\n", "      <td>372.456207</td>\n", "      <td>342.650238</td>\n", "      <td>413.875397</td>\n", "      <td>279.019501</td>\n", "      <td>345.557068</td>\n", "      <td>458.167786</td>\n", "      <td>289.990814</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>141</th>\n", "      <td>2024-09-15T23:30:00Z+05:30</td>\n", "      <td>305.769592</td>\n", "      <td>255.971069</td>\n", "      <td>-0.564954</td>\n", "      <td>352.833527</td>\n", "      <td>348.602356</td>\n", "      <td>350.436340</td>\n", "      <td>428.212891</td>\n", "      <td>254.094391</td>\n", "      <td>455.536987</td>\n", "      <td>253.427048</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>142</th>\n", "      <td>2024-09-15T23:40:00Z+05:30</td>\n", "      <td>384.080689</td>\n", "      <td>393.609680</td>\n", "      <td>-0.563784</td>\n", "      <td>278.390259</td>\n", "      <td>392.488983</td>\n", "      <td>259.867218</td>\n", "      <td>226.144959</td>\n", "      <td>249.118179</td>\n", "      <td>385.965637</td>\n", "      <td>242.601730</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>143</th>\n", "      <td>2024-09-15T23:50:00Z+05:30</td>\n", "      <td>166.423615</td>\n", "      <td>349.563568</td>\n", "      <td>144.789535</td>\n", "      <td>323.070618</td>\n", "      <td>465.763397</td>\n", "      <td>242.379990</td>\n", "      <td>0.000000</td>\n", "      <td>188.199493</td>\n", "      <td>241.847931</td>\n", "      <td>247.199982</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>144 rows × 13 columns</p>\n", "</div>"], "text/plain": ["                           time  GGM05.Active power_x  GGM05.Active power_y  \\\n", "0    2024-09-15T00:00:00Z+05:30            983.603577                   NaN   \n", "1    2024-09-15T00:10:00Z+05:30            764.496277            447.746765   \n", "2    2024-09-15T00:20:00Z+05:30           1059.477173            435.158142   \n", "3    2024-09-15T00:30:00Z+05:30            891.285706            501.326080   \n", "4    2024-09-15T00:40:00Z+05:30            733.655884            485.915924   \n", "..                          ...                   ...                   ...   \n", "139  2024-09-15T23:10:00Z+05:30            348.321625            397.101562   \n", "140  2024-09-15T23:20:00Z+05:30            299.778351            361.636749   \n", "141  2024-09-15T23:30:00Z+05:30            305.769592            255.971069   \n", "142  2024-09-15T23:40:00Z+05:30            384.080689            393.609680   \n", "143  2024-09-15T23:50:00Z+05:30            166.423615            349.563568   \n", "\n", "     GGM100.Active power  GGM101_.Active power  GGM102.Active power  \\\n", "0                    NaN                   NaN                  NaN   \n", "1             400.739014            769.067993           199.990875   \n", "2             494.012634            613.855835           199.995560   \n", "3              -0.477051            564.078613           199.987274   \n", "4             776.971375            650.751709           200.007538   \n", "..                   ...                   ...                  ...   \n", "139            -0.553163            454.434265           424.415222   \n", "140            -0.558653            372.456207           342.650238   \n", "141            -0.564954            352.833527           348.602356   \n", "142            -0.563784            278.390259           392.488983   \n", "143           144.789535            323.070618           465.763397   \n", "\n", "     GGM105.Active power  GGM111.Active power  GGM142.Active power  \\\n", "0                    NaN                  NaN                  NaN   \n", "1             360.498779           200.000717            -0.045662   \n", "2             826.810242           200.020798           435.339874   \n", "3             559.334778           199.958374           205.163300   \n", "4             638.784363           200.043930            -0.036023   \n", "..                   ...                  ...                  ...   \n", "139           236.167602           416.430023           423.942871   \n", "140           413.875397           279.019501           345.557068   \n", "141           350.436340           428.212891           254.094391   \n", "142           259.867218           226.144959           249.118179   \n", "143           242.379990             0.000000           188.199493   \n", "\n", "     GGM99.Active power  PTT1.Active power  GGM05.Active power  \\\n", "0                   NaN                NaN                 NaN   \n", "1            935.335144         492.216736                 NaN   \n", "2            889.864136         356.661346                 NaN   \n", "3            761.969543         624.727661                 NaN   \n", "4            728.519653         630.348389                 NaN   \n", "..                  ...                ...                 ...   \n", "139          577.082947         413.044006                 NaN   \n", "140          458.167786         289.990814                 NaN   \n", "141          455.536987         253.427048                 NaN   \n", "142          385.965637         242.601730                 NaN   \n", "143          241.847931         247.199982                 NaN   \n", "\n", "     GGM05.Active power  \n", "0                   NaN  \n", "1                   NaN  \n", "2                   NaN  \n", "3                   NaN  \n", "4                   NaN  \n", "..                  ...  \n", "139                 NaN  \n", "140                 NaN  \n", "141                 NaN  \n", "142                 NaN  \n", "143                 NaN  \n", "\n", "[144 rows x 13 columns]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["genDf"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Fetch generator speed, rotor speed, Nacelle Direction from Generator, Rotor,Nacelle components respectively.\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["params = ['WGEN.Generator Speed RPM', 'WROT.Rotor Speed RPM ','WNAC.Nacelle Direction']\n", "category = 'Turbine'\n", "device = ['GGM05']\n", "condition = {'Generator Speed RPM':'min','Rotor Speed RPM':'max','Nacelle Direction':'mode'}\n", "turbKpisDf = m.fetchDataV2(plantName,category,params,device,'2023-08-01','2023-08-31',granularity = '1d',condition=condition)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>time</th>\n", "      <th>GGM05_WNAC.Nacelle Direction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-08-01T00:00:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-08-02T00:00:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-08-03T00:00:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-08-04T00:00:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-08-05T00:00:00Z+05:30</td>\n", "      <td>102.768410</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2023-08-06T00:00:00Z+05:30</td>\n", "      <td>143.707932</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2023-08-07T00:00:00Z+05:30</td>\n", "      <td>186.257233</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2023-08-08T00:00:00Z+05:30</td>\n", "      <td>156.256867</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2023-08-09T00:00:00Z+05:30</td>\n", "      <td>358.137390</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2023-08-10T00:00:00Z+05:30</td>\n", "      <td>8.308228</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2023-08-11T00:00:00Z+05:30</td>\n", "      <td>85.170639</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2023-08-12T00:00:00Z+05:30</td>\n", "      <td>88.756042</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2023-08-13T00:00:00Z+05:30</td>\n", "      <td>102.987930</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2023-08-14T00:00:00Z+05:30</td>\n", "      <td>96.548821</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2023-08-15T00:00:00Z+05:30</td>\n", "      <td>65.121613</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2023-08-16T00:00:00Z+05:30</td>\n", "      <td>65.011856</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2023-08-17T00:00:00Z+05:30</td>\n", "      <td>86.158455</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2023-08-18T00:00:00Z+05:30</td>\n", "      <td>111.914864</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2023-08-19T00:00:00Z+05:30</td>\n", "      <td>69.768005</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2023-08-20T00:00:00Z+05:30</td>\n", "      <td>99.073242</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>2023-08-21T00:00:00Z+05:30</td>\n", "      <td>107.780670</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>2023-08-22T00:00:00Z+05:30</td>\n", "      <td>129.366302</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>2023-08-23T00:00:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>2023-08-24T00:00:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>2023-08-25T00:00:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>2023-08-26T00:00:00Z+05:30</td>\n", "      <td>102.768410</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>2023-08-27T00:00:00Z+05:30</td>\n", "      <td>143.707932</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>2023-08-28T00:00:00Z+05:30</td>\n", "      <td>186.257233</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>2023-08-29T00:00:00Z+05:30</td>\n", "      <td>156.256867</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>2023-08-30T00:00:00Z+05:30</td>\n", "      <td>358.137390</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>2023-08-31T00:00:00Z+05:30</td>\n", "      <td>8.308228</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                          time  GGM05_WNAC.Nacelle Direction\n", "0   2023-08-01T00:00:00Z+05:30                           NaN\n", "1   2023-08-02T00:00:00Z+05:30                           NaN\n", "2   2023-08-03T00:00:00Z+05:30                           NaN\n", "3   2023-08-04T00:00:00Z+05:30                           NaN\n", "4   2023-08-05T00:00:00Z+05:30                    102.768410\n", "5   2023-08-06T00:00:00Z+05:30                    143.707932\n", "6   2023-08-07T00:00:00Z+05:30                    186.257233\n", "7   2023-08-08T00:00:00Z+05:30                    156.256867\n", "8   2023-08-09T00:00:00Z+05:30                    358.137390\n", "9   2023-08-10T00:00:00Z+05:30                      8.308228\n", "10  2023-08-11T00:00:00Z+05:30                     85.170639\n", "11  2023-08-12T00:00:00Z+05:30                     88.756042\n", "12  2023-08-13T00:00:00Z+05:30                    102.987930\n", "13  2023-08-14T00:00:00Z+05:30                     96.548821\n", "14  2023-08-15T00:00:00Z+05:30                     65.121613\n", "15  2023-08-16T00:00:00Z+05:30                     65.011856\n", "16  2023-08-17T00:00:00Z+05:30                     86.158455\n", "17  2023-08-18T00:00:00Z+05:30                    111.914864\n", "18  2023-08-19T00:00:00Z+05:30                     69.768005\n", "19  2023-08-20T00:00:00Z+05:30                     99.073242\n", "20  2023-08-21T00:00:00Z+05:30                    107.780670\n", "21  2023-08-22T00:00:00Z+05:30                    129.366302\n", "22  2023-08-23T00:00:00Z+05:30                           NaN\n", "23  2023-08-24T00:00:00Z+05:30                           NaN\n", "24  2023-08-25T00:00:00Z+05:30                           NaN\n", "25  2023-08-26T00:00:00Z+05:30                    102.768410\n", "26  2023-08-27T00:00:00Z+05:30                    143.707932\n", "27  2023-08-28T00:00:00Z+05:30                    186.257233\n", "28  2023-08-29T00:00:00Z+05:30                    156.256867\n", "29  2023-08-30T00:00:00Z+05:30                    358.137390\n", "30  2023-08-31T00:00:00Z+05:30                      8.308228"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["turbKpisDf"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Fetch Average Wind speed for a day"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Alarms\n", "####  Same function fetchDataV2 is being used for fetching alarm data but need to update category and parameters.\n", "- **````Category````**\n", "    \n", "    ```python\n", "       category = ['Alarm']\n", "    ```\n", "\n", "- **````Parameter````**\n", "    \n", "    ```python\n", "    params = ['Turbine Alarm']\n", "    ```\n", "\n", "- **````Start Date````**\n", "    \n", "   ```python \n", "    startDate ='2023-08-10'\n", "   ```\n", "- **````End Date````**\n", "    \n", "    ```python \n", "    endDate ='2023-08-10'\n", "    ```\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Output\n", "#### Returns a DataFrame consisting Alarm time,cotrollerid, controllername, type, categories, alarm serverity, message, parameterid , parametername, state, raise time, resolve time and resolved values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"colab": {"name": "Energy Storage API sample.ipynb", "provenance": []}, "kernelspec": {"display_name": "jupyterlab", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 4}