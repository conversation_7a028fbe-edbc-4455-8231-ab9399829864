import os
import boto3
from config.creds_storage_s3 import USE_S3, AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_STORAGE_BUCKET_NAME, STATIC_FOLDER, AWS_REGION
from helper.logger_setup import setup_logger


logging = setup_logger('storage', 'storage.log')


def upload_file_s3(file_path, destination_path):
    """Uploads a file to S3 or stores it locally based on configuration."""
    
    try:
        if USE_S3:
            logging.info(f"Uploading {file_path} to S3 bucket: {AWS_STORAGE_BUCKET_NAME}, destination: {destination_path}")

            s3_client = boto3.client(
                "s3",
                aws_access_key_id=AWS_ACCESS_KEY_ID,
                aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
                region_name=AWS_REGION,
            )
            bucket = AWS_STORAGE_BUCKET_NAME

            # Upload file to S3
            s3_client.upload_file(file_path, bucket, destination_path)
            
            s3_url = f"https://{bucket}.s3.{AWS_REGION}.amazonaws.com/{destination_path}"
            logging.info(f"File successfully uploaded to S3: {s3_url}")
            return s3_url
        
        else:
            logging.info(f"Saving file locally: {file_path} -> {destination_path}")

            # Save locally
            local_path = os.path.join(STATIC_FOLDER, destination_path)
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            os.rename(file_path, local_path)
            
            logging.info(f"File successfully saved locally: {local_path}")
            return local_path

    except Exception as e:
        logging.error(f"Error in upload_file_s3: {e}")
        raise
