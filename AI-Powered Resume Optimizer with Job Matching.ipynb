{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: openai in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (1.67.0)\n", "Requirement already satisfied: beautifulsoup4 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (4.13.3)\n", "Requirement already satisfied: nltk in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (3.9.1)\n", "Collecting pdfplumber\n", "  Downloading pdfplumber-0.11.5-py3-none-any.whl.metadata (42 kB)\n", "Requirement already satisfied: transformers in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (4.49.0)\n", "Requirement already satisfied: sentence-transformers in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (3.4.1)\n", "Collecting streamlit\n", "  Downloading streamlit-1.43.2-py2.py3-none-any.whl.metadata (8.9 kB)\n", "Requirement already satisfied: stable-baselines3 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (2.5.0)\n", "Collecting gym\n", "  Downloading gym-0.26.2.tar.gz (721 kB)\n", "     ---------------------------------------- 0.0/721.7 kB ? eta -:--:--\n", "     -------------------------------------- 721.7/721.7 kB 7.3 MB/s eta 0:00:00\n", "  Installing build dependencies: started\n", "  Installing build dependencies: finished with status 'done'\n", "  Getting requirements to build wheel: started\n", "  Getting requirements to build wheel: finished with status 'done'\n", "  Preparing metadata (pyproject.toml): started\n", "  Preparing metadata (pyproject.toml): finished with status 'done'\n", "Requirement already satisfied: anyio<5,>=3.5.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from openai) (4.8.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from openai) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from openai) (0.28.1)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from openai) (0.8.2)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from openai) (2.10.6)\n", "Requirement already satisfied: sniffio in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from openai) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from openai) (4.67.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.11 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from openai) (4.12.2)\n", "Requirement already satisfied: soupsieve>1.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from beautifulsoup4) (2.6)\n", "Requirement already satisfied: click in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from nltk) (8.1.8)\n", "Requirement already satisfied: joblib in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from nltk) (1.4.2)\n", "Requirement already satisfied: regex>=2021.8.3 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from nltk) (2024.11.6)\n", "Collecting pdfminer.six==20231228 (from pdfplumber)\n", "  Downloading pdfminer.six-20231228-py3-none-any.whl.metadata (4.2 kB)\n", "Requirement already satisfied: Pillow>=9.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pdfplumber) (11.1.0)\n", "Collecting pypdfium2>=4.18.0 (from pdfplumber)\n", "  Downloading pypdfium2-4.30.1-py3-none-win_amd64.whl.metadata (48 kB)\n", "Requirement already satisfied: charset-normalizer>=2.0.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pdfminer.six==20231228->pdfplumber) (3.4.1)\n", "Collecting cryptography>=36.0.0 (from pdfminer.six==20231228->pdfplumber)\n", "  Downloading cryptography-44.0.2-cp39-abi3-win_amd64.whl.metadata (5.7 kB)\n", "Requirement already satisfied: filelock in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from transformers) (3.17.0)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.26.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from transformers) (0.28.1)\n", "Requirement already satisfied: numpy>=1.17 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from transformers) (2.1.3)\n", "Requirement already satisfied: packaging>=20.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from transformers) (24.2)\n", "Requirement already satisfied: pyyaml>=5.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from transformers) (6.0.1)\n", "Requirement already satisfied: requests in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from transformers) (2.32.3)\n", "Requirement already satisfied: tokenizers<0.22,>=0.21 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from transformers) (0.21.0)\n", "Requirement already satisfied: safetensors>=0.4.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from transformers) (0.5.2)\n", "Requirement already satisfied: torch>=1.11.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from sentence-transformers) (2.6.0)\n", "Requirement already satisfied: scikit-learn in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from sentence-transformers) (1.6.1)\n", "Requirement already satisfied: scipy in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from sentence-transformers) (1.15.2)\n", "Collecting altair<6,>=4.0 (from streamlit)\n", "  Downloading altair-5.5.0-py3-none-any.whl.metadata (11 kB)\n", "Requirement already satisfied: blinker<2,>=1.0.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from streamlit) (1.9.0)\n", "Requirement already satisfied: cachetools<6,>=4.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from streamlit) (5.5.2)\n", "Requirement already satisfied: pandas<3,>=1.4.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from streamlit) (2.2.3)\n", "Requirement already satisfied: protobuf<6,>=3.20 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from streamlit) (5.29.4)\n", "Requirement already satisfied: pyarrow>=7.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from streamlit) (19.0.1)\n", "Requirement already satisfied: tenacity<10,>=8.1.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from streamlit) (9.0.0)\n", "Collecting toml<2,>=0.10.1 (from streamlit)\n", "  Downloading toml-0.10.2-py2.py3-none-any.whl.metadata (7.1 kB)\n", "Collecting watchdog<7,>=2.1.5 (from streamlit)\n", "  Downloading watchdog-6.0.0-py3-none-win_amd64.whl.metadata (44 kB)\n", "Requirement already satisfied: gitpython!=3.1.19,<4,>=3.0.7 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from streamlit) (3.1.44)\n", "Collecting pydeck<1,>=0.8.0b4 (from streamlit)\n", "  Downloading pydeck-0.9.1-py2.py3-none-any.whl.metadata (4.1 kB)\n", "Requirement already satisfied: tornado<7,>=6.0.3 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from streamlit) (6.4.2)\n", "Requirement already satisfied: gymnasium<1.1.0,>=0.29.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from stable-baselines3) (1.0.0)\n", "Requirement already satisfied: cloudpickle in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from stable-baselines3) (3.1.1)\n", "Requirement already satisfied: matplotlib in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from stable-baselines3) (3.10.0)\n", "Collecting gym_notices>=0.0.4 (from gym)\n", "  Downloading gym_notices-0.0.8-py3-none-any.whl.metadata (1.0 kB)\n", "Requirement already satisfied: jinja2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from altair<6,>=4.0->streamlit) (3.1.5)\n", "Requirement already satisfied: jsonschema>=3.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from altair<6,>=4.0->streamlit) (4.23.0)\n", "Collecting narwhals>=1.14.2 (from altair<6,>=4.0->streamlit)\n", "  Downloading narwhals-1.31.0-py3-none-any.whl.metadata (11 kB)\n", "Requirement already satisfied: idna>=2.8 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from anyio<5,>=3.5.0->openai) (3.10)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from click->nltk) (0.4.0)\n", "Requirement already satisfied: gitdb<5,>=4.0.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gitpython!=3.1.19,<4,>=3.0.7->streamlit) (4.0.12)\n", "Requirement already satisfied: farama-notifications>=0.0.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gymnasium<1.1.0,>=0.29.1->stable-baselines3) (0.0.4)\n", "Requirement already satisfied: certifi in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from httpx<1,>=0.23.0->openai) (2025.1.31)\n", "Requirement already satisfied: httpcore==1.* in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from httpx<1,>=0.23.0->openai) (1.0.7)\n", "Requirement already satisfied: h11<0.15,>=0.13 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai) (0.14.0)\n", "Requirement already satisfied: fsspec>=2023.5.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from huggingface-hub<1.0,>=0.26.0->transformers) (2025.2.0)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from pandas<3,>=1.4.0->streamlit) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pandas<3,>=1.4.0->streamlit) (2025.1)\n", "Requirement already satisfied: tzdata>=2022.7 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pandas<3,>=1.4.0->streamlit) (2025.1)\n", "Requirement already satisfied: annotated-types>=0.6.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pydantic<3,>=1.9.0->openai) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pydantic<3,>=1.9.0->openai) (2.27.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests->transformers) (1.26.20)\n", "Requirement already satisfied: networkx in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from torch>=1.11.0->sentence-transformers) (3.4.2)\n", "Requirement already satisfied: setuptools in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from torch>=1.11.0->sentence-transformers) (75.8.0)\n", "Requirement already satisfied: sympy==1.13.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from torch>=1.11.0->sentence-transformers) (1.13.1)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from sympy==1.13.1->torch>=1.11.0->sentence-transformers) (1.3.0)\n", "Requirement already satisfied: contourpy>=1.0.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from matplotlib->stable-baselines3) (1.3.1)\n", "Requirement already satisfied: cycler>=0.10 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from matplotlib->stable-baselines3) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from matplotlib->stable-baselines3) (4.56.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from matplotlib->stable-baselines3) (1.4.8)\n", "Requirement already satisfied: pyparsing>=2.3.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from matplotlib->stable-baselines3) (3.2.1)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from scikit-learn->sentence-transformers) (3.5.0)\n", "Requirement already satisfied: cffi>=1.12 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from cryptography>=36.0.0->pdfminer.six==20231228->pdfplumber) (1.17.1)\n", "Requirement already satisfied: smmap<6,>=3.0.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gitdb<5,>=4.0.1->gitpython!=3.1.19,<4,>=3.0.7->streamlit) (5.0.2)\n", "Requirement already satisfied: MarkupSafe>=2.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from jinja2->altair<6,>=4.0->streamlit) (3.0.2)\n", "Requirement already satisfied: attrs>=22.2.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from jsonschema>=3.0->altair<6,>=4.0->streamlit) (25.1.0)\n", "Requirement already satisfied: jsonschema-specifications>=2023.03.6 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from jsonschema>=3.0->altair<6,>=4.0->streamlit) (2024.10.1)\n", "Requirement already satisfied: referencing>=0.28.4 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from jsonschema>=3.0->altair<6,>=4.0->streamlit) (0.36.2)\n", "Requirement already satisfied: rpds-py>=0.7.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from jsonschema>=3.0->altair<6,>=4.0->streamlit) (0.22.3)\n", "Requirement already satisfied: six>=1.5 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from python-dateutil>=2.8.2->pandas<3,>=1.4.0->streamlit) (1.17.0)\n", "Requirement already satisfied: pycparser in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from cffi>=1.12->cryptography>=36.0.0->pdfminer.six==20231228->pdfplumber) (2.22)\n", "Downloading pdfplumber-0.11.5-py3-none-any.whl (59 kB)\n", "Downloading pdfminer.six-20231228-py3-none-any.whl (5.6 MB)\n", "   ---------------------------------------- 0.0/5.6 MB ? eta -:--:--\n", "   -------------------- ------------------- 2.9/5.6 MB 13.9 MB/s eta 0:00:01\n", "   ------------------------------- -------- 4.5/5.6 MB 11.2 MB/s eta 0:00:01\n", "   ---------------------------------------- 5.6/5.6 MB 11.0 MB/s eta 0:00:00\n", "Downloading streamlit-1.43.2-py2.py3-none-any.whl (9.7 MB)\n", "   ---------------------------------------- 0.0/9.7 MB ? eta -:--:--\n", "   ---------- ----------------------------- 2.6/9.7 MB 16.7 MB/s eta 0:00:01\n", "   ------------------------- -------------- 6.3/9.7 MB 14.9 MB/s eta 0:00:01\n", "   ------------------------------------ --- 8.9/9.7 MB 15.0 MB/s eta 0:00:01\n", "   ---------------------------------------- 9.7/9.7 MB 12.9 MB/s eta 0:00:00\n", "Downloading altair-5.5.0-py3-none-any.whl (731 kB)\n", "   ---------------------------------------- 0.0/731.2 kB ? eta -:--:--\n", "   --------------------------------------- 731.2/731.2 kB 15.1 MB/s eta 0:00:00\n", "Downloading gym_notices-0.0.8-py3-none-any.whl (3.0 kB)\n", "Downloading pydeck-0.9.1-py2.py3-none-any.whl (6.9 MB)\n", "   ---------------------------------------- 0.0/6.9 MB ? eta -:--:--\n", "   --------------------- ------------------ 3.7/6.9 MB 18.1 MB/s eta 0:00:01\n", "   ---------------------------------------- 6.9/6.9 MB 17.0 MB/s eta 0:00:00\n", "Downloading pypdfium2-4.30.1-py3-none-win_amd64.whl (3.0 MB)\n", "   ---------------------------------------- 0.0/3.0 MB ? eta -:--:--\n", "   ---------------------------------------- 3.0/3.0 MB 21.6 MB/s eta 0:00:00\n", "Downloading toml-0.10.2-py2.py3-none-any.whl (16 kB)\n", "Downloading watchdog-6.0.0-py3-none-win_amd64.whl (79 kB)\n", "Downloading cryptography-44.0.2-cp39-abi3-win_amd64.whl (3.2 MB)\n", "   ---------------------------------------- 0.0/3.2 MB ? eta -:--:--\n", "   ---------------------------------------- 3.2/3.2 MB 23.6 MB/s eta 0:00:00\n", "Downloading narwhals-1.31.0-py3-none-any.whl (313 kB)\n", "Building wheels for collected packages: gym\n", "  Building wheel for gym (pyproject.toml): started\n", "  Building wheel for gym (pyproject.toml): finished with status 'done'\n", "  Created wheel for gym: filename=gym-0.26.2-py3-none-any.whl size=827736 sha256=fa89dfb8528676ca36644cef96469c31350ced85b48bd2d0bdaef7c49264c228\n", "  Stored in directory: c:\\users\\<USER>\\appdata\\local\\pip\\cache\\wheels\\95\\51\\6c\\9bb05ebbe7c5cb8171dfaa3611f32622ca4658d53f31c79077\n", "Successfully built gym\n", "Installing collected packages: gym_notices, watchdog, toml, pypdfium2, narwhals, gym, pydeck, cryptography, pdfminer.six, pdfplumber, altair, streamlit\n", "Successfully installed altair-5.5.0 cryptography-44.0.2 gym-0.26.2 gym_notices-0.0.8 narwhals-1.31.0 pdfminer.six-20231228 pdfplumber-0.11.5 pydeck-0.9.1 pypdfium2-4.30.1 streamlit-1.43.2 toml-0.10.2 watchdog-6.0.0\n"]}], "source": ["!pip install openai beautifulsoup4 nltk pdfplumber transformers sentence-transformers streamlit stable-baselines3 gym"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting webdriver_manager\n", "  Downloading webdriver_manager-4.0.2-py2.py3-none-any.whl.metadata (12 kB)\n", "Requirement already satisfied: requests in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from webdriver_manager) (2.32.3)\n", "Requirement already satisfied: python-dotenv in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from webdriver_manager) (1.0.1)\n", "Requirement already satisfied: packaging in c:\\users\\<USER>\\appdata\\roaming\\python\\python312\\site-packages (from webdriver_manager) (24.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests->webdriver_manager) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests->webdriver_manager) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests->webdriver_manager) (1.26.20)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests->webdriver_manager) (2025.1.31)\n", "Downloading webdriver_manager-4.0.2-py2.py3-none-any.whl (27 kB)\n", "Installing collected packages: webdriver_manager\n", "Successfully installed webdriver_manager-4.0.2\n"]}], "source": ["!pip install webdriver_manager"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:From c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\tf_keras\\src\\losses.py:2976: The name tf.losses.sparse_softmax_cross_entropy is deprecated. Please use tf.compat.v1.losses.sparse_softmax_cross_entropy instead.\n", "\n"]}], "source": ["# Import core libraries\n", "import os\n", "import requests\n", "import json\n", "import openai\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Import web scraping tools\n", "from bs4 import BeautifulSoup\n", "\n", "# Import text processing tools\n", "import nltk\n", "from nltk.corpus import stopwords\n", "from nltk.tokenize import word_tokenize\n", "\n", "# Import AI models\n", "from sentence_transformers import SentenceTransformer, util\n", "from stable_baselines3 import PPO\n", "import gym\n", "\n", "# Import PDF processing\n", "import pdfplumber\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 2. Data Collection (Job Analysis Agent)\n", "🔹 2.1 Scrape Job Listings from Indeed"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"ename": "NoSuchDriverException", "evalue": "Message: Unable to obtain driver for chrome; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location\n", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\common\\driver_finder.py:64\u001b[0m, in \u001b[0;36mDriverFinder._binary_paths\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m     63\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m Path(path)\u001b[38;5;241m.\u001b[39mis_file():\n\u001b[1;32m---> 64\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mThe path is not a valid file: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpath\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     65\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_paths[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdriver_path\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m path\n", "\u001b[1;31mValueError\u001b[0m: The path is not a valid file: /path/to/chromedriver", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31mNoSuchDriverException\u001b[0m                     <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[18], line 39\u001b[0m\n\u001b[0;32m     35\u001b[0m     driver\u001b[38;5;241m.\u001b[39mquit()\n\u001b[0;32m     36\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m pd\u001b[38;5;241m.\u001b[39mDataFrame(jobs)\n\u001b[1;32m---> 39\u001b[0m jobs_df \u001b[38;5;241m=\u001b[39m \u001b[43mscrape_glassdoor_jobs\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mData Scientist\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlocation\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mIndia\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnum_pages\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m     40\u001b[0m \u001b[38;5;28mprint\u001b[39m(jobs_df\u001b[38;5;241m.\u001b[39mhead())\n", "Cell \u001b[1;32mIn[18], line 6\u001b[0m, in \u001b[0;36mscrape_glassdoor_jobs\u001b[1;34m(keyword, location, num_pages)\u001b[0m\n\u001b[0;32m      4\u001b[0m options \u001b[38;5;241m=\u001b[39m webdriver\u001b[38;5;241m.\u001b[39mChromeOptions()\n\u001b[0;32m      5\u001b[0m options\u001b[38;5;241m.\u001b[39madd_argument(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m--headless\u001b[39m\u001b[38;5;124m'\u001b[39m)  \u001b[38;5;66;03m# Run browser in headless mode\u001b[39;00m\n\u001b[1;32m----> 6\u001b[0m driver \u001b[38;5;241m=\u001b[39m \u001b[43mwebdriver\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mChrome\u001b[49m\u001b[43m(\u001b[49m\u001b[43mservice\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mservice\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m      8\u001b[0m jobs \u001b[38;5;241m=\u001b[39m []\n\u001b[0;32m      9\u001b[0m base_url \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhttps://www.glassdoor.com/Job/jobs.htm\u001b[39m\u001b[38;5;124m\"\u001b[39m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\chrome\\webdriver.py:45\u001b[0m, in \u001b[0;36mWebDriver.__init__\u001b[1;34m(self, options, service, keep_alive)\u001b[0m\n\u001b[0;32m     42\u001b[0m service \u001b[38;5;241m=\u001b[39m service \u001b[38;5;28;01mif\u001b[39;00m service \u001b[38;5;28;01melse\u001b[39;00m Service()\n\u001b[0;32m     43\u001b[0m options \u001b[38;5;241m=\u001b[39m options \u001b[38;5;28;01mif\u001b[39;00m options \u001b[38;5;28;01melse\u001b[39;00m Options()\n\u001b[1;32m---> 45\u001b[0m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[0;32m     46\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbrowser_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mDesiredCapabilities\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mCHROME\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mbrowserName\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m     47\u001b[0m \u001b[43m    \u001b[49m\u001b[43mvendor_prefix\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mgoog\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m     48\u001b[0m \u001b[43m    \u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m     49\u001b[0m \u001b[43m    \u001b[49m\u001b[43mservice\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mservice\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m     50\u001b[0m \u001b[43m    \u001b[49m\u001b[43mkeep_alive\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mkeep_alive\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m     51\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\chromium\\webdriver.py:50\u001b[0m, in \u001b[0;36mChromiumDriver.__init__\u001b[1;34m(self, browser_name, vendor_prefix, options, service, keep_alive)\u001b[0m\n\u001b[0;32m     47\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mservice \u001b[38;5;241m=\u001b[39m service\n\u001b[0;32m     49\u001b[0m finder \u001b[38;5;241m=\u001b[39m DriverFinder(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mservice, options)\n\u001b[1;32m---> 50\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[43mfinder\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_browser_path\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m:\n\u001b[0;32m     51\u001b[0m     options\u001b[38;5;241m.\u001b[39mbinary_location \u001b[38;5;241m=\u001b[39m finder\u001b[38;5;241m.\u001b[39mget_browser_path()\n\u001b[0;32m     52\u001b[0m     options\u001b[38;5;241m.\u001b[39mbrowser_version \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\common\\driver_finder.py:47\u001b[0m, in \u001b[0;36mDriverFinder.get_browser_path\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m     46\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mget_browser_path\u001b[39m(\u001b[38;5;28mself\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28mstr\u001b[39m:\n\u001b[1;32m---> 47\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_binary_paths\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mbrowser_path\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\common\\driver_finder.py:78\u001b[0m, in \u001b[0;36mDriverFinder._binary_paths\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m     76\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[0;32m     77\u001b[0m     msg \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUnable to obtain driver for \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mbrowser\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m---> 78\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m NoSuchDriverException(msg) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01<PERSON>r\u001b[39;00m\n\u001b[0;32m     79\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_paths\n", "\u001b[1;31mNoSuchDriverException\u001b[0m: Message: Unable to obtain driver for chrome; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location\n"]}], "source": ["def scrape_glassdoor_jobs(keyword, location=\"United States\", num_pages=1):\n", "    # Configure the WebDriver (adjust the path to the WebDriver as needed)\n", "    service = Service('/path/to/chromedriver')\n", "    options = webdriver.ChromeOptions()\n", "    options.add_argument('--headless')  # Run browser in headless mode\n", "    driver = webdriver.Chrome(service=service, options=options)\n", "\n", "    jobs = []\n", "    base_url = \"https://www.glassdoor.com/Job/jobs.htm\"\n", "\n", "    for page in range(num_pages):\n", "        # Construct the URL with query parameters\n", "        params = {\n", "            'sc.keyword': keyword,\n", "            'locT': 'C',\n", "            'locId': location,\n", "            'p': page + 1\n", "        }\n", "        url = base_url + '?' + '&'.join([f\"{k}={v}\" for k, v in params.items()])\n", "        driver.get(url)\n", "        time.sleep(3)  # Wait for the page to load\n", "\n", "        soup = BeautifulSoup(driver.page_source, 'html.parser')\n", "        job_listings = soup.find_all('li', class_='react-job-listing')\n", "\n", "        for job in job_listings:\n", "            try:\n", "                title = job.find('a', class_='jobLink').text.strip()\n", "                company = job.find('div', class_='jobHeader').find('a').text.strip()\n", "                location = job.find('span', class_='jobLocation').text.strip()\n", "                jobs.append({\"title\": title, \"company\": company, \"location\": location})\n", "            except AttributeError:\n", "                continue\n", "\n", "    driver.quit()\n", "    return pd.DataFrame(jobs)\n", "\n", "\n", "jobs_df = scrape_glassdoor_jobs(\"Data Scientist\", location=\"India\", num_pages=2)\n", "print(jobs_df.head())\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Clean and Preprocess Job Data\n", "Remove stopwords\n", "Tokenize and lemmatize the text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}