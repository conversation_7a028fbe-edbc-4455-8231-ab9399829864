{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from IntegrationUtilities import PrescintoIntegrationUtilities"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["m = PrescintoIntegrationUtilities(server = 'IN',token ='1c798bcc-a845-475c-a133-7e410ae4edc6')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["plantName = 'IN.INTE.KIDS'\n", "startDate = '2023-01-01'\n", "endDate = '2025-02-19'"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Response status code: 422\n", "Response error: {\"detail\":[{\"type\":\"dict_type\",\"loc\":[\"body\",\"condition\"],\"msg\":\"Input should be a valid dictionary\",\"input\":null}]}\n"]}], "source": ["category = ['Alarm']\n", "params = ['Inverter Alarm']\n", "alarmDf = m.fetchDataV2(plantName,category,params,None,startDate,endDate)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}