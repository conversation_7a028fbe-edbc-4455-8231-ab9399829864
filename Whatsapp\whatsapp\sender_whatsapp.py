import requests
import os
from dotenv import load_dotenv
from helper.logger_setup import setup_logger
from DB.db_ops import insert_message_data_db

load_dotenv()

WABATOKEN = os.getenv('WABATOKEN')
URL = os.getenv('URL')
MEDIA_UPLOAD_URL = os.getenv('MEDIA_UPLOAD_URL')

logging = setup_logger('send_whatsapp', 'send_whatsapp.log')

def send_whatsapp_document(recipient_number: str, document_url: str, caption: str, file_name: str):
    """
    Sends a PDF document via WhatsApp using the WhatsApp Cloud API.

    Parameters:
    recipient_number (str): The recipient's WhatsApp phone number (including country code).
    document_url (str): Publicly accessible URL of the PDF document.
    caption (str, optional): Caption to send with the document. Defaults to an empty string.
    file_name (str, optional): Name of the document file. Defaults to pdf filename.

    Returns:
    dict: Response from the WhatsApp API.
    """
    payload = {
        "messaging_product": "whatsapp",
        "recipient_type": "individual",
        "to": recipient_number,
        "type": "document",
        "document": {
            # "link": document_url,
            "id": document_url,
            "caption": caption
        }
    }

    if file_name is not None:
        payload["document"]["filename"] = file_name

    headers = {
        "Authorization": f"Bearer {WABATOKEN}",
        "Content-Type": "application/json"
    }

    try:
        logging.info(f"Sending document to {recipient_number} - {file_name or 'No filename provided'}")
        response = requests.post(URL, headers=headers, json=payload)
        response.raise_for_status()
        logging.info(f"Document sent successfully to {recipient_number} - Status Code: {response.status_code}")
        return response.json()

    except requests.exceptions.HTTPError as http_err:
        logging.error(f"HTTP error occurred while sending document to {recipient_number}: {http_err} - Response: {response.text}")
        return {"error": str(http_err)}

    except requests.exceptions.RequestException as req_err:
        logging.error(f"Request error occurred while sending document to {recipient_number}: {req_err}")
        return {"error": str(req_err)}


def send_msg(incoming_num, msg):
    """
    Sends a text message via WhatsApp using the WhatsApp Cloud API.

    Parameters:
    incoming_num (str): The recipient's WhatsApp phone number (including country code).
    msg (str): The message body.
    template_name (str, optional): Template name to use if the 24-hour rule applies.

    Returns:
    dict: Response from the WhatsApp API.
    """
    payload = {
        "messaging_product": "whatsapp",
        "recipient_type": "individual",
        "to": incoming_num,
        "type": "text",
        "text": {
            "preview_url": False,
            "body": msg
        }
    }

    headers = {
        'Authorization': f'Bearer {WABATOKEN}',
        'Content-Type': 'application/json'
    }

    try:
        response = requests.post(URL, headers=headers, json=payload)
        response.raise_for_status()
        logging.info(f"Message sent successfully to {incoming_num} - Status Code: {response.status_code}")
        return response.json()

    except requests.exceptions.RequestException as e:
        logging.error(f"Request error occurred while sending message to {incoming_num}: {e}")

        # Check for 24-hour rule violation
        if response is not None and response.status_code == 400:
            error_details = response.json().get('error', {})
            
        else:
            logging.error(f"Error sending message to {incoming_num}: {error_details.get('message')}")
    return {"error": str(e)}


def send_template(incoming_num, template_name):
    """
    Sends a template message when the 24-hour rule is applied.

    Parameters:
    incoming_num (str): The recipient's WhatsApp phone number (including country code).
    template_name (str): Name of the template to be used.

    Returns:
    dict: Response from the WhatsApp API.
    """
    payload = {
        "messaging_product": "whatsapp",
        "to": incoming_num,
        "type": "template",
        "template": {
            "name": template_name,
            "language": {
                "code": "en"
            }
        }
    }

    headers = {
        'Authorization': f'Bearer {WABATOKEN}',
        'Content-Type': 'application/json'
    }

    try:
        response = requests.post(URL, headers=headers, json=payload)
        response.raise_for_status()
        print(response.json)
        print(response.headers)
        print(response.text)
        logging.info(f"Template '{template_name}' sent successfully to {incoming_num} - Status Code: {response.status_code}")
        return response.json()

    except requests.exceptions.RequestException as e:
        logging.error(f"Error sending template '{template_name}' to {incoming_num}: {e}")
        return {"error": str(e)}


def delete_whatsapp_media(media_id):
    delete_url = f'https://graph.facebook.com/v18.0/{media_id}'
    headers = {
        'Authorization': f'Bearer {WABATOKEN}'
    }

    response = requests.delete(delete_url, headers=headers)
    if response.status_code == 200:
        print(f"Media ID {media_id} deleted successfully!")
        return True
    else:
        print(f"Failed to delete media: {response.text}")
        return False


def send_dgr_report(to_number, media_id, customer_name, plant_name, date, caption):
    """
    Sends a DGR report to a customer via WhatsApp using the WhatsApp Business API.

    Args:
        to_number (str): Recipient's phone number in international format.
        media_id (str): ID of the uploaded media file.
        customer_name (str): Customer's name.
        plant_name (str): Name of the plant.
        date (str): Date of the report.
        caption (str): Filename for the media file.

    Returns:
        bool: True if the message was sent successfully, False otherwise.
    """
    try:
        headers = {
            'Authorization': f'Bearer {WABATOKEN}',
            'Content-Type': 'application/json'
        }

        payload = {
            "messaging_product": "whatsapp",
            "recipient_type": "individual",
            "to": to_number,
            "type": "template",
            "template": {
                "name": "purchase_receipt_2",
                "language": {
                    "code": "en_US"
                },
                "components": [
                    {
                        "type": "header",
                        "parameters": [
                            {
                                "type": "document",
                                "document": {
                                    "id": media_id,
                                    "filename": caption
                                }
                            }
                        ]
                    },
                    {
                        "type": "body",
                        "parameters": [
                            {"type": "text", "text": customer_name},
                            {"type": "text", "text": plant_name},
                            {"type": "text", "text": date}
                        ]
                    }
                ]
            }
        }

        response = requests.post(URL, headers=headers, json=payload)

        if response.status_code == 200:
            logging.info(f"DGR sent successfully to {to_number}")
            return True
        else:
            logging.error(f"Failed to send message to {to_number} : {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logging.exception(f"Error in send_dgr_report: {e}")
        return False


def upload_whatsapp_media(file_path):
    """
    Uploads a media file to WhatsApp and returns the media ID.

    Args:
        file_path (str): Path to the file to be uploaded.

    Returns:
        str: Media ID if successful, None otherwise.
    """
    try:
        if not os.path.exists(file_path):
            logging.error(f"File not found: {file_path}")
            return None

        logging.info(f"Uploading media file: {file_path}")

        payload = {'messaging_product': 'whatsapp'}
        files = {'file': (os.path.basename(file_path), open(file_path, 'rb'), 'application/pdf')}
        headers = {'Authorization': f'Bearer {WABATOKEN}'}

        response = requests.post(MEDIA_UPLOAD_URL, headers=headers, data=payload, files=files)
        if response.status_code == 200:
            media_id = response.json().get('id')
            logging.info(f"Media uploaded successfully. Media ID: {media_id}")
            return media_id
        else:
            logging.error(f"Failed to upload media: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        logging.exception(f"Error in upload_whatsapp_media: {e}")
        return None

    finally:
        # Close the file if it was opened
        if 'files' in locals():
            files['file'][1].close()





def send_solar_dgr_template(
    to_number: str,
    customer_name: str,
    date: str,
    today_gen: str,
    today_pr: str,
    today_poa: str,
    plant_short_name: str,
    dgr_path: str
   
) -> bool:
    """
    Sends a WhatsApp template message for Solar Daily Generation Report.

    Args:
        access_token (str): WhatsApp Business API token.
        from_phone_number_id (str): Phone number ID from WhatsApp Business account.
        to_number (str): Recipient's WhatsApp number (e.g., '91XXXXXXXXXX').
        customer_name (str): Customer's name (used in header).
        date (str): Date of report (used in {{1}} in body).
        today_gen (str): Today's generation in kWh.
        today_pr (str): Today's PR %.
        today_poa (str): Today's POA.
        month_gen (str): Monthly generation in kWh.
        month_pr (str): Monthly PR %.
        month_poa (str): Monthly POA.
        template_name (str): Template name from WhatsApp Manager.
        language_code (str): Language code (e.g., 'en', 'en_US').

    Returns:
        bool: True if message was sent successfully, else False.
    """

    

    headers = {
        "Authorization": f"Bearer {WABATOKEN}",
        "Content-Type": "application/json"
    }

    payload = {
        "messaging_product": "whatsapp",
        "recipient_type": "individual",
        "to": to_number,
        "type": "template",
        "template": {
            "name": "delivery_solar",
            "language": {
                "code": "en_US"
            },
            "components": [
                {
                    "type": "header",
                    "parameters": [
                        {"type": "text", "text": customer_name}
                    ]
                },
                {
                    "type": "body",
                    "parameters": [
                        {"type": "text", "text": date},         # {{1}} in body
                        {"type": "text", "text": today_gen},    # {{2}}
                        {"type": "text", "text": today_pr},     # {{3}}
                        {"type": "text", "text": today_poa},    # {{4}}
                    
                    ]
                }
            ]
        }
    }

    try:
        response = requests.post(URL, headers=headers, json=payload)

       
        
        if response.status_code == 200:
            data = response.json()
            wa_id = data.get("contacts", [{}])[0].get("wa_id")
            message_id = data.get("messages", [{}])[0].get("id")
            # message_status = data.get("messages", [{}])[0].get("message_status")
            report_type = "solar"
            data = [{
                "wa_id": wa_id,
                "message_id": message_id,
                "report_type": report_type,
                "plant_short_name": plant_short_name,
                'plant_long_name': customer_name,
                'date': date,
                'dgr_path': dgr_path  # Placeholder for DGR path if needed
            }]
            insert_message_data_db(data)
            logging.info(f"Solar DGR template sent successfully to {to_number}")
            return True
        else:
            logging.error(f"Failed to send Solar DGR to {to_number}. "
                          f"Status Code: {response.status_code}, Response: {response.text}")
            return False
    except Exception as e:
        logging.exception(f"Exception in sending Solar DGR to {to_number}: {e}")
        return False





def send_wind_dgr_template(
    to_number: str,
    customer_name: str,
    today_gen: str,
    today_ws: str,
    report_date: str,
    plant_short_name: str,
    dgr_path: str
) -> bool:
    """
    Sends a WhatsApp template message for Wind Daily Generation Report.

    Args:
        access_token (str): WhatsApp Business API token.
        from_phone_number_id (str): Phone number ID from WhatsApp Business account.
        to_number (str): Recipient's WhatsApp number (e.g., '91XXXXXXXXXX').
        customer_name (str): Customer's name for the header.
        today_gen (str): Today's generation in kWh.
        today_ws (str): Today's wind speed in m/s.
        month_gen (str): Monthly generation in kWh.
        month_ws (str): Monthly wind speed in m/s.
        report_date (str): Date for the report.
        template_name (str): WhatsApp template name.
        language_code (str): Language code (e.g., 'en', 'en_US').

    Returns:
        bool: True if the message was sent successfully, False otherwise.
    """


    headers = {
        "Authorization": f"Bearer {WABATOKEN}",
        "Content-Type": "application/json"
    }

    payload = {
        "messaging_product": "whatsapp",
        "recipient_type": "individual",
        "to": to_number,
        "type": "template",
        "template": {
            "name": "wind_dgr_new",
            "language": {
                "code": "en_US"
            },
            "components": [
                {
                    "type": "header",
                    "parameters": [
                        {"type": "text", "text": customer_name}
                    ]
                },
                {
                    "type": "body",
                    "parameters": [
                        {"type": "text", "text": today_gen},    # {{1}}
                        {"type": "text", "text": today_ws},     # {{2}}
                        {"type": "text", "text": report_date}   # {{5}}
                    ]
                }
            ]
        }
    }

    try:
        response = requests.post(URL, headers=headers, json=payload)
        if response.status_code == 200:
            logging.info(f"Wind DGR template sent successfully to {to_number}")

            data = response.json()
            wa_id = data.get("contacts", [{}])[0].get("wa_id")
            message_id = data.get("messages", [{}])[0].get("id")
            # message_status = data.get("messages", [{}])[0].get("message_status")
            report_type = "wind"
            data = [{
                "wa_id": wa_id,
                "message_id": message_id,
                "report_type": report_type,
                "plant_short_name": plant_short_name,
                'plant_long_name': customer_name,
                'date': report_date,
                'dgr_path': dgr_path  # Placeholder for DGR path if needed
            }]
            insert_message_data_db(data)
            return True
        else:
            logging.error(f"Failed to send Wind DGR to {to_number}. "
                          f"Status Code: {response.status_code}, Response: {response.text}")
            return False
    except Exception as e:
        logging.exception(f"Exception in sending Wind DGR to {to_number}: {e}")
        return False



def send_both_dgr_template(
    to_number: str,
    customer_name: str,
    wind_today_gen: str,
    wind_today_ws: str,
    report_date: str,
    solar_today_gen: str,
    solar_today_pr: str,
    solar_today_poa: str,
    dgr_path: str,
    plant_short_name_wind: str,
    total_calculated: str
   
) -> bool:
   
    headers = {
        "Authorization": f"Bearer {WABATOKEN}",
        "Content-Type": "application/json"
    }

    payload = {
        "messaging_product": "whatsapp",
        "recipient_type": "individual",
        "to": to_number,
        "type": "template",
        "template": {
            "name": "combine_dgr_new",
            "language": {
                "code": "en"
            },
            "components": [
                {
                    "type": "header",
                    "parameters": [
                        {"type": "text", "text": customer_name}  # {{1}} in header
                    ]
                },
                {
                    "type": "body",
                    "parameters": [
                        {"type": "text", "text": wind_today_gen},   # {{1}}
                        {"type": "text", "text": wind_today_ws},    # {{2}}
                        {"type": "text", "text": report_date},      # {{3}}
                        {"type": "text", "text": solar_today_gen},  # {{5}}
                        {"type": "text", "text": solar_today_pr},   # {{6}}
                        {"type": "text", "text": solar_today_poa},  # {{7}}
                        {"type": "text", "text": total_calculated}  # {{8}}


                    ]
                }
            ]
        }
    }

    try:
        response = requests.post(URL, headers=headers, json=payload)
        if response.status_code == 200:
            data = response.json()
            wa_id = data.get("contacts", [{}])[0].get("wa_id")
            message_id = data.get("messages", [{}])[0].get("id")
            # message_status = data.get("messages", [{}])[0].get("message_status")
            report_type = "combined"
            data = [{
                "wa_id": wa_id,
                "message_id": message_id,
                "report_type": report_type,
                "plant_short_name": plant_short_name_wind,
                'plant_long_name': customer_name,
                'date': report_date,
                'dgr_path': dgr_path  # Placeholder for DGR path if needed
            }]
            insert_message_data_db(data)
            logging.info(f"Solar + Wind DGR sent successfully to {to_number}")
            return True
        else:
            logging.error(f"Failed to send Solar + Wind DGR to {to_number}. "
                          f"Status Code: {response.status_code}, Response: {response.text}")
            return False
    except Exception as e:
        logging.exception(f"Exception sending Solar + Wind DGR: {e}")
        return False
