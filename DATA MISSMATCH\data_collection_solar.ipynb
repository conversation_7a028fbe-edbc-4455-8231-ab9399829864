{"cells": [{"cell_type": "code", "execution_count": 1, "id": "60ad1684", "metadata": {}, "outputs": [], "source": ["from IntegrationUtilities import PrescintoIntegrationUtilities\n", "m = PrescintoIntegrationUtilities(server = 'IN',token ='884c5f4d-223b-4191-9400-3afd6ee9b538')"]}, {"cell_type": "code", "execution_count": 43, "id": "903f2ab6", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\<PERSON><PERSON><PERSON><PERSON><PERSON>\\Wind_solar\\DATA MISSMATCH\\IntegrationUtilities.py:308: FutureWarning: Passing literal json to 'read_json' is deprecated and will be removed in a future version. To read from a literal string, wrap it in a 'StringIO' object.\n", "  dataResp = pd.read_json(resp, orient=\"split\")\n"]}], "source": ["plantName = 'IN.INTE.BAL2'\n", "startDate = '2022-09-05'\n", "endDate = '2025-03-31'\n", "\n", "category = ['Plant']\n", "params = ['Daily Energy']\n", "condition_generation = {\"Daily Energy\": \"last\"}\n", "dgr = m.fetchDataV2(plantName,category,params,None, startDate, endDate, granularity = '60m', condition=condition_generation)"]}, {"cell_type": "code", "execution_count": 44, "id": "5fcd6c53", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>time</th>\n", "      <th>Plant.Daily Energy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2022-09-05T00:00:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2022-09-05T01:00:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2022-09-05T02:00:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2022-09-05T03:00:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2022-09-05T04:00:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22531</th>\n", "      <td>2025-03-31T19:00:00Z+05:30</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22532</th>\n", "      <td>2025-03-31T20:00:00Z+05:30</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22533</th>\n", "      <td>2025-03-31T21:00:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22534</th>\n", "      <td>2025-03-31T22:00:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22535</th>\n", "      <td>2025-03-31T23:00:00Z+05:30</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>22536 rows × 2 columns</p>\n", "</div>"], "text/plain": ["                             time  Plant.Daily Energy\n", "0      2022-09-05T00:00:00Z+05:30                 NaN\n", "1      2022-09-05T01:00:00Z+05:30                 NaN\n", "2      2022-09-05T02:00:00Z+05:30                 NaN\n", "3      2022-09-05T03:00:00Z+05:30                 NaN\n", "4      2022-09-05T04:00:00Z+05:30                 NaN\n", "...                           ...                 ...\n", "22531  2025-03-31T19:00:00Z+05:30                 0.0\n", "22532  2025-03-31T20:00:00Z+05:30                 0.0\n", "22533  2025-03-31T21:00:00Z+05:30                 NaN\n", "22534  2025-03-31T22:00:00Z+05:30                 NaN\n", "22535  2025-03-31T23:00:00Z+05:30                 0.0\n", "\n", "[22536 rows x 2 columns]"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["dgr"]}, {"cell_type": "code", "execution_count": 45, "id": "6d69b517", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "df = pd.DataFrame(dgr)\n", "df.to_csv(f'{plantName}.csv', index=False)"]}, {"cell_type": "code", "execution_count": 76, "id": "2ed5dc75", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Column added and data saved.\n"]}], "source": ["import pandas as pd\n", "\n", "# Load the CSV\n", "df = pd.read_csv('IN.INTE.BAL1.csv')\n", "\n", "# Add a new column and fill with default value\n", "df['Plant Short Name'] = 'IN.INTE.BAL1'\n", "\n", "# (Optional) Add logic to compute the new column differently per row\n", "# For example: df['new_col'] = df['generation'].apply(lambda x: 'High' if x > 1000 else 'Low')\n", "\n", "# Save back to CSV (overwrite or new file)\n", "df.to_csv('IN.INTE.BAL1.csv', index=False)\n", "\n", "print(\"Column added and data saved.\")\n"]}, {"cell_type": "code", "execution_count": 66, "id": "0d5e37a5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["New column 'DateValue' added with formatted date.\n"]}], "source": ["import pandas as pd\n", "\n", "# Read the CSV\n", "df = pd.read_csv('IN.INTE.SKRT2.csv')\n", "\n", "# Convert the ISO datetime to datetime object\n", "df['date_parsed'] = pd.to_datetime(df['time'], utc=True)\n", "\n", "# Format it as \"Month Day, Year\" and store in new column\n", "df['DateValue'] = df['date_parsed'].dt.strftime('%B %d, %Y')\n", "\n", "# Optional: Drop the intermediate datetime column if not needed\n", "df.drop(columns=['date_parsed'], inplace=True)\n", "\n", "# Save to a new CSV\n", "df.to_csv('IN.INTE.STOV.csv', index=False)\n", "\n", "print(\"New column 'DateValue' added with formatted date.\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "db7a5fab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📥 Processing: IN.INTE.BAL1.csv\n", "✅ Inserted 22032 records from IN.INTE.BAL1.csv\n", "📥 Processing: IN.INTE.BAL2.csv\n", "✅ Inserted 22536 records from IN.INTE.BAL2.csv\n", "📥 Processing: IN.INTE.JOD1.csv\n", "✅ Inserted 22536 records from IN.INTE.JOD1.csv\n", "📥 Processing: IN.INTE.JOD2.csv\n", "✅ Inserted 22536 records from IN.INTE.JOD2.csv\n", "📥 Processing: IN.INTE.KIDS.csv\n", "✅ Inserted 1704 records from IN.INTE.KIDS.csv\n", "📥 Processing: IN.INTE.KPLS.csv\n", "✅ Inserted 25488 records from IN.INTE.KPLS.csv\n", "📥 Processing: IN.INTE.KSIS.csv\n", "✅ Inserted 8112 records from IN.INTE.KSIS.csv\n", "📥 Processing: IN.INTE.SAAB.csv\n", "✅ Inserted 22176 records from IN.INTE.SAAB.csv\n", "📥 Processing: IN.INTE.SKRT2.csv\n", "✅ Inserted 22176 records from IN.INTE.SKRT2.csv\n", "📥 Processing: IN.INTE.STOV.csv\n", "✅ Inserted 22176 records from IN.INTE.STOV.csv\n", "🎉 All CSV files processed and inserted successfully.\n"]}], "source": ["import os\n", "import pandas as pd\n", "from sqlalchemy import create_engine, Column, String, DateTime, Float, Integer\n", "from sqlalchemy.orm import sessionmaker, declarative_base\n", "\n", "# -------------------- Configuration --------------------\n", "DATABASE_URL = 'mysql+pymysql://root:test123@localhost/data_missmatch'\n", "CSV_FOLDER_PATH = 'CSV'  # Change to your actual folder path\n", "\n", "# -------------------- SQLAlchemy Setup --------------------\n", "Base = declarative_base()\n", "\n", "class WindDGR(Base):\n", "    __tablename__ = 'data_solar'\n", "    id = Column(Integer, primary_key=True, autoincrement=True)\n", "    time = Column(DateTime)\n", "    daily_energy = Column(Float, nullable=True)\n", "    plant_long_name = Column(String(255))\n", "    date_value = Column(String(50))\n", "    plant_short_name = Column(String(100))\n", "\n", "# -------------------- Create Table --------------------\n", "engine = create_engine(DATABASE_URL)\n", "Base.metadata.create_all(engine)\n", "Session = sessionmaker(bind=engine)\n", "session = Session()\n", "\n", "# -------------------- Process CSV Files --------------------\n", "for file_name in os.listdir(CSV_FOLDER_PATH):\n", "    if file_name.endswith('.csv'):\n", "        file_path = os.path.join(CSV_FOLDER_PATH, file_name)\n", "        print(f\"📥 Processing: {file_name}\")\n", "        \n", "        df = pd.read_csv(file_path, encoding='utf-8')\n", "\n", "        # Convert 'time' to datetime (UTC)\n", "        df['time'] = pd.to_datetime(df['time'], utc=True, errors='coerce')\n", "\n", "        # Format date into 'Month Day, Year'\n", "        df['DateValue'] = df['time'].dt.strftime('%B %d, %Y')\n", "\n", "        # Replace blank strings with None (this step is just for handling blank values)\n", "        df.replace({r'^\\s*$': None}, regex=True, inplace=True)\n", "\n", "        # Replace NaN values in all columns with 0, except 'time' and 'DateValue'\n", "        df['Daily Energy'] = df['Daily Energy'].fillna(0)  # Replace NaN in 'Daily Energy' column with 0\n", "\n", "        # Map rows to model\n", "        records = [\n", "            WindDGR(\n", "                time=row['time'],e\n", "                daily_energy=row['Daily Energy'],\n", "                plant_long_name=row['Plant Long Name'],\n", "                date_value=row['DateValue'],\n", "                plant_short_name=row['Plant Short Name']\n", "            )\n", "            for _, row in df.iterrows()\n", "        ]\n", "\n", "        # Insert in bulk\n", "        session.bulk_save_objects(records)\n", "        session.commit()\n", "        print(f\"✅ Inserted {len(records)} records from {file_name}\")\n", "\n", "# Close DB session\n", "session.close()\n", "print(\"🎉 All CSV files processed and inserted successfully.\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "d120a29d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}