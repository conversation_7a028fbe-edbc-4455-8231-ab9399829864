def extract_message_details(message_details):
	button_id, button_title, incoming_msg = "NA", "", ""
	incoming_name = ""
	incoming_num = ""
	message_type = ""
	try:
		message_id = message_details['entry'][0]['changes'][0]['value']['messages'][0]["id"]
		wabaid = message_details['entry'][0]['id']
	except KeyError:
		message_id = message_details['entry'][0]['changes'][0]['value']['statuses'][0]["id"]
		wabaid = message_details['entry'][0]['id']
	entries = message_details['entry'] 
	for entry in entries:
		changes = entry['changes']
		for change in changes:
			value = change['value']
			if "statuses" not in value:
					messages = value.get('messages', [])
					contacts = value.get('contacts', [])
					for contact in contacts:
						incoming_name = contact['profile']['name']
					for message in messages:
						incoming_num = message['from']
						message_type = message['type']
						if message_type == 'text':
							incoming_msg = message['text']['body']
						elif message_type == 'button':
							incoming_msg = message['button']['text']
							button_id = message['button']['id']
							button_title = message['button']['title']
						
						elif message_type == 'interactive':
							interactive = message['interactive']
							if 'list_reply' in interactive:
									incoming_msg = interactive['list_reply']['description']
									button_id = interactive['list_reply']['id']
									button_title = interactive['list_reply']['title']
							elif 'button_reply' in interactive:
									incoming_msg = interactive['button_reply']['title']
									button_id = interactive['button_reply']['id']
									button_title = interactive['button_reply']['title']
							
						else:
							incoming_msg = 'Sorry! this message type is currently not supported, please try sending text messages'

	return (incoming_num, incoming_name, incoming_msg, button_id, button_title, message_type, message_id, wabaid)




def extract_whatsapp_message_info(data):
    """
    Safely extracts relevant WhatsApp message info from the webhook payload.

    Args:
        data (dict): Webhook payload from WhatsApp.

    Returns:
        dict: A dictionary with extracted fields.
    """
    try:
        entry = data.get('entry', [{}])[0]
        change = entry.get('changes', [{}])[0].get('value', {})

        # profile_name = change.get('contacts', [{}])[0].get('profile', {}).get('name')
        incoming_num = change.get('contacts', [{}])[0].get('wa_id')
        # message_from = change.get('messages', [{}])[0].get('from')
        # id = change.get('messages', [{}])[0].get('id')
        # context_from = change.get('messages', [{}])[0].get('context', {}).get('from')
        message_id = change.get('messages', [{}])[0].get('context', {}).get('id')
        # button_payload = change.get('messages', [{}])[0].get('button', {}).get('payload')
        button_text = change.get('messages', [{}])[0].get('button', {}).get('text')
		

        print(f"Incoming number: {incoming_num}")
        print(f"Button text: {button_text}")
        print(f"Message ID: {message_id}")


        return incoming_num, button_text, message_id
        

    except Exception as e:
        print("Error extracting WhatsApp message info: %s", str(e))
        return {}

