import pandas as pd
from datetime import datetime

# Load the actual data CSV (with UNIT DATETIME)
data_df = pd.read_excel("Full consumption data cloud9.xlsx")

# Parse UNIT DATETIME
data_df['UNIT DATETIME'] = pd.to_datetime(data_df['UNIT DATETIME'], format='%d-%m-%Y %H:%M:%S')

# Generate Datevalue, Month No, Month Name, Hour No
data_df['Datevalue'] = data_df['UNIT DATETIME'].dt.strftime('%B %d, %Y')
data_df['Month No'] = data_df['UNIT DATETIME'].dt.month
data_df['Month Name'] = data_df['UNIT DATETIME'].dt.strftime('%b')
data_df['Hour No'] = data_df['UNIT DATETIME'].dt.hour + 1  # Convert hour to 1–24 format

# Fix edge case: hour 0 becomes 24
data_df['Hour No'] = data_df['Hour No'].replace({25: 1}).replace({0: 24})

# Define static metadata (fill in your actual values)
plant_metadata = {
    'Plant ID': 3348,
    'Plant Long Name': 'Kids Clinic India Limited',
    'Plant Short Name': 'IN.INTE.KIDS',
    'Account ID': 55,
    'Account Name': 'Integrum'
}

# Add metadata to the dataframe
for key, value in plant_metadata.items():
    data_df[key] = value

# Rearrange columns: metadata first, then time fields, then remaining data
cols_order = ['Plant ID', 'Plant Long Name', 'Plant Short Name', 'Account ID', 'Account Name',
              'Datevalue', 'Month No', 'Month Name', 'Hour No'] + \
             [col for col in data_df.columns if col not in 
              ['UNIT DATETIME'] + list(plant_metadata.keys()) + 
              ['Datevalue', 'Month No', 'Month Name', 'Hour No']]

data_df = data_df[cols_order]

# Save to CSV
data_df.to_csv("processed_data.csv", index=False)


import pandas as pd

# Load the processed CSV file
df = pd.read_csv("processed_data.csv")

# Convert 'Datevalue' from string to datetime for proper sorting
df['Datevalue'] = pd.to_datetime(df['Datevalue'], format='%B %d, %Y')

# Sort by 'Datevalue' and then optionally by 'Hour No' for time order
df = df.sort_values(by=['Datevalue', 'Hour No'])

# (Optional) Convert 'Datevalue' back to string format if you want to keep original format
df['Datevalue'] = df['Datevalue'].dt.strftime('%B %d, %Y')

# Save the sorted CSV
df.to_csv("processed_data.csv", index=False)


import pandas as pd
from pathlib import Path

# Raw data
raw_data = [
    [1, 'Kids Clinic India Limited', 'Captive', 'C2HT-136', 'Malleswaram', 45220, 45457, 53318, 57674,  55864 ],
    [2, 'Kids Clinic India Limited', 'Captive', 'S13HT-87', 'ELECTRONIC CITY', 55463, 56673, 67099, 40651, 66151],
    [3, 'Kids Clinic India Limited', 'Captive', 'S12HT-99', 'KANAKAPURA', 37649, 41434, 51469, 84618,  50721],
    [4, 'Kids Clinic India Limited', 'Captive', 'S11HT-124', 'BELLANDUR', 36222, 37562, 47046, 66192,  49250],
    [5, 'Kids Clinic India Limited', 'Captive', 'S11HT-419', 'SARJAPURA', 39944, 43224, 53210, 29022, 55200],
    [6, 'Kids Clinic India Limited', 'Captive', 'C8HT-111', 'SAHAKAR NAGAR', 46288, 46856, 63174, 56010, 61860],
    [7, 'Kids Clinic India Limited', 'Captive', 'E8HT-203', 'HRBR UNIT', 35507, 36389, 51818,  67539 ,  48479],
    [8, 'Kids Clinic India Limited', 'Captive', 'E4HT-355', 'WHITEFIELD', 57520, 58820, 72000, 53758,  75760],
    [9, 'Kids Clinic India Limited', 'Captive', 'S11BHT 406', 'BELLANDUR CORP. OFFICE', 21892, 23529, 27740, 76880,  27903],
    [10, 'Kids Clinic India Limited', 'Captive', 'C8HT-135', 'THANISANDRA', 43750, 49742, 53512, 63301,  62107],
    [11, 'Kids Clinic India Limited', 'Captive', 'E6HT209', 'Old Airport Road', None, None, None, 89500,  86362]
]
columns = ['Sl.No', 'Consumer', 'Type', 'R.R.No', 'Division', 'Jan-25', 'Feb-25', 'Mar-25', 'Apr-25', 'May-25']
df = pd.DataFrame(raw_data, columns=columns)

# Days in each month for 2025
days_in_month = {'Jan-25': 31, 'Feb-25': 28, 'Mar-25': 31, 'Apr-25': 30, 'May-25': 31}

# Melt to month-level
df_melt = df.melt(
    id_vars=['Sl.No', 'Consumer', 'Type', 'R.R.No', 'Division'],
    value_vars=list(days_in_month.keys()),
    var_name='Month',
    value_name='Total_kWh'
)
# Compute daily average, skip missing
df_melt['Daily_kWh'] = df_melt.apply(
    lambda r: (r['Total_kWh'] / days_in_month[r['Month']]) if pd.notna(r['Total_kWh']) else 0,
    axis=1
)

# Time slot definitions
time_slots = [
    {'start': 22, 'end': 6,  'pct': 25.78},
    {'start': 6,  'end': 9, 'pct': 16.85},
    {'start': 9, 'end': 18, 'pct': 38.98},
    {'start': 18, 'end': 22, 'pct': 18.72},
]

def hours_in_slot(start, end):
    "Return list of hours, handling overnight wrap." 
    return list(range(start, 24)) + list(range(0, end)) if end <= start else list(range(start, end))

# Generate hourly records
records = []
for _, row in df_melt[df_melt['Daily_kWh'] > 0].iterrows():
    # month date range
    month_str = row['Month']
    dates = pd.date_range(
        start=pd.to_datetime(f'01-{month_str}', format='%d-%b-%y'),
        periods=days_in_month[month_str],
        freq='D'
    )
    for date in dates:
        slot_id = 1
        for slot in time_slots:
            hrs = hours_in_slot(slot['start'], slot['end'])
            kwh_hour = row['Daily_kWh'] * slot['pct'] / 100 / len(hrs)
            for hr in hrs:
                records.append({
                    'Sl.No': row['Sl.No'],
                    'Consumer': row['Consumer'],
                    'Type': row['Type'],
                    'R.R.No': row['R.R.No'],
                    'Division': row['Division'],
                    'Date': date.date(),
                    'Hour': hr,
                    'Time_Slot': slot_id,
                    'Energy_kWh': round(kwh_hour, 4)
                })
            slot_id += 1

hourly_df = pd.DataFrame(records)
# Sort by consumer, date, hour
hourly_df = hourly_df.sort_values(['Sl.No', 'Date', 'Hour']).reset_index(drop=True)

# Save to CSV

hourly_df.to_csv("Hourly_Energy_Consumption.csv", index=False)
# print(f"Saved hourly data to: {output}")


hourly_df

import pandas as pd
from datetime import datetime

# Load the CSV
df = pd.read_csv("Hourly_Energy_Consumption.csv")  # Replace with your actual file name

# Static values from your example
plant_id = 3348
plant_long_name = "Kids Clinic India Limited"
plant_short_name = "IN.INTE.KIDS"
account_id = 55
account_name = "Integrum"

# Add new columns
df["Plant ID"] = plant_id
df["Plant Long Name"] = plant_long_name
df["Plant Short Name"] = plant_short_name
df["Account ID"] = account_id
df["Account Name"] = account_name

# Parse Date and add date-related fields
parsed_dates = pd.to_datetime(df["Date"], format="%Y-%m-%d")
df["Datevalue"] = parsed_dates.dt.strftime("%d-%b-%y")
df["Month No"] = parsed_dates.dt.month
df["Month Name"] = parsed_dates.dt.strftime("%b")

# Hour No and COUNTER
df["Hour No"] = df["Hour"]
df["COUNTER"] = range(1, len(df) + 1)

# Reorder columns
columns_order = [
    "Plant ID", "Plant Long Name", "Plant Short Name", "Account ID", "Account Name",
    "Datevalue", "Month No", "Month Name", "Hour No", "COUNTER"
] + list(df.columns.difference([
    "Plant ID", "Plant Long Name", "Plant Short Name", "Account ID", "Account Name",
    "Datevalue", "Month No", "Month Name", "Hour No", "COUNTER"
]))

df = df[columns_order]

# Save to new CSV
df.to_csv("output_with_extra_columns.csv", index=False)
print("CSV file updated and saved as 'output_with_extra_columns.csv'")


import pandas as pd

# Load the CSV file
df = pd.read_csv("output_with_extra_columns.csv")  # Replace with your actual filename

# Drop the columns 'Sl.No' and 'COUNTER' if they exist
df = df.drop(columns=["Sl.No", "COUNTER"], errors='ignore')

# Save the updated CSV
df.to_csv("Consumption data Cloud nine - processed_data.csv", index=False)
print("Updated CSV saved as 'output_with_extra_columns.csv'")


import pandas as pd
from datetime import timedelta

import pandas as pd
from datetime import timedelta

def generate_15min_interval_data(raw_data, columns, days_in_month, time_slots, tz_str='Asia/Kolkata'):
    df = pd.DataFrame(raw_data, columns=columns)

    # Melt the month columns
    df_melt = df.melt(
        id_vars=[c for c in columns if c not in days_in_month],
        value_vars=list(days_in_month),
        var_name='Month',
        value_name='Total_kWh'
    )

    # Daily kWh
    df_melt['Daily_kWh'] = df_melt.apply(
        lambda r: (r['Total_kWh'] / days_in_month[r['Month']]) if pd.notna(r['Total_kWh']) else 0,
        axis=1
    )

    def hours_in_slot(start, end):
        return list(range(start, 24)) + list(range(0, end)) if end <= start else list(range(start, end))

    records = []
    for _, row in df_melt[df_melt['Daily_kWh'] > 0].iterrows():
        month_str = row['Month']
        dates = pd.date_range(
            start=pd.to_datetime(f'01-{month_str}', format='%d-%b-%y'),
            periods=days_in_month[month_str],
            freq='D'
        )

        for date in dates:
            for slot in time_slots:
                hrs = hours_in_slot(slot['start'], slot['end'])
                slot_total_energy = row['Daily_kWh'] * slot['pct'] / 100
                energy_per_interval = slot_total_energy / (len(hrs) * 4)

                for hr in hrs:
                    base = pd.Timestamp(date.year, date.month, date.day, hr)
                    for q in range(0, 60, 15):
                        ts = base + timedelta(minutes=q)
                        records.append({
                            **{c: row[c] for c in df_melt.columns if c not in ['Month', 'Total_kWh', 'Daily_kWh']},
                            'time_ts': ts,
                            'Energy_kWh': round(energy_per_interval, 4)
                        })

    out = pd.DataFrame(records)

    # Localize and format time
    out['time'] = (
        out['time_ts']
        .dt.tz_localize('UTC')
        .dt.tz_convert(tz_str)
        .apply(lambda x: f"{x.strftime('%Y-%m-%dT%H:%M:%S')}Z" + x.strftime('%z')[:3] + ':' + x.strftime('%z')[3:])
    )

    out['Date'] = out['time_ts'].dt.date
    out = out.drop(columns=['time_ts'])

    out = out.sort_values(['Consumer', 'R.R.No', 'Date', 'time']).reset_index(drop=True)

    return out


# === USAGE EXAMPLE ===
# Raw data updated with June 2025
raw_data = [
    [1, 'Kids Clinic India Limited', 'Captive', 'C2HT-136', 'Malleswaram', 45220, 45457, 53318, 57674,  55864, 49082.03],
    [2, 'Kids Clinic India Limited', 'Captive', 'S13HT-87', 'Electronic City', 55463, 56673, 67099, 40651, 66151, 66573.00],
    [3, 'Kids Clinic India Limited', 'Captive', 'S12HT-99', 'Kanakapura Road', 37649, 41434, 51469, 84618,  50721, 44071.00],
    [4, 'Kids Clinic India Limited', 'Captive', 'S11HT-124', 'Bellandur', 36222, 37562, 47046, 66192,  49250, 48470.24],
    [5, 'Kids Clinic India Limited', 'Captive', 'S11HT-419', 'Sarjapur Road', 39944, 43224, 53210, 29022, 55200, 48034.52],
    [6, 'Kids Clinic India Limited', 'Captive', 'C8HT-111', 'Sahakarnagar', 46288, 46856, 63174, 56010, 61860, 50000.00],
    [7, 'Kids Clinic India Limited', 'Captive', 'E8HT-203', 'HRBR Layout', 35507, 36389, 51818, 67539,  48479, 41784.00],
    [8, 'Kids Clinic India Limited', 'Captive', 'E4HT-355', 'Whitefield', 57520, 58820, 72000, 53758,  75760, 78737.00],
    [9, 'Kids Clinic India Limited', 'Captive', 'S11BHT 406', 'Corporate Office Bellandur', 21892, 23529, 27740, 76880,  27903, 25596.00],
    [10, 'Kids Clinic India Limited', 'Captive', 'C8HT-135', 'Thanisandra', 43750, 49742, 53512, 63301,  62107, 60556.00],
    [11, 'Kids Clinic India Limited', 'Captive', 'E6HT209', 'Old Airport Road', None, None, None, 89500,  86362, 81614.00]
]
columns = ['Sl.No', 'Consumer', 'Type', 'R.R.No', 'Division', 'Jan-25', 'Feb-25', 'Mar-25', 'Apr-25', 'May-25', 'Jun-25']

days_in_month = {'Jan-25': 31, 'Feb-25': 28, 'Mar-25': 31, 'Apr-25': 30, 'May-25': 31, 'Jun-25': 30}
time_slots = [
    {'start': 22, 'end': 6,  'pct': 24.19},
    {'start': 6,  'end': 10, 'pct': 18.08},
    {'start': 10, 'end': 18, 'pct': 41.18},
    {'start': 18, 'end': 22, 'pct': 16.54},
]

df_15min = generate_15min_interval_data(raw_data, columns, days_in_month, time_slots)

# Optional: Preview
print(df_15min.head())
print(df_15min[['time', 'Energy_kWh']].head())


df_15min.head(20)

df_15min.shape

df_15min.to_csv("full consumption data unitil June.csv")

