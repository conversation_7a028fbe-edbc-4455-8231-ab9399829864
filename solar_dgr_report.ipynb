{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from integration_utilities import PrescintoIntegrationUtilities\n", "m = PrescintoIntegrationUtilities(server = 'IN',token ='37b5265d-2a67-4958-9818-501924ee72df')"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["plantName = 'IN.INTE.KIDS'\n", "# startDate = '2023-01-01'\n", "# endDate = '2025-02-19'\n", "# plantName = 'IN.INTE.KPLS'\n", "startDate = '2025-05-01'\n", "endDate = '2025-07-13'"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['WS', 'Inverter', 'SMB-24', 'MFM', 'Plant']\n"]}], "source": ["categories = None\n", "deviceDict = None\n", "parameterDict = None\n", "try:\n", "    categories, deviceDict, parameterDict = m.getPlantInfo(plantName)\n", "    print(categories)\n", "except Exception as e:\n", "    print(f'Error PlantInfo: {str(e)}')"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\<PERSON><PERSON><PERSON><PERSON><PERSON>\\Wind_solar\\integration_utilities.py:308: FutureWarning: Passing literal json to 'read_json' is deprecated and will be removed in a future version. To read from a literal string, wrap it in a 'StringIO' object.\n", "  dataResp = pd.read_json(resp, orient=\"split\")\n"]}], "source": ["plantName = 'IN.INTE.KIDS'\n", "params = [\n", "    'POA Irradiance',\n", "    'Module Temperature',\n", "    'Inverter Temperature',\n", "    'PV Voltage',\n", "    'PV Current',\n", "    'DC Current 1', 'DC Voltage 1',\n", "    'DC Current 2', 'DC Voltage 2',\n", "    'AC Voltage',\n", "    'AC Current',\n", "    'Power Factor',\n", "    'Heatsink Temperature',\n", "    'Specific Power',\n", "    'CUF'\n", "]\n", "\n", "\n", "category = ['Inverter']\n", "\n", "wsDf = m.fetchDataV2(plantName, category, params, None, '2024-12-01', '2025-07-22')\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["wsDf.to_csv(\"KIDS CLINIC FOR KISHOR.csv\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[6], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m invAcPwrDf \u001b[38;5;241m=\u001b[39m \u001b[43mm\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfetchDataV2\u001b[49m\u001b[43m(\u001b[49m\u001b[43mplantName\u001b[49m\u001b[43m,\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mInverter\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mDaily Energy\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[38;5;28;43;01m<PERSON><PERSON>\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstartDate\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mendDate\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m      2\u001b[0m invAcPwrDf\n", "File \u001b[1;32md:\\<PERSON><PERSON><PERSON><PERSON><PERSON>\\Wind_solar\\integration_utilities.py:643\u001b[0m, in \u001b[0;36mPrescintoIntegrationUtilities.fetchDataV2\u001b[1;34m(self, pName, catList, paramList, deviceList, sDate, eDate, granularity, condition, quality, fetchInUserTimeZone, timeout)\u001b[0m\n\u001b[0;32m    598\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mfetchDataV2\u001b[39m(\n\u001b[0;32m    599\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m    600\u001b[0m     pName,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    610\u001b[0m     timeout\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[0;32m    611\u001b[0m ):\n\u001b[0;32m    612\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"        \u001b[39;00m\n\u001b[0;32m    613\u001b[0m \u001b[38;5;124;03m    Start Date and End Date Format Should be: 'YYYY-MM-DD' or 'YYYY-MM-DD HH:MM:SS' or 'YYYY-MM-DD HH:MM:SS±HH:MM'.\u001b[39;00m\n\u001b[0;32m    614\u001b[0m \u001b[38;5;124;03m    Here ±HH:MM represents timezone, Always it will start with - or +\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    640\u001b[0m \n\u001b[0;32m    641\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m--> 643\u001b[0m     dataDf \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m__getDataV2\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    644\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpName\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    645\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcatList\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    646\u001b[0m \u001b[43m        \u001b[49m\u001b[43mparamList\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    647\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdeviceList\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    648\u001b[0m \u001b[43m        \u001b[49m\u001b[43msDate\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    649\u001b[0m \u001b[43m        \u001b[49m\u001b[43meDate\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    650\u001b[0m \u001b[43m        \u001b[49m\u001b[43mgranularity\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    651\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcondition\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    652\u001b[0m \u001b[43m        \u001b[49m\u001b[43mquality\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    653\u001b[0m \u001b[43m        \u001b[49m\u001b[43mfetchInUserTimeZone\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    654\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    655\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    656\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m dataDf\n", "File \u001b[1;32md:\\<PERSON><PERSON><PERSON><PERSON><PERSON>\\Wind_solar\\integration_utilities.py:297\u001b[0m, in \u001b[0;36mPrescintoIntegrationUtilities.__getDataV2\u001b[1;34m(self, pName, catList, paramList, deviceList, sDate, eDate, granularity, condition, quality, fetchInUserTimeZone, timeout)\u001b[0m\n\u001b[0;32m    284\u001b[0m payload \u001b[38;5;241m=\u001b[39m {\n\u001b[0;32m    285\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpName\u001b[39m\u001b[38;5;124m\"\u001b[39m: pName,\n\u001b[0;32m    286\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcategoryList\u001b[39m\u001b[38;5;124m\"\u001b[39m: catList,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    294\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124misUserTimeZone\u001b[39m\u001b[38;5;124m\"\u001b[39m: fetchInUserTimeZone,\n\u001b[0;32m    295\u001b[0m }\n\u001b[0;32m    296\u001b[0m timeout \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m__getTimeOut(timeout)\n\u001b[1;32m--> 297\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[43mrequests\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpost\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    298\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m__URL_GET_DATA_V2\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    299\u001b[0m \u001b[43m    \u001b[49m\u001b[43mjson\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mpayload\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    300\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m__header\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    301\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    302\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    303\u001b[0m \u001b[38;5;66;03m# extracting data in json format\u001b[39;00m\n\u001b[0;32m    304\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m response\u001b[38;5;241m.\u001b[39mstatus_code \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m200\u001b[39m:\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\requests\\api.py:115\u001b[0m, in \u001b[0;36mpost\u001b[1;34m(url, data, json, **kwargs)\u001b[0m\n\u001b[0;32m    103\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mpost\u001b[39m(url, data\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, json\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[0;32m    104\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124mr\u001b[39m\u001b[38;5;124;03m\"\"\"Sends a POST request.\u001b[39;00m\n\u001b[0;32m    105\u001b[0m \n\u001b[0;32m    106\u001b[0m \u001b[38;5;124;03m    :param url: URL for the new :class:`Request` object.\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    112\u001b[0m \u001b[38;5;124;03m    :rtype: requests.Response\u001b[39;00m\n\u001b[0;32m    113\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m--> 115\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mpost\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdata\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mjson\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mjson\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\requests\\api.py:59\u001b[0m, in \u001b[0;36mrequest\u001b[1;34m(method, url, **kwargs)\u001b[0m\n\u001b[0;32m     55\u001b[0m \u001b[38;5;66;03m# By using the 'with' statement we are sure the session is closed, thus we\u001b[39;00m\n\u001b[0;32m     56\u001b[0m \u001b[38;5;66;03m# avoid leaving sockets open which can trigger a ResourceWarning in some\u001b[39;00m\n\u001b[0;32m     57\u001b[0m \u001b[38;5;66;03m# cases, and look like a memory leak in others.\u001b[39;00m\n\u001b[0;32m     58\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m sessions\u001b[38;5;241m.\u001b[39mSession() \u001b[38;5;28;01mas\u001b[39;00m session:\n\u001b[1;32m---> 59\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43msession\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\requests\\sessions.py:589\u001b[0m, in \u001b[0;36mSession.request\u001b[1;34m(self, method, url, params, data, headers, cookies, files, auth, timeout, allow_redirects, proxies, hooks, stream, verify, cert, json)\u001b[0m\n\u001b[0;32m    584\u001b[0m send_kwargs \u001b[38;5;241m=\u001b[39m {\n\u001b[0;32m    585\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtimeout\u001b[39m\u001b[38;5;124m\"\u001b[39m: timeout,\n\u001b[0;32m    586\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mallow_redirects\u001b[39m\u001b[38;5;124m\"\u001b[39m: allow_redirects,\n\u001b[0;32m    587\u001b[0m }\n\u001b[0;32m    588\u001b[0m send_kwargs\u001b[38;5;241m.\u001b[39mupdate(settings)\n\u001b[1;32m--> 589\u001b[0m resp \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprep\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43msend_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    591\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m resp\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\requests\\sessions.py:746\u001b[0m, in \u001b[0;36mSession.send\u001b[1;34m(self, request, **kwargs)\u001b[0m\n\u001b[0;32m    743\u001b[0m         \u001b[38;5;28;01mpass\u001b[39;00m\n\u001b[0;32m    745\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m stream:\n\u001b[1;32m--> 746\u001b[0m     \u001b[43mr\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcontent\u001b[49m\n\u001b[0;32m    748\u001b[0m \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m r\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\requests\\models.py:902\u001b[0m, in \u001b[0;36mResponse.content\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    900\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_content \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m    901\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m--> 902\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_content \u001b[38;5;241m=\u001b[39m \u001b[38;5;124;43mb\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mjoin\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43miter_content\u001b[49m\u001b[43m(\u001b[49m\u001b[43mCONTENT_CHUNK_SIZE\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    904\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_content_consumed \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[0;32m    905\u001b[0m \u001b[38;5;66;03m# don't need to release the connection; that's been handled by urllib3\u001b[39;00m\n\u001b[0;32m    906\u001b[0m \u001b[38;5;66;03m# since we exhausted the data.\u001b[39;00m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\requests\\models.py:820\u001b[0m, in \u001b[0;36mResponse.iter_content.<locals>.generate\u001b[1;34m()\u001b[0m\n\u001b[0;32m    818\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mraw, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstream\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n\u001b[0;32m    819\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 820\u001b[0m         \u001b[38;5;28;01<PERSON><PERSON> from\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mraw\u001b[38;5;241m.\u001b[39mstream(chunk_size, decode_content\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[0;32m    821\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m ProtocolError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m    822\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m ChunkedEncodingError(e)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\urllib3\\response.py:1066\u001b[0m, in \u001b[0;36mHTTPResponse.stream\u001b[1;34m(self, amt, decode_content)\u001b[0m\n\u001b[0;32m   1064\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m   1065\u001b[0m     \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m is_fp_closed(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_fp) \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_decoded_buffer) \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m-> 1066\u001b[0m         data \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[43mamt\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mamt\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdecode_content\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1068\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m data:\n\u001b[0;32m   1069\u001b[0m             \u001b[38;5;28;01myield\u001b[39;00m data\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\urllib3\\response.py:955\u001b[0m, in \u001b[0;36mHTTPResponse.read\u001b[1;34m(self, amt, decode_content, cache_content)\u001b[0m\n\u001b[0;32m    952\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_decoded_buffer) \u001b[38;5;241m>\u001b[39m\u001b[38;5;241m=\u001b[39m amt:\n\u001b[0;32m    953\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_decoded_buffer\u001b[38;5;241m.\u001b[39mget(amt)\n\u001b[1;32m--> 955\u001b[0m data \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_raw_read\u001b[49m\u001b[43m(\u001b[49m\u001b[43mamt\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    957\u001b[0m flush_decoder \u001b[38;5;241m=\u001b[39m amt \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mor\u001b[39;00m (amt \u001b[38;5;241m!=\u001b[39m \u001b[38;5;241m0\u001b[39m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m data)\n\u001b[0;32m    959\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m data \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_decoded_buffer) \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m:\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\urllib3\\response.py:879\u001b[0m, in \u001b[0;36mHTTPResponse._raw_read\u001b[1;34m(self, amt, read1)\u001b[0m\n\u001b[0;32m    876\u001b[0m fp_closed \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mgetattr\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_fp, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mclosed\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mFalse\u001b[39;00m)\n\u001b[0;32m    878\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_error_catcher():\n\u001b[1;32m--> 879\u001b[0m     data \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_fp_read\u001b[49m\u001b[43m(\u001b[49m\u001b[43mamt\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mread1\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mread1\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m fp_closed \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    880\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m amt \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m amt \u001b[38;5;241m!=\u001b[39m \u001b[38;5;241m0\u001b[39m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m data:\n\u001b[0;32m    881\u001b[0m         \u001b[38;5;66;03m# Platform-specific: Buggy versions of Python.\u001b[39;00m\n\u001b[0;32m    882\u001b[0m         \u001b[38;5;66;03m# Close the connection when no data is returned\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    887\u001b[0m         \u001b[38;5;66;03m# not properly close the connection in all cases. There is\u001b[39;00m\n\u001b[0;32m    888\u001b[0m         \u001b[38;5;66;03m# no harm in redundantly calling close.\u001b[39;00m\n\u001b[0;32m    889\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_fp\u001b[38;5;241m.\u001b[39mclose()\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\urllib3\\response.py:862\u001b[0m, in \u001b[0;36mHTTPResponse._fp_read\u001b[1;34m(self, amt, read1)\u001b[0m\n\u001b[0;32m    859\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_fp\u001b[38;5;241m.\u001b[39mread1(amt) \u001b[38;5;28;01mif\u001b[39;00m amt \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_fp\u001b[38;5;241m.\u001b[39mread1()\n\u001b[0;32m    860\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m:\n\u001b[0;32m    861\u001b[0m     \u001b[38;5;66;03m# StringIO doesn't like amt=None\u001b[39;00m\n\u001b[1;32m--> 862\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_fp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[43mamt\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mif\u001b[39;00m amt \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_fp\u001b[38;5;241m.\u001b[39mread()\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\http\\client.py:479\u001b[0m, in \u001b[0;36mHTTPResponse.read\u001b[1;34m(self, amt)\u001b[0m\n\u001b[0;32m    476\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlength \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m amt \u001b[38;5;241m>\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlength:\n\u001b[0;32m    477\u001b[0m     \u001b[38;5;66;03m# clip the read to the \"end of response\"\u001b[39;00m\n\u001b[0;32m    478\u001b[0m     amt \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlength\n\u001b[1;32m--> 479\u001b[0m s \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[43mamt\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    480\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m s \u001b[38;5;129;01mand\u001b[39;00m amt:\n\u001b[0;32m    481\u001b[0m     \u001b[38;5;66;03m# Ideally, we would raise IncompleteRead if the content-length\u001b[39;00m\n\u001b[0;32m    482\u001b[0m     \u001b[38;5;66;03m# wasn't satisfied, but it might break compatibility.\u001b[39;00m\n\u001b[0;32m    483\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_close_conn()\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py:720\u001b[0m, in \u001b[0;36mSocketIO.readinto\u001b[1;34m(self, b)\u001b[0m\n\u001b[0;32m    718\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[0;32m    719\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 720\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_sock\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrecv_into\u001b[49m\u001b[43m(\u001b[49m\u001b[43mb\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    721\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m timeout:\n\u001b[0;32m    722\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_timeout_occurred \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n", "\u001b[1;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["invAcPwrDf = m.fetchDataV2(plantName,['Inverter'],['Daily Energy'],None, startDate, endDate)\n", "invAcPwrDf"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\<PERSON><PERSON><PERSON><PERSON><PERSON>\\Wind_solar\\integration_utilities.py:308: FutureWarning: Passing literal json to 'read_json' is deprecated and will be removed in a future version. To read from a literal string, wrap it in a 'StringIO' object.\n", "  dataResp = pd.read_json(resp, orient=\"split\")\n"]}], "source": ["category = ['Inverter']\n", "params = ['Daily Energy']\n", "condition_generation = {\"Daily Energy\": \"last\"}\n", "dgr = m.fetchDataV2(plantName,category,params,None, startDate, endDate, granularity = '15m', condition=condition_generation)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>time</th>\n", "      <th>C9_INV_1.Daily Energy</th>\n", "      <th>C9_INV_10.Daily Energy</th>\n", "      <th>C9_INV_11.Daily Energy</th>\n", "      <th>C9_INV_12.Daily Energy</th>\n", "      <th>C9_INV_13.Daily Energy</th>\n", "      <th>C9_INV_14.Daily Energy</th>\n", "      <th>C9_INV_15.Daily Energy</th>\n", "      <th>C9_INV_16.Daily Energy</th>\n", "      <th>C9_INV_2.Daily Energy</th>\n", "      <th>C9_INV_3.Daily Energy</th>\n", "      <th>C9_INV_4.Daily Energy</th>\n", "      <th>C9_INV_5.Daily Energy</th>\n", "      <th>C9_INV_6.Daily Energy</th>\n", "      <th>C9_INV_7.Daily Energy</th>\n", "      <th>C9_INV_8.Daily Energy</th>\n", "      <th>C9_INV_9.Daily Energy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-05-01T00:00:00Z+05:30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-05-01T00:15:00Z+05:30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-05-01T00:30:00Z+05:30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-05-01T00:45:00Z+05:30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-05-01T01:00:00Z+05:30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7099</th>\n", "      <td>2025-07-13T22:45:00Z+05:30</td>\n", "      <td>1516.3</td>\n", "      <td>1452.9</td>\n", "      <td>1426.1</td>\n", "      <td>1429.6</td>\n", "      <td>1452.6</td>\n", "      <td>1427.9</td>\n", "      <td>1459.0</td>\n", "      <td>1523.3</td>\n", "      <td>1389.0</td>\n", "      <td>1518.4</td>\n", "      <td>1518.2</td>\n", "      <td>1511.0</td>\n", "      <td>1525.1</td>\n", "      <td>1448.8</td>\n", "      <td>1443.3</td>\n", "      <td>1436.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7100</th>\n", "      <td>2025-07-13T23:00:00Z+05:30</td>\n", "      <td>1516.3</td>\n", "      <td>1452.9</td>\n", "      <td>1426.1</td>\n", "      <td>1429.6</td>\n", "      <td>1452.6</td>\n", "      <td>1427.9</td>\n", "      <td>1459.0</td>\n", "      <td>1523.3</td>\n", "      <td>1389.0</td>\n", "      <td>1518.4</td>\n", "      <td>1518.2</td>\n", "      <td>1511.0</td>\n", "      <td>1525.1</td>\n", "      <td>1448.8</td>\n", "      <td>1443.3</td>\n", "      <td>1436.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7101</th>\n", "      <td>2025-07-13T23:15:00Z+05:30</td>\n", "      <td>1516.3</td>\n", "      <td>1452.9</td>\n", "      <td>1426.1</td>\n", "      <td>1429.6</td>\n", "      <td>1452.6</td>\n", "      <td>1427.9</td>\n", "      <td>1459.0</td>\n", "      <td>1523.3</td>\n", "      <td>1389.0</td>\n", "      <td>1518.4</td>\n", "      <td>1518.2</td>\n", "      <td>1511.0</td>\n", "      <td>1525.1</td>\n", "      <td>1448.8</td>\n", "      <td>1443.3</td>\n", "      <td>1436.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7102</th>\n", "      <td>2025-07-13T23:30:00Z+05:30</td>\n", "      <td>1516.3</td>\n", "      <td>1452.9</td>\n", "      <td>1426.1</td>\n", "      <td>1429.6</td>\n", "      <td>1452.6</td>\n", "      <td>1427.9</td>\n", "      <td>1459.0</td>\n", "      <td>1523.3</td>\n", "      <td>1389.0</td>\n", "      <td>1518.4</td>\n", "      <td>1518.2</td>\n", "      <td>1511.0</td>\n", "      <td>1525.1</td>\n", "      <td>1448.8</td>\n", "      <td>1443.3</td>\n", "      <td>1436.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7103</th>\n", "      <td>2025-07-13T23:45:00Z+05:30</td>\n", "      <td>1516.3</td>\n", "      <td>0.0</td>\n", "      <td>1426.1</td>\n", "      <td>1429.6</td>\n", "      <td>0.0</td>\n", "      <td>1427.9</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1389.0</td>\n", "      <td>1518.4</td>\n", "      <td>0.0</td>\n", "      <td>1511.0</td>\n", "      <td>0.0</td>\n", "      <td>1448.8</td>\n", "      <td>1443.3</td>\n", "      <td>1436.2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>7104 rows × 17 columns</p>\n", "</div>"], "text/plain": ["                            time  C9_INV_1.Daily Energy  \\\n", "0     2025-05-01T00:00:00Z+05:30                    0.0   \n", "1     2025-05-01T00:15:00Z+05:30                    0.0   \n", "2     2025-05-01T00:30:00Z+05:30                    0.0   \n", "3     2025-05-01T00:45:00Z+05:30                    0.0   \n", "4     2025-05-01T01:00:00Z+05:30                    0.0   \n", "...                          ...                    ...   \n", "7099  2025-07-13T22:45:00Z+05:30                 1516.3   \n", "7100  2025-07-13T23:00:00Z+05:30                 1516.3   \n", "7101  2025-07-13T23:15:00Z+05:30                 1516.3   \n", "7102  2025-07-13T23:30:00Z+05:30                 1516.3   \n", "7103  2025-07-13T23:45:00Z+05:30                 1516.3   \n", "\n", "      C9_INV_10.Daily Energy  C9_INV_11.Daily Energy  C9_INV_12.Daily Energy  \\\n", "0                        0.0                     0.0                     0.0   \n", "1                        0.0                     0.0                     0.0   \n", "2                        0.0                     0.0                     0.0   \n", "3                        0.0                     0.0                     0.0   \n", "4                        0.0                     0.0                     0.0   \n", "...                      ...                     ...                     ...   \n", "7099                  1452.9                  1426.1                  1429.6   \n", "7100                  1452.9                  1426.1                  1429.6   \n", "7101                  1452.9                  1426.1                  1429.6   \n", "7102                  1452.9                  1426.1                  1429.6   \n", "7103                     0.0                  1426.1                  1429.6   \n", "\n", "      C9_INV_13.Daily Energy  C9_INV_14.Daily Energy  C9_INV_15.Daily Energy  \\\n", "0                        0.0                     0.0                     0.0   \n", "1                        0.0                     0.0                     0.0   \n", "2                        0.0                     0.0                     0.0   \n", "3                        0.0                     0.0                     0.0   \n", "4                        0.0                     0.0                     0.0   \n", "...                      ...                     ...                     ...   \n", "7099                  1452.6                  1427.9                  1459.0   \n", "7100                  1452.6                  1427.9                  1459.0   \n", "7101                  1452.6                  1427.9                  1459.0   \n", "7102                  1452.6                  1427.9                  1459.0   \n", "7103                     0.0                  1427.9                     0.0   \n", "\n", "      C9_INV_16.Daily Energy  C9_INV_2.Daily Energy  C9_INV_3.Daily Energy  \\\n", "0                        0.0                    0.0                    0.0   \n", "1                        0.0                    0.0                    0.0   \n", "2                        0.0                    0.0                    0.0   \n", "3                        0.0                    0.0                    0.0   \n", "4                        0.0                    0.0                    0.0   \n", "...                      ...                    ...                    ...   \n", "7099                  1523.3                 1389.0                 1518.4   \n", "7100                  1523.3                 1389.0                 1518.4   \n", "7101                  1523.3                 1389.0                 1518.4   \n", "7102                  1523.3                 1389.0                 1518.4   \n", "7103                     0.0                 1389.0                 1518.4   \n", "\n", "      C9_INV_4.Daily Energy  C9_INV_5.Daily Energy  C9_INV_6.Daily Energy  \\\n", "0                       0.0                    0.0                    0.0   \n", "1                       0.0                    0.0                    0.0   \n", "2                       0.0                    0.0                    0.0   \n", "3                       0.0                    0.0                    0.0   \n", "4                       0.0                    0.0                    0.0   \n", "...                     ...                    ...                    ...   \n", "7099                 1518.2                 1511.0                 1525.1   \n", "7100                 1518.2                 1511.0                 1525.1   \n", "7101                 1518.2                 1511.0                 1525.1   \n", "7102                 1518.2                 1511.0                 1525.1   \n", "7103                    0.0                 1511.0                    0.0   \n", "\n", "      C9_INV_7.Daily Energy  C9_INV_8.Daily Energy  C9_INV_9.Daily Energy  \n", "0                       0.0                    0.0                    0.0  \n", "1                       0.0                    0.0                    0.0  \n", "2                       0.0                    0.0                    0.0  \n", "3                       0.0                    0.0                    0.0  \n", "4                       0.0                    0.0                    0.0  \n", "...                     ...                    ...                    ...  \n", "7099                 1448.8                 1443.3                 1436.2  \n", "7100                 1448.8                 1443.3                 1436.2  \n", "7101                 1448.8                 1443.3                 1436.2  \n", "7102                 1448.8                 1443.3                 1436.2  \n", "7103                 1448.8                 1443.3                 1436.2  \n", "\n", "[7104 rows x 17 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["dgr"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["dgr.to_csv(\"KIDS 05-01 TO 07-13.csv\")\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "df = pd.DataFrame(dgr)\n", "df.to_csv(f'KPLS SOLAR.csv', index=False)"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['time', 'Plant.PR'], dtype='object')"]}, "execution_count": 70, "metadata": {}, "output_type": "execute_result"}], "source": ["dgr.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\<PERSON><PERSON><PERSON><PERSON><PERSON>\\Wind_solar\\integration_utilities.py:308: FutureWarning: Passing literal json to 'read_json' is deprecated and will be removed in a future version. To read from a literal string, wrap it in a 'StringIO' object.\n", "  dataResp = pd.read_json(resp, orient=\"split\")\n"]}], "source": ["plantName = 'IN.INTE.KPLS'\n", "startDate = '2023-01-01'\n", "endDate = '2025-05-05'\n", "\n", "category = ['Inverter']\n", "params = ['Daily Energy']\n", "condition_generation = {\"Daily Energy\": \"last\"}\n", "poa = m.fetchDataV2(plantName,category,params,None, startDate, endDate, granularity = '15m', condition=condition_generation)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>time</th>\n", "      <th>INV-01.Active Power</th>\n", "      <th>INV-02.Active Power</th>\n", "      <th>INV-03.Active Power</th>\n", "      <th>INV-04.Active Power</th>\n", "      <th>INV-05.Active Power</th>\n", "      <th>INV-06.Active Power</th>\n", "      <th>INV-07.Active Power</th>\n", "      <th>INV-08.Active Power</th>\n", "      <th>INV-09.Active Power</th>\n", "      <th>INV-10.Active Power</th>\n", "      <th>INV-11.Active Power</th>\n", "      <th>INV-12.Active Power</th>\n", "      <th>INV-13.Active Power</th>\n", "      <th>INV-14.Active Power</th>\n", "      <th>INV-15.Active Power</th>\n", "      <th>INV-16.Active Power</th>\n", "      <th>INV-17.Active Power</th>\n", "      <th>INV-18.Active Power</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-01-01T00:00:00Z+05:30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-01-01T00:15:00Z+05:30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-01-01T00:30:00Z+05:30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-01-01T00:45:00Z+05:30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-01-01T01:00:00Z+05:30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82171</th>\n", "      <td>2025-05-05T22:45:00Z+05:30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.02</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82172</th>\n", "      <td>2025-05-05T23:00:00Z+05:30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.02</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82173</th>\n", "      <td>2025-05-05T23:15:00Z+05:30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.02</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82174</th>\n", "      <td>2025-05-05T23:30:00Z+05:30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.04</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82175</th>\n", "      <td>2025-05-05T23:45:00Z+05:30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>82176 rows × 19 columns</p>\n", "</div>"], "text/plain": ["                             time  INV-01.Active Power  INV-02.Active Power  \\\n", "0      2023-01-01T00:00:00Z+05:30                  0.0                  0.0   \n", "1      2023-01-01T00:15:00Z+05:30                  0.0                  0.0   \n", "2      2023-01-01T00:30:00Z+05:30                  0.0                  0.0   \n", "3      2023-01-01T00:45:00Z+05:30                  0.0                  0.0   \n", "4      2023-01-01T01:00:00Z+05:30                  0.0                  0.0   \n", "...                           ...                  ...                  ...   \n", "82171  2025-05-05T22:45:00Z+05:30                  0.0                  0.0   \n", "82172  2025-05-05T23:00:00Z+05:30                  0.0                  0.0   \n", "82173  2025-05-05T23:15:00Z+05:30                  0.0                  0.0   \n", "82174  2025-05-05T23:30:00Z+05:30                  0.0                  0.0   \n", "82175  2025-05-05T23:45:00Z+05:30                  0.0                  0.0   \n", "\n", "       INV-03.Active Power  INV-04.Active Power  INV-05.Active Power  \\\n", "0                      0.0                  0.0                  0.0   \n", "1                      0.0                  0.0                  0.0   \n", "2                      0.0                  0.0                  0.0   \n", "3                      0.0                  0.0                  0.0   \n", "4                      0.0                  0.0                  0.0   \n", "...                    ...                  ...                  ...   \n", "82171                  0.0                  0.0                  0.0   \n", "82172                  0.0                  0.0                  0.0   \n", "82173                  0.0                  0.0                  0.0   \n", "82174                  0.0                  0.0                  0.0   \n", "82175                  0.0                  0.0                  0.0   \n", "\n", "       INV-06.Active Power  INV-07.Active Power  INV-08.Active Power  \\\n", "0                      0.0                  0.0                  0.0   \n", "1                      0.0                  0.0                  0.0   \n", "2                      0.0                  0.0                  0.0   \n", "3                      0.0                  0.0                  0.0   \n", "4                      0.0                  0.0                  0.0   \n", "...                    ...                  ...                  ...   \n", "82171                  0.0                  0.0                  0.0   \n", "82172                  0.0                  0.0                  0.0   \n", "82173                  0.0                  0.0                  0.0   \n", "82174                  0.0                  0.0                  0.0   \n", "82175                  0.0                  0.0                  0.0   \n", "\n", "       INV-09.Active Power  INV-10.Active Power  INV-11.Active Power  \\\n", "0                      0.0                  0.0                  0.0   \n", "1                      0.0                  0.0                  0.0   \n", "2                      0.0                  0.0                  0.0   \n", "3                      0.0                  0.0                  0.0   \n", "4                      0.0                  0.0                  0.0   \n", "...                    ...                  ...                  ...   \n", "82171                  0.0                  0.0                  0.0   \n", "82172                  0.0                  0.0                  0.0   \n", "82173                  0.0                  0.0                  0.0   \n", "82174                  0.0                  0.0                  0.0   \n", "82175                  0.0                  0.0                  0.0   \n", "\n", "       INV-12.Active Power  INV-13.Active Power  INV-14.Active Power  \\\n", "0                      0.0                  0.0                 0.00   \n", "1                      0.0                  0.0                 0.00   \n", "2                      0.0                  0.0                 0.00   \n", "3                      0.0                  0.0                 0.00   \n", "4                      0.0                  0.0                 0.00   \n", "...                    ...                  ...                  ...   \n", "82171                  0.0                  0.0                 0.02   \n", "82172                  0.0                  0.0                 0.02   \n", "82173                  0.0                  0.0                 0.02   \n", "82174                  0.0                  0.0                 0.04   \n", "82175                  0.0                  0.0                 0.00   \n", "\n", "       INV-15.Active Power  INV-16.Active Power  INV-17.Active Power  \\\n", "0                      0.0                  0.0                  0.0   \n", "1                      0.0                  0.0                  0.0   \n", "2                      0.0                  0.0                  0.0   \n", "3                      0.0                  0.0                  0.0   \n", "4                      0.0                  0.0                  0.0   \n", "...                    ...                  ...                  ...   \n", "82171                  0.0                  0.0                  0.0   \n", "82172                  0.0                  0.0                  0.0   \n", "82173                  0.0                  0.0                  0.0   \n", "82174                  0.0                  0.0                  0.0   \n", "82175                  0.0                  0.0                  0.0   \n", "\n", "       INV-18.Active Power  \n", "0                      0.0  \n", "1                      0.0  \n", "2                      0.0  \n", "3                      0.0  \n", "4                      0.0  \n", "...                    ...  \n", "82171                  0.0  \n", "82172                  0.0  \n", "82173                  0.0  \n", "82174                  0.0  \n", "82175                  0.0  \n", "\n", "[82176 rows x 19 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["poa"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "df = pd.DataFrame(poa)\n", "df.to_csv('KPL Active Power.csv', index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# POA"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["category = ['plant']\n", "params = ['Daily POA Energy']\n", "POA = m.fetchDataV2(plantName,category,params,None, startDate, endDate, granularity = '1d')"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Devices not found in given plant!'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["POA"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# PR %"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["category = ['plant']\n", "params = ['PR']\n", "PR = m.fetchDataV2(plantName,category,params,None, startDate, endDate, granularity = '1d')"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Devices not found in given plant!'"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["PR"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# DG"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\<PERSON><PERSON><PERSON><PERSON><PERSON>\\Wind_solar\\IntegrationUtilities.py:308: FutureWarning: Passing literal json to 'read_json' is deprecated and will be removed in a future version. To read from a literal string, wrap it in a 'StringIO' object.\n", "  dataResp = pd.read_json(resp, orient=\"split\")\n"]}], "source": ["category = ['Inverter']\n", "params = ['Daily Energy']\n", "dg = m.fetchDataV2(plantName,category,params,None, startDate, endDate, granularity = '1d')"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>time</th>\n", "      <th>INV01.Daily Energy</th>\n", "      <th>INV02.Daily Energy</th>\n", "      <th>INV03.Daily Energy</th>\n", "      <th>INV04.Daily Energy</th>\n", "      <th>INV05.Daily Energy</th>\n", "      <th>INV06.Daily Energy</th>\n", "      <th>INV07.Daily Energy</th>\n", "      <th>INV08.Daily Energy</th>\n", "      <th>INV09.Daily Energy</th>\n", "      <th>...</th>\n", "      <th>INV17.Daily Energy</th>\n", "      <th>INV18.Daily Energy</th>\n", "      <th>INV19.Daily Energy</th>\n", "      <th>INV20.Daily Energy</th>\n", "      <th>INV21.Daily Energy</th>\n", "      <th>INV22.Daily Energy</th>\n", "      <th>INV23.Daily Energy</th>\n", "      <th>INV24.Daily Energy</th>\n", "      <th>INV25.Daily Energy</th>\n", "      <th>INV26.Daily Energy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-01-01T00:00:00Z+05:30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-01-02T00:00:00Z+05:30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-01-03T00:00:00Z+05:30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-01-04T00:00:00Z+05:30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-01-05T00:00:00Z+05:30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>56</th>\n", "      <td>2025-02-26T00:00:00Z+05:30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57</th>\n", "      <td>2025-02-27T00:00:00Z+05:30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>58</th>\n", "      <td>2025-02-28T00:00:00Z+05:30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>344.1</td>\n", "      <td>304.9</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>59</th>\n", "      <td>2025-03-01T00:00:00Z+05:30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>60</th>\n", "      <td>2025-03-02T00:00:00Z+05:30</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>61 rows × 27 columns</p>\n", "</div>"], "text/plain": ["                          time  INV01.Daily Energy  INV02.Daily Energy  \\\n", "0   2025-01-01T00:00:00Z+05:30                 0.0                 0.0   \n", "1   2025-01-02T00:00:00Z+05:30                 0.0                 0.0   \n", "2   2025-01-03T00:00:00Z+05:30                 0.0                 0.0   \n", "3   2025-01-04T00:00:00Z+05:30                 0.0                 0.0   \n", "4   2025-01-05T00:00:00Z+05:30                 0.0                 0.0   \n", "..                         ...                 ...                 ...   \n", "56  2025-02-26T00:00:00Z+05:30                 0.0                 0.0   \n", "57  2025-02-27T00:00:00Z+05:30                 0.0                 0.0   \n", "58  2025-02-28T00:00:00Z+05:30                 0.0                 0.0   \n", "59  2025-03-01T00:00:00Z+05:30                 0.0                 0.0   \n", "60  2025-03-02T00:00:00Z+05:30                 0.0                 0.0   \n", "\n", "    INV03.Daily Energy  INV04.Daily Energy  INV05.Daily Energy  \\\n", "0                    0                 0.0                 0.0   \n", "1                    0                 0.0                 0.0   \n", "2                    0                 0.0                 0.0   \n", "3                    0                 0.0                 0.0   \n", "4                    0                 0.0                 0.0   \n", "..                 ...                 ...                 ...   \n", "56                   0                 0.0                 0.0   \n", "57                   0                 0.0                 0.0   \n", "58                   0               344.1               304.9   \n", "59                   0                 0.0                 0.0   \n", "60                   0                 0.0                 0.0   \n", "\n", "    INV06.Daily Energy  INV07.Daily Energy  INV08.Daily Energy  \\\n", "0                  0.0                 0.0                 0.0   \n", "1                  0.0                 0.0                 0.0   \n", "2                  0.0                 0.0                 0.0   \n", "3                  0.0                 0.0                 0.0   \n", "4                  0.0                 0.0                 0.0   \n", "..                 ...                 ...                 ...   \n", "56                 0.0                 0.0                 0.0   \n", "57                 0.0                 0.0                 0.0   \n", "58                 0.0                 0.0                 0.0   \n", "59                 0.0                 0.0                 0.0   \n", "60                 0.0                 0.0                 0.0   \n", "\n", "    INV09.Daily Energy  ...  INV17.Daily Energy  INV18.Daily Energy  \\\n", "0                  0.0  ...                 0.0                 0.0   \n", "1                  0.0  ...                 0.0                 0.0   \n", "2                  0.0  ...                 0.0                 0.0   \n", "3                  0.0  ...                 0.0                 0.0   \n", "4                  0.0  ...                 0.0                 0.0   \n", "..                 ...  ...                 ...                 ...   \n", "56                 0.0  ...                 0.0                 0.0   \n", "57                 0.0  ...                 0.0                 0.0   \n", "58                 0.0  ...                 0.0                 0.0   \n", "59                 0.0  ...                 0.0                 0.0   \n", "60                 0.0  ...                 0.0                 0.0   \n", "\n", "    INV19.Daily Energy  INV20.Daily Energy  INV21.Daily Energy  \\\n", "0                  0.0                 0.0                 0.0   \n", "1                  0.0                 0.0                 0.0   \n", "2                  0.0                 0.0                 0.0   \n", "3                  0.0                 0.0                 0.0   \n", "4                  0.0                 0.0                 0.0   \n", "..                 ...                 ...                 ...   \n", "56                 0.0                 0.0                 0.0   \n", "57                 0.0                 0.0                 0.0   \n", "58                 0.0                 0.0                 0.0   \n", "59                 0.0                 0.0                 0.0   \n", "60                 0.0                 0.0                 0.0   \n", "\n", "    INV22.Daily Energy  INV23.Daily Energy  INV24.Daily Energy  \\\n", "0                  0.0                 0.0                 0.0   \n", "1                  0.0                 0.0                 0.0   \n", "2                  0.0                 0.0                 0.0   \n", "3                  0.0                 0.0                 0.0   \n", "4                  0.0                 0.0                 0.0   \n", "..                 ...                 ...                 ...   \n", "56                 0.0                 0.0                 0.0   \n", "57                 0.0                 0.0                 0.0   \n", "58                 0.0                 0.0                 0.0   \n", "59                 0.0                 0.0                 0.0   \n", "60                 0.0                 0.0                 0.0   \n", "\n", "    INV25.Daily Energy  INV26.Daily Energy  \n", "0                  0.0                 0.0  \n", "1                  0.0                 0.0  \n", "2                  0.0                 0.0  \n", "3                  0.0                 0.0  \n", "4                  0.0                 0.0  \n", "..                 ...                 ...  \n", "56                 0.0                 0.0  \n", "57                 0.0                 0.0  \n", "58                 0.0                 0.0  \n", "59                 0.0                 0.0  \n", "60                 0.0                 0.0  \n", "\n", "[61 rows x 27 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["dg"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\<PERSON><PERSON><PERSON><PERSON><PERSON>\\Wind_solar\\IntegrationUtilities.py:308: FutureWarning: Passing literal json to 'read_json' is deprecated and will be removed in a future version. To read from a literal string, wrap it in a 'StringIO' object.\n", "  dataResp = pd.read_json(resp, orient=\"split\")\n"]}], "source": ["plantName = 'IN.INTE.KSIP'\n", "startDate = '2022-02-10'\n", "endDate = '2025-03-25'\n", "category = ['Turbine']\n", "params = ['WTUR.Generation today']\n", "condition = {\"Generation today\": \"max\"}\n", "dg = m.fetchDataV2(plantName,category,params,None, startDate, endDate, granularity = '1d', condition= condition)"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>time</th>\n", "      <th>BBV19.Generation today</th>\n", "      <th>BBV20.Generation today</th>\n", "      <th>BBV24.Generation today</th>\n", "      <th>BBV26.Generation today</th>\n", "      <th>BBV44.Generation today</th>\n", "      <th>BBV47.Generation today</th>\n", "      <th>BBV56.Generation today</th>\n", "      <th>BBV57.Generation today</th>\n", "      <th>BBV58.Generation today</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2022-02-10T00:00:00Z+05:30</td>\n", "      <td>201.673471</td>\n", "      <td>323.765959</td>\n", "      <td>NaN</td>\n", "      <td>427.817406</td>\n", "      <td>118.106843</td>\n", "      <td>355.131421</td>\n", "      <td>NaN</td>\n", "      <td>2202.512370</td>\n", "      <td>1465.842938</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2022-02-11T00:00:00Z+05:30</td>\n", "      <td>316.043753</td>\n", "      <td>435.907615</td>\n", "      <td>NaN</td>\n", "      <td>689.506509</td>\n", "      <td>466.617745</td>\n", "      <td>505.755808</td>\n", "      <td>NaN</td>\n", "      <td>2963.316215</td>\n", "      <td>3233.059874</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2022-02-12T00:00:00Z+05:30</td>\n", "      <td>315.772183</td>\n", "      <td>437.357331</td>\n", "      <td>NaN</td>\n", "      <td>688.577315</td>\n", "      <td>466.189710</td>\n", "      <td>505.658168</td>\n", "      <td>NaN</td>\n", "      <td>2968.876439</td>\n", "      <td>3258.909852</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2022-02-13T00:00:00Z+05:30</td>\n", "      <td>250.503215</td>\n", "      <td>362.572834</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1439.886488</td>\n", "      <td>422.733755</td>\n", "      <td>NaN</td>\n", "      <td>3110.413612</td>\n", "      <td>3547.639228</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2022-02-14T00:00:00Z+05:30</td>\n", "      <td>373.297821</td>\n", "      <td>364.430749</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>1460.753299</td>\n", "      <td>428.439438</td>\n", "      <td>NaN</td>\n", "      <td>3544.490220</td>\n", "      <td>3547.639228</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1135</th>\n", "      <td>2025-03-21T00:00:00Z+05:30</td>\n", "      <td>775.193748</td>\n", "      <td>2720.541603</td>\n", "      <td>1411.800645</td>\n", "      <td>1300.247925</td>\n", "      <td>2634.776657</td>\n", "      <td>1683.813622</td>\n", "      <td>2364.359193</td>\n", "      <td>2476.257752</td>\n", "      <td>2002.650957</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1136</th>\n", "      <td>2025-03-22T00:00:00Z+05:30</td>\n", "      <td>167.658904</td>\n", "      <td>2222.865565</td>\n", "      <td>565.537034</td>\n", "      <td>1768.144374</td>\n", "      <td>2414.317355</td>\n", "      <td>1013.370798</td>\n", "      <td>1988.956715</td>\n", "      <td>2079.738103</td>\n", "      <td>2167.663472</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1137</th>\n", "      <td>2025-03-23T00:00:00Z+05:30</td>\n", "      <td>1817.264770</td>\n", "      <td>4299.859345</td>\n", "      <td>1830.735297</td>\n", "      <td>3534.188972</td>\n", "      <td>5615.779063</td>\n", "      <td>4702.543104</td>\n", "      <td>5137.765116</td>\n", "      <td>4968.890806</td>\n", "      <td>3305.955889</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1138</th>\n", "      <td>2025-03-24T00:00:00Z+05:30</td>\n", "      <td>1500.845660</td>\n", "      <td>4065.043054</td>\n", "      <td>2547.103306</td>\n", "      <td>1705.926442</td>\n", "      <td>5453.095818</td>\n", "      <td>4637.117715</td>\n", "      <td>1082.873548</td>\n", "      <td>3019.296050</td>\n", "      <td>3245.824593</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1139</th>\n", "      <td>2025-03-25T00:00:00Z+05:30</td>\n", "      <td>2301.968310</td>\n", "      <td>2664.423518</td>\n", "      <td>4319.488844</td>\n", "      <td>2938.441943</td>\n", "      <td>4529.819238</td>\n", "      <td>5071.875558</td>\n", "      <td>0.000000</td>\n", "      <td>5655.204876</td>\n", "      <td>5410.077398</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1140 rows × 10 columns</p>\n", "</div>"], "text/plain": ["                            time  BBV19.Generation today  \\\n", "0     2022-02-10T00:00:00Z+05:30              201.673471   \n", "1     2022-02-11T00:00:00Z+05:30              316.043753   \n", "2     2022-02-12T00:00:00Z+05:30              315.772183   \n", "3     2022-02-13T00:00:00Z+05:30              250.503215   \n", "4     2022-02-14T00:00:00Z+05:30              373.297821   \n", "...                          ...                     ...   \n", "1135  2025-03-21T00:00:00Z+05:30              775.193748   \n", "1136  2025-03-22T00:00:00Z+05:30              167.658904   \n", "1137  2025-03-23T00:00:00Z+05:30             1817.264770   \n", "1138  2025-03-24T00:00:00Z+05:30             1500.845660   \n", "1139  2025-03-25T00:00:00Z+05:30             2301.968310   \n", "\n", "      BBV20.Generation today  BBV24.Generation today  BBV26.Generation today  \\\n", "0                 323.765959                     NaN              427.817406   \n", "1                 435.907615                     NaN              689.506509   \n", "2                 437.357331                     NaN              688.577315   \n", "3                 362.572834                     NaN                     NaN   \n", "4                 364.430749                0.000000                     NaN   \n", "...                      ...                     ...                     ...   \n", "1135             2720.541603             1411.800645             1300.247925   \n", "1136             2222.865565              565.537034             1768.144374   \n", "1137             4299.859345             1830.735297             3534.188972   \n", "1138             4065.043054             2547.103306             1705.926442   \n", "1139             2664.423518             4319.488844             2938.441943   \n", "\n", "      BBV44.Generation today  BBV47.Generation today  BBV56.Generation today  \\\n", "0                 118.106843              355.131421                     NaN   \n", "1                 466.617745              505.755808                     NaN   \n", "2                 466.189710              505.658168                     NaN   \n", "3                1439.886488              422.733755                     NaN   \n", "4                1460.753299              428.439438                     NaN   \n", "...                      ...                     ...                     ...   \n", "1135             2634.776657             1683.813622             2364.359193   \n", "1136             2414.317355             1013.370798             1988.956715   \n", "1137             5615.779063             4702.543104             5137.765116   \n", "1138             5453.095818             4637.117715             1082.873548   \n", "1139             4529.819238             5071.875558                0.000000   \n", "\n", "      BBV57.Generation today  BBV58.Generation today  \n", "0                2202.512370             1465.842938  \n", "1                2963.316215             3233.059874  \n", "2                2968.876439             3258.909852  \n", "3                3110.413612             3547.639228  \n", "4                3544.490220             3547.639228  \n", "...                      ...                     ...  \n", "1135             2476.257752             2002.650957  \n", "1136             2079.738103             2167.663472  \n", "1137             4968.890806             3305.955889  \n", "1138             3019.296050             3245.824593  \n", "1139             5655.204876             5410.077398  \n", "\n", "[1140 rows x 10 columns]"]}, "execution_count": 74, "metadata": {}, "output_type": "execute_result"}], "source": ["dg"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSV files created: IN.INTE.KSIP_daily_data.csv and IN.INTE.KSIP_monthly_data.csv\n"]}], "source": ["### THIS IS FOR WINDs\n", "\n", "import pandas as pd\n", "\n", "def generate_csv_from_data(data, plantName):\n", "    # Create DataFrame\n", "    df = pd.DataFrame(data)\n", "    \n", "    # Convert time column to datetime and make it human-readable\n", "    df[\"time\"] = pd.to_datetime(df[\"time\"]).dt.strftime(\"%Y-%m-%d\")\n", "    \n", "    # Identify generation columns dynamically (excluding time column)\n", "    generation_columns = [col for col in df.columns if col != \"time\"]\n", "    \n", "    # Calculate daily generation\n", "    df[\"Daily Generation\"] = df[generation_columns].sum(axis=1)\n", "    # Extract month column\n", "    df[\"Month\"] = pd.to_datetime(df[\"time\"]).dt.to_period(\"M\")\n", "    \n", "    # Save daily data to CSV\n", "    df_daily = df[[\"time\", \"Daily Generation\", \"Month\"]]\n", "    df_daily.to_csv(f\"{plantName}_daily_data.csv\", index=False)\n", "    \n", "    # Group by month and sum values for monthly data\n", "    df_monthly = df.groupby(\"Month\")[\"Daily Generation\"].sum().reset_index()\n", "    df_monthly.columns = [\"Month\", \"Monthly Generation\"]\n", "    # Save monthly data to CSV\n", "    df_monthly.to_csv(f\"{plantName}_monthly_data.csv\", index=False)\n", "    \n", "    print(f\"CSV files created: {plantName}_daily_data.csv and {plantName}_monthly_data.csv\")\n", "\n", "# Example usage\n", "generate_csv_from_data(dg, plantName)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSV files saved: IN.INTE.KPRT_Daily_data.csv, IN.INTE.KPRT_monthly_data.csv\n"]}], "source": ["### THIS IS FOR SOLAR\n", "\n", "\n", "import pandas as pd\n", "import numpy as np\n", "\n", "def generate_energy_csv(plant_name,dg):\n", "    \n", "    \n", "    # Creating DataFrame\n", "    df = pd.DataFrame(dg)\n", "    df.rename(columns={'timestamp': 'time', 'Plant.Daily Energy': 'Daily Generation'}, inplace=True)\n", "    df[\"time\"] = pd.to_datetime(df[\"time\"])\n", "    df[\"time\"] = df[\"time\"].dt.strftime(\"%Y-%m-%d\")  # Convert to human-readable format\n", "    \n", "    # Calculating monthly sum\n", "    df[\"Month\"] = pd.to_datetime(df[\"time\"]).dt.to_period(\"M\")\n", "    monthly_sum = df.groupby(\"Month\")[\"Daily Generation\"].sum().reset_index()\n", "    monthly_sum.rename(columns={\"Daily Generation\": \"Monthly Generation\"}, inplace=True)\n", "    \n", "    # Saving to CSV\n", "    file_path = f\"{plant_name}_Daily_data.csv\"\n", "    df.to_csv(file_path, index=False)\n", "    \n", "    monthly_sum_path = f\"{plant_name}_monthly_data.csv\"\n", "    monthly_sum.to_csv(monthly_sum_path, index=False)\n", "    \n", "    print(f\"CSV files saved: {file_path}, {monthly_sum_path}\")\n", "\n", "# Define parameters\n", "\n", "\n", "# Call the function with API instance (m)\n", "generate_energy_csv(plantName, dg)\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "RuntimeError", "evalue": "asyncio.run() cannot be called from a running event loop", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[2], line 15\u001b[0m\n\u001b[0;32m      8\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mmain\u001b[39m():\n\u001b[0;32m      9\u001b[0m     \u001b[38;5;28;01mawait\u001b[39;00m asyncio\u001b[38;5;241m.\u001b[39mgather(\n\u001b[0;32m     10\u001b[0m         fetch_data(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mSolar API\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;241m3\u001b[39m),\n\u001b[0;32m     11\u001b[0m         fetch_data(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mWind API\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;241m2\u001b[39m),\n\u001b[0;32m     12\u001b[0m         fetch_data(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mWeather API\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;241m1\u001b[39m)\n\u001b[0;32m     13\u001b[0m     )\n\u001b[1;32m---> 15\u001b[0m \u001b[43masyncio\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmain\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\runners.py:191\u001b[0m, in \u001b[0;36mrun\u001b[1;34m(main, debug, loop_factory)\u001b[0m\n\u001b[0;32m    161\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Execute the coroutine and return the result.\u001b[39;00m\n\u001b[0;32m    162\u001b[0m \n\u001b[0;32m    163\u001b[0m \u001b[38;5;124;03mThis function runs the passed coroutine, taking care of\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    187\u001b[0m \u001b[38;5;124;03m    asyncio.run(main())\u001b[39;00m\n\u001b[0;32m    188\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m    189\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m events\u001b[38;5;241m.\u001b[39m_get_running_loop() \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[0;32m    190\u001b[0m     \u001b[38;5;66;03m# fail fast with short traceback\u001b[39;00m\n\u001b[1;32m--> 191\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\n\u001b[0;32m    192\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124masyncio.run() cannot be called from a running event loop\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m    194\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m Runner(debug\u001b[38;5;241m=\u001b[39mdebug, loop_factory\u001b[38;5;241m=\u001b[39mloop_factory) \u001b[38;5;28;01mas\u001b[39;00m runner:\n\u001b[0;32m    195\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m runner\u001b[38;5;241m.\u001b[39mrun(main)\n", "\u001b[1;31mRuntimeError\u001b[0m: asyncio.run() cannot be called from a running event loop"]}], "source": ["import asyncio\n", "\n", "async def fetch_data(source, delay):\n", "    print(f\"Fetching data from {source}...\")\n", "    await asyncio.sleep(delay)  # Simulate network delay\n", "    print(f\"Data from {source} received\")\n", "\n", "async def main():\n", "    await asyncio.gather(\n", "        fetch_data(\"Solar API\", 3),\n", "        fetch_data(\"Wind API\", 2),\n", "        fetch_data(\"Weather API\", 1)\n", "    )\n", "\n", "asyncio.run(main())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}