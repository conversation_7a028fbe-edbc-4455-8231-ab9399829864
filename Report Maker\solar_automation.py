import pandas as pd
import csv
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from utils import fetch_data, generate_solar_dgr_report, generate_solar_dgr_pdf, csv_to_pdf, merge_pdfs
import logging
import os

logging = logging.getLogger('solar_dgr_automation')

def generate_solar_automation_report(plant_name, start_date, customer_name, project):
    poa_data = fetch_data(plant_name, ['Daily POA Energy'], 'Plant', start_date, start_date)
    pr_data = fetch_data(plant_name, ['PR'], 'Plant', start_date, start_date)
    daily_generation = fetch_data(plant_name, ['Daily Energy'], 'Inverter', start_date, start_date)

    if poa_data.empty or pr_data.empty or daily_generation.empty:
        logging.error("Skipping report generation due to missing data.")
        return

    avg_poa = poa_data.iloc[:, 1:].mean().mean()
    avg_pr = pr_data.iloc[:, 1:].mean().mean()
    total_generation = daily_generation.iloc[:, 1:].sum().sum()

    csv_report = generate_solar_dgr_report(avg_poa, avg_pr, total_generation, start_date, customer_name, project)
    summary_pdf = generate_solar_dgr_pdf(start_date, customer_name, project, avg_poa, avg_pr, total_generation)
    converted_pdf = csv_to_pdf(csv_report)

    final_pdf = merge_pdfs(summary_pdf, converted_pdf, f"Final_Solar_DGR_{start_date}.pdf")
    
    os.remove(csv_report)
    os.remove(summary_pdf)
    os.remove(converted_pdf)

