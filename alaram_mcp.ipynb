{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a70f5170", "metadata": {}, "outputs": [], "source": ["from integration_utilities import PrescintoIntegrationUtilities"]}, {"cell_type": "code", "execution_count": 2, "id": "56fe34d7", "metadata": {}, "outputs": [], "source": ["m = PrescintoIntegrationUtilities(server = 'IN',token ='5f142bbb-02dc-481d-9cba-452efc48b304')"]}, {"cell_type": "code", "execution_count": 3, "id": "92c851ed", "metadata": {}, "outputs": [], "source": ["#Khyathi steels\n", "plantName = 'IN.INTE.JOD1'\n", "startDate = '2025-01-19'\n", "endDate = '2025-02-19'"]}, {"cell_type": "code", "execution_count": 4, "id": "01f551dd", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'm' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[4], line 3\u001b[0m\n\u001b[0;32m      1\u001b[0m params \u001b[38;5;241m=\u001b[39m [\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mInverter Alarm\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[0;32m      2\u001b[0m category \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mAlarm\u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[1;32m----> 3\u001b[0m alarmDf \u001b[38;5;241m=\u001b[39m \u001b[43mm\u001b[49m\u001b[38;5;241m.\u001b[39mfetchDataV2(plantName,category,params,\u001b[38;5;28;01mNone\u001b[39;00m, startDate, endDate)\n", "\u001b[1;31mNameError\u001b[0m: name 'm' is not defined"]}], "source": ["params = ['Inverter Alarm']\n", "category = 'Alarm'\n", "alarmDf = m.fetchDataV2(plantName,category,params,None, startDate, endDate)"]}, {"cell_type": "code", "execution_count": 28, "id": "86a995a8", "metadata": {}, "outputs": [{"data": {"text/plain": ["'None'"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["alarmDf"]}, {"cell_type": "code", "execution_count": 29, "id": "37bd27db", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'str' object has no attribute 'columns'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[29], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[43malarmDf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\n", "\u001b[1;31mAttributeError\u001b[0m: 'str' object has no attribute 'columns'"]}], "source": ["alarmDf.columns"]}, {"cell_type": "code", "execution_count": null, "id": "f7789899", "metadata": {}, "outputs": [{"data": {"text/plain": ["'None'"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["#Khyathi steels\n", "plantName = 'IN.INTE.SABE'\n", "startDate = '2025-01-19'\n", "endDate = '2025-02-19'\n", "\n", "params = ['Plant Alarm']\n", "category = 'Alarm'\n", "alarmDf = m.fetchDataV2(plantName,category,params,None, startDate, endDate)\n", "alarmDf"]}, {"cell_type": "code", "execution_count": 3, "id": "647f8093", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\<PERSON><PERSON><PERSON><PERSON><PERSON>\\Wind_solar\\integration_utilities.py:308: FutureWarning: Passing literal json to 'read_json' is deprecated and will be removed in a future version. To read from a literal string, wrap it in a 'StringIO' object.\n", "  dataResp = pd.read_json(resp, orient=\"split\")\n"]}], "source": ["params = ['Daily Energy']\n", "category = ['Plant']\n", "plantName = 'IN.INTE.KIDS'\n", "startDate = '2025-01-01'\n", "endDate = '2025-06-02'\n", "condition = {\"Daily Energy\" : \"last\"}\n", "dg_df = m.fetchDataV2(plantName, category, params, None, startDate, endDate, granularity='15m', condition = condition)"]}, {"cell_type": "code", "execution_count": 7, "id": "804f6b19", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(154298753.86)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["dg_df['Kids Clinic India Limited.Daily Energy'].sum()"]}, {"cell_type": "code", "execution_count": 4, "id": "b1b2c1ff", "metadata": {}, "outputs": [], "source": ["dg_df.to_csv(\"TEST KIDS.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 5, "id": "fed90ce1", "metadata": {}, "outputs": [], "source": ["# I WANT TO CREATE A CSV FILE FROM THIS dg_df AND ADD TWO COLUMNS \"Plant Long Name\" AND \"Plant Short Name\"  \"Kids Clinic India Limited\" AND 'IN.INTE.KIDS'\n", "dg_df.to_csv(\"dg_df.csv\", index=False)\n", "import pandas as pd\n", "df = pd.read_csv(\"dg_df.csv\")\n", "df[\"Plant Long Name\"] = \"Kids Clinic India Limited\"\n", "df[\"Plant Short Name\"] = 'IN.INTE.KIDS'\n", "df.to_csv(\"dg_df.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "fc0087a9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}