import pandas as pd
import csv
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from utils import fetch_data, generate_dgr_report, generate_dgr_pdf, csv_to_pdf, merge_pdfs
import logging
import os

logging = logging.getLogger('wind_dgr_automation')

def generate_wind_automation_report(plant_name, start_date, customer_name, project, ma_percent):
    wind_speed_data = fetch_data(plant_name, ['WTUR.Wind-Speed'], 'Turbine', start_date, start_date)
    daily_generation = fetch_data(plant_name, ['WTUR.Generation today'], 'Turbine', start_date, start_date)

    avg_wind_speed = wind_speed_data.iloc[:, 1:].mean().mean() if not wind_speed_data.empty else 0
    total_generation = daily_generation.iloc[:, 1:].sum().sum() if not daily_generation.empty else 0

    csv_report = generate_dgr_report(wind_speed_data, daily_generation, start_date, customer_name, project, ma_percent)
    summary_pdf = generate_dgr_pdf(start_date, customer_name, project, avg_wind_speed, total_generation, ma_percent)
    converted_pdf = csv_to_pdf(csv_report)

    final_pdf = merge_pdfs(summary_pdf, converted_pdf, f"Final_Wind_DGR_{start_date}.pdf")

    os.remove(csv_report)
    os.remove(summary_pdf)
    os.remove(converted_pdf)
    
