import pandas as pd
import csv
import logging
from datetime import datetime, timedelta
from IntegrationUtilities import PrescintoIntegrationUtilities
from logger_setup import setup_logger
from IntegrationUtilities import PrescintoIntegrationUtilities
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from PyPDF2 import PdfMerger


logging = setup_logger('test_automate', 'test_automate.log')

# Initialize Prescinto Integration
m = PrescintoIntegrationUtilities(server='IN', token='1d8f9dc5-8f50-4ffc-8b90-1b40b866283c')

def get_yesterday_date():
    return (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')

def fetch_data(plant_name, params, category, start_date, end_date):
    """
    Fetches data from Prescinto API and handles errors.
    """
    try:
        data = m.fetchDataV2(plant_name, category, params, None, start_date, end_date, granularity='15m')
        if isinstance(data, str):  # API returned an error message as a string
            logging.error(f"Error fetching {params}: {data}")
            return None
        return pd.DataFrame(data)
    except Exception as e:
        logging.error(f"Exception fetching {params}: {e}")
        return None



def generate_solar_dgr_report(avg_poa, avg_pr, total_generation, start_date, customer_name, project):
    output_file = "HARI_Final_Solar_DGR_Report.csv"

    with open(output_file, mode="w", newline="") as file:
        writer = csv.writer(file)
        writer.writerow(["Date", "Customer Name", "Project", "POA(Kwh/m2)", "PR%", "Daily Generation(kWh)"])
        writer.writerow([start_date, customer_name, project, avg_poa, avg_pr, total_generation])

    print(f"CSV file '{output_file}' generated successfully.")
    return output_file

def generate_solar_dgr_pdf(date, customer_name, project, poa, pr, daily_generation):
    output_file = "HARI_Final_Solar_DGR_Report.pdf"
    doc = SimpleDocTemplate(output_file, pagesize=A4)
    elements = []
    styles = getSampleStyleSheet()

    title = Paragraph("<b><font size=14>Solar Daily Generation Report</font></b>", styles["Title"])
    elements.append(title)
    elements.append(Spacer(1, 10))

    data = [
        ["Date:", date],
        ["Customer Name:", customer_name],
        ["Project:", project],
        ["POA (Kwh/m2):", poa],
        ["PR%:", pr],
        ["Daily Generation (MU):", daily_generation]
    ]

    table = Table(data, colWidths=[150, 250])
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 5),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))

    elements.append(table)
    doc.build(elements)
    print(f"PDF report '{output_file}' generated successfully.")
    return output_file

def csv_to_pdf(csv_filename):

    """
    Converts a CSV file to a PDF file with table formatting.
    """
    pdf_filename = 'hari_csv_pdf.pdf'
    try:
        df = pd.read_csv(csv_filename)  # Read the CSV file
        data = [df.columns.tolist()] + df.values.tolist()  # Convert DataFrame to list format

        doc = SimpleDocTemplate(pdf_filename, pagesize=A4)
        elements = []

        table = Table(data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        elements.append(table)
        doc.build(elements)

        print(f"PDF file '{pdf_filename}' generated successfully from '{csv_filename}'.")
        return pdf_filename

    except Exception as e:
        print(f"Error while converting CSV to PDF: {e}")




def merge_pdfs(pdf1, pdf2):
    """
    Merges two PDF files into a single PDF.
    """
    output_pdf = "final_report_solar.pdf"
    try:
        merger = PdfMerger()
        merger.append(pdf1)
        merger.append(pdf2)
        merger.write(output_pdf)
        merger.close()
        print(f"PDFs merged successfully into '{output_pdf}'")
    except Exception as e:
        print(f"Error while merging PDFs: {e}")




def main():
    report_date = get_yesterday_date()
    # report_date = '2025-02-27'
    plant_name = 'IN.INTE.KIDS'
    customer_name = "Kids Clinic India"
    project = "SOLAR_INTEGRUM"
    
    # Fetch Data
    poa_data = fetch_data(plant_name, ['Daily POA Energy'], 'Plant', report_date, report_date)
    pr_data = fetch_data(plant_name, ['PR'], 'Plant', report_date, report_date)
    daily_generation = fetch_data(plant_name, ['Daily Energy'], 'Inverter', report_date, report_date)

    
    avg_poa = poa_data.iloc[:, 1:].mean().mean() if not poa_data.empty else 0.0
    avg_pr = pr_data.iloc[:, 1:].mean().mean() if not pr_data.empty else 0.0
    total_generation = daily_generation.iloc[:, 1:].sum().sum() if not daily_generation.empty else 0.0
    
    if poa_data is not None and pr_data is not None and daily_generation is not None:
        second_csv = generate_solar_dgr_report(avg_poa, avg_pr, total_generation, report_date, customer_name, project)
       
    else:
        logging.error("Skipping report generation due to missing data")



    main_pdf = generate_solar_dgr_pdf(report_date, customer_name, project, avg_poa, avg_pr, total_generation)
    
    csv_pdf = csv_to_pdf(second_csv)
    # Example usage
    merge_pdfs(main_pdf, csv_pdf)

# Run the script automatically at 1:00 AM daily
if __name__ == "__main__":
    from schedule import every, repeat, run_pending
    import time
    
    @repeat(every().day.at("12:17"))
    def scheduled_task():
        main()
    
    while True:
        run_pending()
        time.sleep(60)
