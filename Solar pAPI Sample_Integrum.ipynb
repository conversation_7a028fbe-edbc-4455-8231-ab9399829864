{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ![Prescinto](https://prescinto.ai/wp-content/uploads/2023/06/Frame-7-3-1-1.svg)\n", "#### pAPI Wrapper Function's Sample Notebook"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "u93-l4Aww3ZJ"}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import plotly.graph_objects as go\n", "import plotly.io as pio\n", "from plotly.subplots import make_subplots\n", "pd.options.plotting.backend = \"plotly\""]}, {"cell_type": "markdown", "metadata": {"id": "iz5Wtjwww3ZX"}, "source": ["# Prescinto API (pAPI) access control\n", "\n", "* pAPI access is controlled using API token.\n", "- pAPI Validates each incoming request by API token.\n", "* API token can be aquire from [Prescinto Cloud](https://cloud.prescinto.ai)\n", "* Prescinto API token has a expiry as well."]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Prescinto Library\n", "## *PrescintoIntegrationUtilities* is a utility that has  wrapper function for each pAPI request.\n", "### User can directly consume these functions/methods rather than direct restapi call.\n", "##### This file is being documented only for Solar Domain. So In this document we will go through different function calls for Solar Domain."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\envs\\jupyterlab\\lib\\site-packages\\requests\\__init__.py:102: RequestsDependencyWarning: urllib3 (1.26.12) or chardet (5.2.0)/charset_normalizer (2.0.12) doesn't match a supported version!\n", "  warnings.warn(\"urllib3 ({}) or chardet ({})/charset_normalizer ({}) doesn't match a supported \"\n"]}], "source": ["from integrationUtilities import PrescintoIntegrationUtilities"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### PrescintoIntegrationUtilities has two paramerters\n", "- ```Server(str,optional):``` Name of the Server for fetching data. [**Prescinto**](https://prescinto.ai) has its servers in different regions in all over the world. If No server choice is given, It will automatically connect with your nearby server. i.e.\n", "   ```python\n", "       server = 'IN' or'EU' or 'US' \n", "   ```\n", "- ```token(str, required):``` Api token for validating the api request. i.e.\n", "   ````python\n", "    token = 'API token'\n", "    ````\n", "```Note:``` Without ````API Token```` Request will not validated.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### To get API Token\n", "1. <PERSON>gin to [Prescinto Portal](https://cloud.prescinto.ai)\n", "2. Go to Profile \n", "3. Copy the API token and pass it as parameter while creating class object"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "eP5Yl3C5w3Zh"}, "outputs": [], "source": ["m = PrescintoIntegrationUtilities(server = 'IN',token ='370E0979-EEA7-4715-88FC-92C396xxxxxxx')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Common Variables -\n", "- ```Plant Name:``` Consist Plant Name for which we will fetch information\n", "- ```Start Date:``` Minimum Date for Data filter\n", "- ```End Date:``` Maximum Date for Data filter\n", "\n", "**```Note:```** *Start date and End date is being used where need to pull data for a specified daterange.*"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "GucEULl5w3Zm"}, "outputs": [], "source": ["plantName = 'IN.SOLA.DEMO' # Plant Short Name should be used\n", "startDate = '2024-08-01'\n", "endDate = '2024-08-31'"]}, {"cell_type": "markdown", "metadata": {"id": "T5_Rz9FVw3Zu"}, "source": ["# getPlantInfo-\n", "### This method is used to retrieve information about a plant template.\n", "#### This response can be visually validated by going to the Trends in Prescinto Portal\n", "#### This function consume plant name as parameters.\n", "#### Returns Plant categories, parameters and devices. \n", "- Categories\n", "- Category's Devices\n", "- Category's Parameters"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"id": "_hKbF1Yaw3Zx"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Inverter', 'SMB-24', 'WS', 'MFM', 'Plant']\n"]}], "source": ["plantName = 'IN.INTE.STOV' # Plant Short Name should be used\n", "startDate = '2024-08-01'\n", "endDate = '2024-08-31'\n", "m = PrescintoIntegrationUtilities(server = 'IN',token ='53A0A127-9EBF-4398-A8EB-704C1395BD47')\n", "categories = None\n", "deviceDict = None\n", "parameterDict = None\n", "try:\n", "    categories, deviceDict, parameterDict = m.getPlantInfo(plantName)\n", "    print(categories)\n", "except Exception as e:\n", "    print(f'Error PlantInfo: {str(e)}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.get"]}, {"cell_type": "markdown", "metadata": {"id": "bdiIXp_Qw3Zz"}, "source": ["## Categories\n", "### Devices are segregated into categories. Most useful categories are:\n", "- *````Inverter :````* Inverters\n", "- *````WS :````* Weather stations\n", "- *````SMB-24 :````* String Monitoring Boxes\n", "- *````MFM :````* Multi-Function Meters (taking measurements at sub-plant level)\n", "- *````Plant :````* Consolidated plant level measurements just before interconnection"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"id": "o6g6wdt_w3Z0", "outputId": "de19a444-06fb-4815-c92e-99dccecf805b"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Inverter', 'SMB-24', 'WS', 'MFM', 'Plant']\n"]}], "source": ["if categories is not None:\n", "    print(categories)"]}, {"cell_type": "markdown", "metadata": {"id": "wFeJTbLIw3Z4"}, "source": ["## Category devices\n", "### *````There is defined devices for each of the category````*"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"id": "t_696t9_w3Z5", "outputId": "e1d9f031-84c6-44d7-9cf7-6b52639090c5"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Devices of Inverter:\n", "['INV01', 'INV02', 'INV03', 'INV04', 'INV05', 'INV06', 'INV07', 'INV08', 'INV09', 'INV10', 'INV11', 'INV12', 'INV13', 'INV14', 'INV15', 'INV16', 'INV17', 'INV18', 'INV19', 'INV20', 'INV21', 'INV22', 'INV23', 'INV24', 'INV25', 'INV26']\n", "\n", "Weather Station:\n", "['DG MFM -1', 'DG MFM -2', 'DG MFM -3', 'DG MFM -4', 'DG MFM -5', 'DG MFM -6', 'DG MFM -7', 'DG MFM -8', 'Grid Meter', 'MFM']\n"]}], "source": ["# Dictionary of devices based on categories\n", "if deviceDict is not None:\n", "    print(f'Devices of Inverter:\\n{deviceDict[\"Inverter\"]}')\n", "    print()\n", "    print(f'Weather Station:\\n{deviceDict[\"MFM\"]}')"]}, {"cell_type": "markdown", "metadata": {"id": "SpYMHU05w3Z7"}, "source": ["## Category Parameters\n", "### *````There is defined parameters for each of the category````*"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"id": "hZlsL9hKw3Z8", "outputId": "54e8de84-1598-4cdc-dd99-963461755ff7"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Parameters of Inverter:\n", "\n", "['Active Power', 'Apparent Power', 'DC Current 1', 'DC Current 10', 'DC Current 11', 'DC Current 12', 'DC Current 2', 'DC Current 3', 'DC Current 4', 'DC Current 5', 'DC Current 6', 'DC Current 7', 'DC Current 8', 'DC Current 9', 'Inverter Status', 'Inverter Temperature', 'PV Current', 'PV Power', 'PV Voltage', 'Reactive Power', 'Daily Energy', 'Specific Power', 'Efficiency', 'Total Energy', 'Specific Energy', 'DC Voltage 1', 'DC Voltage 2', 'DC Voltage 3', 'DC Voltage 4', 'DC Voltage 5', 'DC Voltage 6', 'DC Voltage 7', 'DC Voltage 8', 'DC Voltage 9', 'DC Voltage 10', 'DC Voltage 11', 'DC Voltage 12', 'AC Current', 'Power Factor', 'AC Voltage', 'Status', 'PR', 'Expected Power', 'Expected Energy', 'Frequency', 'CUF', 'AC Current Phase 1', 'AC Current Phase 2', 'AC Current Phase 3', 'AC Voltage Phase 1', 'AC Voltage Phase 2', 'AC Voltage Phase 3', 'Fault', 'AC Power Phase 1', 'AC Power Phase 2', 'AC Power Phase 3', 'Error', 'Alarm', 'Heatsink Temperature']\n", "\n", "\n", "Parameters of Weather Station:\n", "\n", "['Module Temperature', 'Ambient Temperature', 'Wind Speed', 'Wind Direction', 'GH Irradiance', 'POA Irradiance', 'Daily POA Energy', 'Daily GHI Energy']\n"]}], "source": ["# Dictionary of parameters\n", "if parameterDict is not None:\n", "    print(f'Parameters of Inverter:\\n')\n", "    print(parameterDict[\"Inverter\"])\n", "    print('\\n')\n", "    print('Parameters of Weather Station:\\n')\n", "    print(parameterDict[\"WS\"])"]}, {"cell_type": "markdown", "metadata": {"id": "TRiCLXsaw3Z-"}, "source": ["## Data Fetching \n", "### fetchDataV2() is used to fetch time series data from database using pAPI\n", "#### ````Input Parameters:````\n", "   - *````Plant Name :````* (string, required)\n", "   - *````Category :````* (list, required)\n", "   - *````Parameter :````* (list, required)\n", "   - *````Devices :````* (None or empty list to be passed if all the devices data is required. Select devices should be passed).\n", "   - *````Start Date :````* (string, format: 'YYYY-MM-DD HH:MM:SS' or 'YYYY-MM-DD' or 'YYYY-MM-DD HH:MM:SS±hh:mm'): The start date and time for data retrieval.       \n", "   - *````End Date :````* (string, format: 'YYYY-MM-DD HH:MM:SS' or 'YYYY-MM-DD' or 'YYYY-MM-DD HH:MM:SS±hh:mm'): The end date and time for data retrieval.\n", "   - *````granularity :````* (String, optional):  The time granularity of the retrieved data.\n", "        - Seconds - '1s', '2s'\n", "        - Minute - '1m', '5m' \n", "        - Hour - '1h', '2h'\n", "        - Day - '1d', '2d'\n", "        - Week - '1w', '2w'\n", "        -  Month - '30d' or '31d'\n", "   Default would be ***5m***\n", "   - *````condition :````* (Dictionary, optional)\n", "        - Should pass a dictionary where key is the parameter and value is the aggregation to be performed\n", "        - Aggregation supported:\n", "            - mean\n", "            - median\n", "            - mode\n", "            - sum\n", "            - first\n", "            - last\n", "            - max\n", "            - min\n", "            - st<PERSON><PERSON>\n", "            - spread\n", "            - count\n", "            - distinct\n", "            - integral\n", "        - default would be first\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Note**: Although there's no limitation at the API with regards to the number of calls, for optimal performance, its recommended to keep the maximum duration to 1 month and pull data in batches for duration more than 1 month."]}, {"cell_type": "markdown", "metadata": {"id": "TRiCLXsaw3Z-"}, "source": ["## Output:\n", "- Dataframe based on the parameters\n", "- Column names would be of the format 'Device.Parameter'\n", "- There would be a 'time' column with Timestamp in UTC format"]}, {"cell_type": "markdown", "metadata": {"id": "3q3cVi3kw3aA"}, "source": ["## Fetch Inverter Active Power\n", "- **````Category````**\n", "    - Static Category\n", "    ```python\n", "       category = ['Inverter']\n", "    ```\n", "    - Dynamic Category\n", "    ```python\n", "       category = [cat for cat in categories  if cat =='Inverter']\n", "    ```\n", "- **````Parameter````**\n", "    - Static Parameter\n", "    ```python\n", "    params = ['Active Power']\n", "    ```\n", "    - Dynamic Parameters\n", "    ``` python\n", "    params = [param for cat, params in paramDict.items() if cat == \"Inverter\" for param in params if param == \"Active Power\"]\n", "    ```  \n", "- **````<PERSON><PERSON><PERSON><PERSON>````**\n", "    - Static Device List\n", "    ```python\n", "    deviceList = [] or ['INV2-1-CB1', 'INV2-1-CB2', 'INV2-1-CB3']\n", "    ```\n", "    - Dynamic Device List\n", "    ```python\n", "    deviceList = deviceDict[\"Inverter\"]\n", "    \n", "    ```\n", "- **````Start Date````**\n", "    - Only Date\n", "    ```python \n", "    startDate ='2023-08-10'\n", "    ```\n", "   - Date Time\n", "    ```python \n", "    startDate ='2023-08-10 13:10:00'\n", "    ```\n", "    - Date Time and Timezone\n", "    ```python \n", "    startDate ='2023-08-10 13:10:00+05:30'\n", "    ```\n", "- **````End Date````**\n", "    - Only Date\n", "    ```python \n", "    endDate ='2023-08-10'\n", "    ```\n", "   - Date Time\n", "   ```python \n", "    endDate ='2023-08-10 15:00:00'\n", "   ```\n", "   - Date Time and Timezone\n", "   ```python \n", "    endDate ='2023-08-10 15:00:00+05:30'\n", "   ```\n", "- **````Condition````**\n", "   - Mean\n", "   ```python \n", "   conditon ={\"Active Power\":\"mean\"}\n", "   ```\n", "   - Median\n", "   ```python \n", "   conditon ={\"Active Power\":\"median\"}\n", "   ```\n", "   - Mode\n", "   ```python \n", "   conditon ={\"Active Power\":\"mode\"}\n", "   ```\n", "   - Sum\n", "   ```python \n", "   conditon ={\"Active Power\":\"sum\"}\n", "   ```\n", "   - Last\n", "   ```python \n", "   conditon ={\"Active Power\":\"last\"}\n", "   ```\n", "   - Count\n", "   ```python \n", "   conditon ={\"Active Power\":\"count\"}\n", "   ```\n", "   - Max\n", "   ```python \n", "   conditon ={\"Active Power\":\"max\"}\n", "   ```\n", "   - Min\n", "   ```python \n", "   conditon ={\"Active Power\":\"min\"}\n", "   ```\n", "   - De<PERSON>ult\n", "   ```python \n", "   conditon ={\"Active Power\":\"first\"}\n", "   ```\n", "    \n", "- **````Granularity````**\n", "   - 1 Second Granularity\n", "   ```python \n", "   granularity ='1s'\n", "   ```\n", "   - 10 Second Granularity\n", "   ```python \n", "   granularity ='10s'\n", "   ```\n", "   - 1 Minute Granularity\n", "   ```python \n", "   granularity ='1m'\n", "   ```\n", "   - 10 Minute Granularity\n", "   ```python \n", "   granularity ='1s'\n", "   ```\n", "   - 1 Hour Granularity\n", "   ```python \n", "   granularity ='1h'\n", "   ```\n", "   - 1 Day Granularity\n", "   ```python \n", "   granularity ='1d'\n", "   ```\n", "   - Default <PERSON>\n", "   ```python \n", "   granularity ='5m'\n", "   ```\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"id": "28UnCAYMw3aB"}, "outputs": [], "source": ["invAcPwrDf = m.fetchDataV2(plantName,['Plant'],['Active Power'],None,\"2024-12-11 06:30:00\",\"2024-12-11 18:30:00\")"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"id": "74MNAGtOvHox", "outputId": "9f4b8e08-4e55-4038-b7e5-0b28802860ad"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>time</th>\n", "      <th>StoveKraft Limited - Unit-3.Active Power</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-12-11T06:30:00Z+05:30</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-12-11T06:35:00Z+05:30</td>\n", "      <td>1.63</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-12-11T06:40:00Z+05:30</td>\n", "      <td>8.60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-12-11T06:45:00Z+05:30</td>\n", "      <td>18.81</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-12-11T06:50:00Z+05:30</td>\n", "      <td>37.80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>140</th>\n", "      <td>2024-12-11T18:10:00Z+05:30</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>141</th>\n", "      <td>2024-12-11T18:15:00Z+05:30</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>142</th>\n", "      <td>2024-12-11T18:20:00Z+05:30</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>143</th>\n", "      <td>2024-12-11T18:25:00Z+05:30</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>144</th>\n", "      <td>2024-12-11T18:30:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>145 rows × 2 columns</p>\n", "</div>"], "text/plain": ["                           time  StoveKraft Limited - Unit-3.Active Power\n", "0    2024-12-11T06:30:00Z+05:30                                      0.00\n", "1    2024-12-11T06:35:00Z+05:30                                      1.63\n", "2    2024-12-11T06:40:00Z+05:30                                      8.60\n", "3    2024-12-11T06:45:00Z+05:30                                     18.81\n", "4    2024-12-11T06:50:00Z+05:30                                     37.80\n", "..                          ...                                       ...\n", "140  2024-12-11T18:10:00Z+05:30                                      0.00\n", "141  2024-12-11T18:15:00Z+05:30                                      0.00\n", "142  2024-12-11T18:20:00Z+05:30                                      0.00\n", "143  2024-12-11T18:25:00Z+05:30                                      0.00\n", "144  2024-12-11T18:30:00Z+05:30                                       NaN\n", "\n", "[145 rows x 2 columns]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["invAcPwrDf"]}, {"cell_type": "markdown", "metadata": {"id": "6WEYxtbkw3aF"}, "source": ["## Fetching POA Irradiance and Module Temperature from WS\n", "##### If deviceList is ```None```, It return data for all devices.\n", "##### No granularity passed, default granularity comes into action. default is ``` '5m' ```\n", "##### No Condition passed, default aggregate condition comes into picture. ie. default is ``` first ```\n"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"id": "yclDJNUzw3aG", "outputId": "226fdd1d-e846-49b8-b910-d2a9705ddc21"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Module Temperature', 'Ambient Temperature', 'Wind Speed', 'Wind Direction', 'GH Irradiance', 'POA Irradiance', 'Daily POA Energy', 'Daily GHI Energy']\n"]}], "source": ["if parameterDict is not None:\n", "    print(parameterDict['WS'])"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"id": "j3Ct-jEiw3aI"}, "outputs": [], "source": ["params = ['POA Irradiance','Module Temperature']\n", "category = ['WS']\n", "wsDf = m.fetchDataV2(plantName, category, params, None, '2024-11-01', '2024-11-02')"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"id": "VT2ggZdCvHo1", "outputId": "81a01ab2-a9db-423f-e57e-aa2fe659f57d"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>time</th>\n", "      <th>WS.Module Temperature</th>\n", "      <th>WS.POA Irradiance</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-11-01T00:00:00Z+05:30</td>\n", "      <td>23.570</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-11-01T00:05:00Z+05:30</td>\n", "      <td>23.849</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-11-01T00:10:00Z+05:30</td>\n", "      <td>24.031</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-11-01T00:15:00Z+05:30</td>\n", "      <td>23.824</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-11-01T00:20:00Z+05:30</td>\n", "      <td>23.912</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>571</th>\n", "      <td>2024-11-02T23:35:00Z+05:30</td>\n", "      <td>22.526</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>572</th>\n", "      <td>2024-11-02T23:40:00Z+05:30</td>\n", "      <td>22.248</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>573</th>\n", "      <td>2024-11-02T23:45:00Z+05:30</td>\n", "      <td>22.236</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>574</th>\n", "      <td>2024-11-02T23:50:00Z+05:30</td>\n", "      <td>22.440</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>575</th>\n", "      <td>2024-11-02T23:55:00Z+05:30</td>\n", "      <td>22.127</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>576 rows × 3 columns</p>\n", "</div>"], "text/plain": ["                           time  WS.Module Temperature  WS.POA Irradiance\n", "0    2024-11-01T00:00:00Z+05:30                 23.570                0.0\n", "1    2024-11-01T00:05:00Z+05:30                 23.849                0.0\n", "2    2024-11-01T00:10:00Z+05:30                 24.031                0.0\n", "3    2024-11-01T00:15:00Z+05:30                 23.824                0.0\n", "4    2024-11-01T00:20:00Z+05:30                 23.912                0.0\n", "..                          ...                    ...                ...\n", "571  2024-11-02T23:35:00Z+05:30                 22.526                0.0\n", "572  2024-11-02T23:40:00Z+05:30                 22.248                0.0\n", "573  2024-11-02T23:45:00Z+05:30                 22.236                0.0\n", "574  2024-11-02T23:50:00Z+05:30                 22.440                0.0\n", "575  2024-11-02T23:55:00Z+05:30                 22.127                0.0\n", "\n", "[576 rows x 3 columns]"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["wsDf"]}, {"cell_type": "markdown", "metadata": {"id": "uvbqESQuw3aK"}, "source": ["## Plant's Max Daily Energy for a year\n", "##### granularity is ````'1d' ```` \n", "##### Condition is passed for the aggregation to be performed on ````Daily Energy```` is ````MAX````"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"id": "oCTsKC0Fw3aK"}, "outputs": [], "source": ["params = ['Daily Energy']\n", "category = ['Plant']\n", "condition = {'Daily Energy':'max'}\n", "plantDEDf = m.fetchDataV2(\n", "            plantName,\n", "            category,\n", "            params,\n", "            None,\n", "            '2024-01-01',\n", "            '2024-12-11',\n", "            granularity = '1d',\n", "            condition=condition)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "h9XMm2RPvHo2", "outputId": "9225460e-6d1a-446b-ba9a-46812b5bfbcf"}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>time</th>\n", "      <th>StoveKraft Limited - Unit-3.Daily Energy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-01-01T00:00:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-01-02T00:00:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-01-03T00:00:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-01-04T00:00:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-01-05T00:00:00Z+05:30</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>341</th>\n", "      <td>2024-12-07T00:00:00Z+05:30</td>\n", "      <td>11405.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>342</th>\n", "      <td>2024-12-08T00:00:00Z+05:30</td>\n", "      <td>2920.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>343</th>\n", "      <td>2024-12-09T00:00:00Z+05:30</td>\n", "      <td>9636.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>344</th>\n", "      <td>2024-12-10T00:00:00Z+05:30</td>\n", "      <td>12023.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>345</th>\n", "      <td>2024-12-11T00:00:00Z+05:30</td>\n", "      <td>10673.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>346 rows × 2 columns</p>\n", "</div>"], "text/plain": ["                           time  StoveKraft Limited - Unit-3.Daily Energy\n", "0    2024-01-01T00:00:00Z+05:30                                       NaN\n", "1    2024-01-02T00:00:00Z+05:30                                       NaN\n", "2    2024-01-03T00:00:00Z+05:30                                       NaN\n", "3    2024-01-04T00:00:00Z+05:30                                       NaN\n", "4    2024-01-05T00:00:00Z+05:30                                       NaN\n", "..                          ...                                       ...\n", "341  2024-12-07T00:00:00Z+05:30                                   11405.0\n", "342  2024-12-08T00:00:00Z+05:30                                    2920.0\n", "343  2024-12-09T00:00:00Z+05:30                                    9636.0\n", "344  2024-12-10T00:00:00Z+05:30                                   12023.0\n", "345  2024-12-11T00:00:00Z+05:30                                   10673.0\n", "\n", "[346 rows x 2 columns]"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["plantDEDf"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Capacity Details API\n", "#### This function allows to retrieve ````DC capacity```` of the devices.\n", "##### Returns the DC capacity of devices within each category for the specified plant at configuration level."]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["dcCapacityDf = m.getCapacityDetail(plantName)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'Inverter': [{'INV01': 119.9},\n", "  {'INV02': 119.9},\n", "  {'INV03': 152.6},\n", "  {'INV04': 109.0},\n", "  {'INV05': 109.0},\n", "  {'INV06': 152.6},\n", "  {'INV07': 152.6},\n", "  {'INV08': 152.6},\n", "  {'INV09': 152.6},\n", "  {'INV10': 152.6},\n", "  {'INV11': 152.6},\n", "  {'INV12': 152.6},\n", "  {'INV13': 152.6},\n", "  {'INV14': 152.6},\n", "  {'INV15': 152.6},\n", "  {'INV16': 152.6},\n", "  {'INV17': 152.6},\n", "  {'INV18': 174.4},\n", "  {'INV19': 174.4},\n", "  {'INV20': 174.4},\n", "  {'INV21': 174.4},\n", "  {'INV22': 163.5},\n", "  {'INV23': 163.5},\n", "  {'INV24': 174.4},\n", "  {'INV25': 174.4},\n", "  {'INV26': 174.4}],\n", " 'SMB-24': [{'INV01|SMB01': 0.0},\n", "  {'INV02|SMB01': 0.0},\n", "  {'INV03|SMB01': 0.0},\n", "  {'INV04|SMB01': 0.0},\n", "  {'INV05|SMB01': 0.0},\n", "  {'INV06|SMB01': 0.0},\n", "  {'INV07|SMB01': 0.0},\n", "  {'INV08|SMB01': 0.0},\n", "  {'INV09|SMB01': 0.0},\n", "  {'INV10|SMB01': 0.0},\n", "  {'INV11|SMB01': 0.0},\n", "  {'INV12|SMB01': 0.0},\n", "  {'INV13|SMB01': 0.0},\n", "  {'INV14|SMB01': 0.0},\n", "  {'INV15|SMB01': 0.0},\n", "  {'INV16|SMB01': 0.0},\n", "  {'INV17|SMB01': 0.0},\n", "  {'INV18|SMB01': 0.0},\n", "  {'INV19|SMB01': 0.0},\n", "  {'INV20|SMB01': 0.0},\n", "  {'INV21|SMB01': 0.0},\n", "  {'INV22|SMB01': 0.0},\n", "  {'INV23|SMB01': 0.0},\n", "  {'INV24|SMB01': 0.0},\n", "  {'INV25|SMB01': 0.0},\n", "  {'INV26|SMB01': 0.0}],\n", " 'WS': [{'WS': None}],\n", " 'MFM': [{'DG MFM -1': 0.0},\n", "  {'DG MFM -2': 0.0},\n", "  {'DG MFM -3': 0.0},\n", "  {'DG MFM -4': 0.0},\n", "  {'DG MFM -5': 0.0},\n", "  {'DG MFM -6': 0.0},\n", "  {'DG MFM -7': 0.0},\n", "  {'DG MFM -8': 0.0},\n", "  {'<PERSON><PERSON>er': 0.0},\n", "  {'MFM': 0.0}],\n", " 'Plant': [{'StoveKraft Limited - Unit-3': 4000.0}]}"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["dcCapacityDf"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Module Cleaning  \n", "#### This function allows to retrieve ```module cleaning``` data for a specific plant within a given date range.\n", "#### Returns two type of result for plant's Inverters.\n", "##### Note : This is subject to Module Cleaning data availabilty in Prescinto Portal\n", "1. Total No of Modules per Inverter\n", "    1. ```Inverter Name:``` Name of the Inverters.\n", "    2. ```No Of Modules:``` Total No. of Modules that Inverter has.\n", "2. Per Inverter Following Details\n", "    1. ```Planned No Of Modules:``` Number of Modules planned for Cleaning.\n", "    2. ```Cleaned No Of Modules:``` Number Of Moduled has Cleaned.\n", "    3. ``` Planned Date:``` Cleaning completion Date\n", "    "]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["starDate ='2024-10-01'\n", "endDate ='2024-10-30'\n", "mcDf =  m.fetchCleaningData(plantName,startDate,endDate)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# mcDf"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Forecast Data (PVSYST)\n", "#### This Function allows to retrieve ```forecast data``` for a specific plant within a given date range.\n", "#### Return ```Monthly Expected Irradiation``` and ```Generation``` fed from simulation software.\n", "##### Note : This is subject to availability of PVSYST Data in Prescinto Portal\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["# date column should come first\n", "startDate ='2024-01-01'\n", "endDate ='2024-12-31'\n", "forecastDf = m.getForecastDetails(plantName,startDate,endDate)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["forecastDf.head(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plant Asset Details\n", "#### This Function allows to retrieve ```asset master``` data for a specific plant.\n", "##### Note : This is subject to availabilty of Asset Master Data in Prescinto Portal"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["assetDf =  m.getAsesstDetails(plantName)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assetDf.head(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plant Last Data Timestamp \n", "#### This Function allows to retrieve the ```last data timestamp``` with ```last value``` for a specific plant, category, parameters.\n", "- **````Category````**\n", "    - Static Category\n", "    ```python\n", "       category = ['Inverter']\n", "    ```\n", "    - Dynamic Category\n", "    ```python\n", "       category = [cat for cat in categories  if cat =='Inverter']\n", "    ```\n", "- **````Parameter````**\n", "    - Static Parameter\n", "    ```python\n", "    params = ['Active Power']\n", "    ```\n", "    - Dynamic Parameters\n", "    ``` python\n", "    params = [param for cat, params in paramDict.items() if cat == \"Inverter\" for param in params if param == \"Active Power\"]\n", "    ```  \n", "- **````<PERSON><PERSON><PERSON><PERSON>````**\n", "    - Static Device List\n", "    ```python\n", "    deviceList = [] or ['Inverter 2-1', 'Inverter 2-2', 'Inverter 1-1', 'Inverter 1-2']\n", "    ```\n", "    - Dynamic Device List\n", "    ```python\n", "    deviceList = deviceDict[\"Inverter\"]\n", "    \n", "    ```\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Output\n", "#### Return DataFrame with 3 columns-\n", "1. ```Time:``` LastTimestamp for Tags\n", "2. ```Name:``` Consists Name of the Tags\n", "3. ```Value:``` Last Value of requested Tags"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["category = ['Inverter']\n", "params = ['Active Power']\n", "deviceList = ['INV01']\n", "lastTimeStampValueDf =  m.getLastPlantTimeStampData(plantName, category,params, deviceList)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lastTimeStampValueDf"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Alarms\n", "####  Same function fetchDataV2 is being used for fetching alarm data but need to update category and parameters.\n", "- **````Category````**\n", "    \n", "    ```python\n", "       category = ['Alarm']\n", "    ```\n", "\n", "- **````Parameter````**\n", "    \n", "    ```python\n", "    params = ['Inverter Alarm']\n", "    ```\n", "\n", "- **````Start Date````**\n", "    \n", "   ```python \n", "    startDate ='2023-08-10'\n", "   ```\n", "- **````End Date````**\n", "    \n", "    ```python \n", "    endDate ='2023-08-10'\n", "    ```\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Output\n", "#### Returns a DataFrame consisting Alarm time,cotrollerid, controllername, type, categories, alarm serverity, message, parameterid , parametername, state, raise time, resolve time and resolved values"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Note : This is subject to Alarms data availability in Prescinto"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["category = ['Alarm']\n", "params = ['Inverter Alarm']\n", "startDate ='2024-10-01'\n", "endDate ='2024-11-11'\n", "\n", "alarmDf = m.fetchDataV2(plantName,category,params,None,startDate,endDate)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["alarmDf"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"colab": {"name": "Energy Storage API sample.ipynb", "provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 4}