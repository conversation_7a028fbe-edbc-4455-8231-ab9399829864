from mcp.server.fastmcp import FastMCP
from datetime import datetime
import os
from dotenv import load_dotenv
import snowflake.connector
import json
mcp = FastMCP("SQLAgent")


load_dotenv()


SNOWFLAKE_CONFIG = {
    "user": os.getenv("SNOWFLAKE_USER"),
    "password": os.getenv("SNOWFLAKE_PASSWORD"),
    "account": os.getenv("SNOWFLAKE_ACCOUNT"),
    "warehouse": os.getenv("SNOWFLAKE_WAREHOUSE"),
    "database": os.getenv("SNOWFLAKE_DATABASE"),
    "schema": os.getenv("SNOWFLAKE_SCHEMA"),
}




@mcp.tool()
def generate_sql_query(question: str, plant_name: str) -> str:

    """
    Generates an optimized Snowflake SQL query based on a natural language question and plant name.
    Returns only the SQL query string (no additional commentary).
    """
    
    schema = schema_definition()
    now = datetime.now()
    year = now.year
    month = now.month
    day = now.day
    hour = now.hour 
    return f"""
You are an expert SQL agent designed to convert natural language questions into optimized SQL queries.
now your task is to generate sql query based on the question and plant name provided to you. here is question: {question} and plant_name: {plant_name}.

### Context:
You have access to two tables:

#### 1. `RLS_VW_SOLAR_PLANT_METRICS_HOURLY`  for solar plant metrics (hourly records)
#### 2. `RLS_VW_WIND_PLANT_METRICS_HOURLY`  for wind plant metrics (hourly records)

Each plant has only one type of data source (either solar or wind). Refer to the following mapping to determine which table to query from:
- If `Plant_Name` matches a solar client, query from the `RLS_VW_SOLAR_PLANT_METRICS_HOURLY`.
- If `Plant_Name` matches a wind client, query from the `RLS_VW_WIND_PLANT_METRICS_HOURLY`.

### Query Timestamp:
Question was asked on: {year}-{month:02}-{day:02} at {hour:02}:00.

here is the schema of the tables:
{schema}

Use the relevant fields like:
- `DATEVALUE` for date-based filtering
- `DATETIMESTAMP` for datetime-based filtering
- `PLANT_LONG_NAME` for identifying the plant
- `PLANT_GENERATION` or `ACTUAL_GENERATION` for energy generation metrics
- `WIND_SPEED_MEASURED` for wind speed
- `DAILY_ENERGY` or `TOTAL_ENERGY_COMPUTED` for daily totals (solar)

---

### Input Format:
Each input consists of:
- A natural language **Question**
- A **Plant_Name**

### Your task:
Based on the question and plant name, generate a valid and optimized SQL query using Snowflake SQL syntax.

---

### Example Inputs:

#### Input 1:
Question: What is the yesterday generation  
Plant_Name: Kids Clinic India Limited

#### Output 1:
```sql
SELECT DATEVALUE, SUM(PLANT_GENERATION) AS TOTAL_GENERATION
FROM RLS_VW_SOLAR_PLANT_METRICS_HOURLY
WHERE PLANT_LONG_NAME = 'Kids Clinic India Limited'
  AND DATEVALUE = CURRENT_DATE() - 1
GROUP BY DATEVALUE;
```

Input 2:
Question: What is this month generation
Plant_Name: M/s SAA AB ENGINEERING PVT LTD

#### Output 2:
```sql
SELECT MONTH_NAME, SUM(ACTUAL_GENERATION) AS TOTAL_GENERATION
FROM RLS_VW_WIND_PLANT_METRICS_HOURLY
WHERE PLANT_LONG_NAME = 'M/s SAA AB ENGINEERING PVT LTD'
  AND MONTH_NO = MONTH(CURRENT_DATE())
  AND YEAR_NO = YEAR(CURRENT_DATE())
GROUP BY MONTH_NAME;
```

### Input 3:
Question: What is the average wind speed of this month
Plant_Name: Sipani Fibres Ltd

```sql
SELECT AVG(DENSITY_CORRECTED_WIND_SPEED) AS AVG_WIND_SPEED_THIS_MONTH
FROM RLS_VW_WIND_PLANT_METRICS_HOURLY
WHERE PLANT_LONG_NAME = 'Sipani Fibres Ltd'
  AND MONTH_NO = MONTH(CURRENT_DATE())
  AND YEAR_NO = YEAR(CURRENT_DATE());
```


### Input 4:
Question: What is the average PR of this month
Plant_Name: KSIPL_Solar_13.5MWp

```sql
  SELECT AVG(PERFORMANCE_RATIO_MEASURED) AS AVG_PR_THIS_MONTH
FROM RLS_VW_SOLAR_PLANT_METRICS_HOURLY
WHERE PLANT_LONG_NAME = 'KSIPL_Solar_13.5MWp'
  AND MONTH_NO = MONTH(CURRENT_DATE())
  AND YEAR(DATEVALUE) = YEAR(CURRENT_DATE());
```
"""
    
    

@mcp.tool()
def query_snowflake(sql_query: str) -> str:
    """
    Executes the given SQL query on Snowflake and returns results as a JSON string.
    """
    try:
        ctx = snowflake.connector.connect(**SNOWFLAKE_CONFIG)
        cs = ctx.cursor()
        cs.execute(sql_query)
        rows = cs.fetchall()
        columns = [desc[0] for desc in cs.description]
        cs.close()
        ctx.close()

        # Convert results to list of dicts
        records = [dict(zip(columns, row)) for row in rows]
        return json.dumps(records, default=str)
    except Exception as e:
        error_msg = str(e)
        return json.dumps({"error": error_msg})



@mcp.tool()
def output_generation(question: str, sql_query: str, sql_result: str, plant_name: str) -> str:
    """
    Generates a concise, 2-3 line human-readable summary of the SQL query results for a given plant.
    If data is unavailable or an error occurred, returns a brief message.
    """
    try:
        result = json.loads(sql_result)
    except Exception:
        return f"No valid data could be retrieved for {plant_name} regarding: {question}."

    # Handle error or empty result
    if isinstance(result, dict) and "error" in result:
        return f"Data for {plant_name} could not be retrieved due to an error: {result['error']}"
    if not result or (isinstance(result, list) and len(result) == 0):
        return f"No data available for {plant_name} regarding: {question}."

    # If result is a list of records, summarize the first record
    if isinstance(result, list) and len(result) > 0 and isinstance(result[0], dict):
        record = result[0]
        # Try to extract key metrics
        metrics = []
        for k, v in record.items():
            if isinstance(v, (int, float)) and v is not None:
                metrics.append(f"{k.replace('_', ' ').title()}: {v}")
            elif isinstance(v, str) and v.strip() and k.lower() not in ["plant_long_name"]:
                metrics.append(f"{k.replace('_', ' ').title()}: {v}")
        metrics_str = "; ".join(metrics[:3])  # Only show up to 3 key metrics

        return (
            f"For {plant_name}, in response to '{question}', the key result is: {metrics_str}."
            if metrics_str else
            f"Data for {plant_name} is available, but no key metrics could be extracted for: {question}."
        )

    # Fallback
    return f"Summary for {plant_name} regarding '{question}': Data processed, but no clear metrics found."


def schema_definition():
    """
    This is my schema definition function.
    It provides the schema of the tables used in the SQL queries.
    It includes the table names, descriptions, and the columns with their data types and descriptions.
    """
    Table_Schema ="""
TABLE 1 : 
- Table Name : RLS_VW_SOLAR_PLANT_METRICS_HOURLY 
- Table Description : This table contains hourly metrics for solar plants, so there is a record for each hour.
- Given Below is the schema of the table (it is in the form of column name, its data type, and its description if available):
    - INSOLATION, FLOAT (The amount of solar radiation received per unit area at the plant.)
    - NO_OF_SAMPLES_WITH_IRR, NUMBER (Number of samples with irradiance data recorded.)
    - DAY_NAME, TEXT (Name of the day for the entry, e.g., 'Monday', 'Tuesday'.)
    - PLANT_ID, NUMBER (Unique ID for the solar plant.)
    - SPECIFIC_POWER, FLOAT (Energy output per unit of installed capacity.)
    - PLANT_GRID_AVAILABILITY, FLOAT (Percentage of time the plant grid is available.)
    - TIME_BASED_AVAILABILITY, FLOAT (Availability of plant based on time.)
    - PLANT_ENERGY_PERFORMANCE_INDEX_EPI, FLOAT (Performance index of the plant’s energy generation.)
    - PLANT_DC_CAPACITY, FLOAT (DC capacity of the plant in MW.)
    - TEMPERATURE_CORRECTED_PR, FLOAT (Temperature-corrected performance ratio.)
    - EXPECTED_GENERATION, FLOAT (Theoretical generation expected for the given period.)
    - DATETIMESTAMP, TIMESTAMP_NTZ (Timestamp of the data entry.)
    - DAY_NO, NUMBER (Day number in the month, ranges from 1 to 31.)
    - ACCOUNT_ID, NUMBER (Account ID linked to the plant.)
    - PLANT_AVG_ACTIVE_POWER, FLOAT (Average active power generated by the plant.)
    - PLANT_LONG_NAME, TEXT (The long name of the plant.)
    - POA_IRRADIANCE, FLOAT (Plane of array irradiance, averaged over an hour.)
    - PLANT_GRID_UNAVAILABLE_MINUTES, FLOAT (Minutes the plant was grid-unavailable.)
    - AVAILABLE_HRS, FLOAT (Total available hours for generation.)
    - TOTAL_ENERGY_COMPUTED, FLOAT (Total energy computed from the plant’s data.)
    - PLANT_ENERGY_AVAILABILITY, FLOAT (Energy availability percentage.)
    - PR_ASSUMED_OR_ESTIMATED, FLOAT (Assumed performance ratio for the plant.)
    - PLANT_AVAILABLE_MINUTES, FLOAT (Total minutes the plant was available for operation.)
    - PLANT_MODULE_TEMP, FLOAT (Temperature of the plant's modules.)
    - PLANT_SHORT_NAME, TEXT (Short name of the plant.)
    - DATEVALUE, DATE (Date of the entry.)
    - OIL_SAVED, FLOAT (Amount of oil saved due to the plant's energy generation.)
    - PLANT_EXPORT_ENERGY, FLOAT (Energy exported from the plant to the grid.)
    - PLANT_DC_CUF, FLOAT (Capacity utilization factor for the DC capacity.)
    - PLANT_GENERATION, FLOAT (Actual energy generated during the period.)
    - PLANT_GHI_IRRADIANCE, FLOAT (Global Horizontal Irradiance.)
    - ACCOUNT_NAME, TEXT (Account name.)
    - PLANT_PRODUCTION_LOSS, FLOAT (Energy lost due to inefficiencies in generation.)
    - MONTH_NAME, TEXT (Name of the month for the entry, e.g., 'January', 'February'.)
    - GENERATION_HRS, FLOAT (Total hours of generation during the period.)
    - NO_OF_SAMPLES_PER_HOUR, NUMBER (Number of samples collected per hour.)
    - NO_OF_SAMPLES_WITH_GEN, NUMBER (Number of samples with generation data.)
    - HOUR_NO, NUMBER (Hour of the day, ranging from 0 to 23.)
    - DAILY_ENERGY, FLOAT (Total energy generated on a given day.)
    - CO2_REDUCED, FLOAT (Amount of CO2 reduced due to the plant's generation.)
    - SUM_ACTIVE_POWER_FOR_EFF, FLOAT (Total active power used for efficiency calculations.)
    - PLANT_ACTIVE_POWER_SETPOINT, FLOAT (Setpoint for active power output.)
    - SUM_PV_POWER_FOR_EFF, FLOAT (Total PV power used for efficiency calculations.)
    - TREES_SAVED, FLOAT (Number of trees saved due to energy generation.)
    - INVERTER_EFFICIENCY, FLOAT (Efficiency of the inverter.)
    - COAL_SAVED, FLOAT (Amount of coal saved due to the plant's generation.)
    - PLANT_IMPORT_ENERGY, FLOAT (Energy imported to the plant from the grid.)
    - CAPACITY_BASED_AVAILABILITY, FLOAT (Availability based on plant’s capacity.)
    - MONTH_NO, NUMBER (Month number, ranging from 1 to 12.)
    - SPECIFIC_YIELD, FLOAT (Energy generated per unit area.)
    - DAILY_POA_ENERGY, FLOAT (Daily plane of array energy.)
    - PLANT_MODULE_TEMP_COMPUTED, FLOAT (Computed temperature of the plant's modules.)
    - PERFORMANCE_RATIO_MEASURED, FLOAT (Measured performance ratio.)
    - PLANT_TOTAL_ENERGY_LAST_VALUE, FLOAT (Total energy generated for the last value recorded.)
    - PLANT_EXPECTED_POWER_CALCULATED, FLOAT (Calculated expected power from the plant.)
    - PLANT_DAILY_GHI_ENERGY, FLOAT (Daily GHI energy generated by the plant.)

TABLE 2:
- Table Name : RLS_VW_WIND_PLANT_METRICS_HOURLY
- Table Description : This table contains hourly metrics for wind plants, so there is a record for each hour.
- Given Below is the schema of the table (it is in the form of column name, its data type, and its description if available):
    - ACTIVE_POWER, FLOAT (Energy generated by the wind turbine.)
    - EXPECTED_GENERATION_FULL_PERFORMANCE, FLOAT (Expected generation assuming full performance.)
    - GRID_AVAILABILITY, NUMBER (Percentage of time the grid is available.)
    - PLANT_TURBINE_EFFICIENCY, FLOAT (Efficiency of the wind turbine.)
    - ENERGY_BASED_AVAILABILITY, FLOAT (Energy availability based on the plant.)
    - DAY_NAME, TEXT (Name of the day, e.g., 'Monday', 'Tuesday'.)
    - WIND_AVAILABLE_MINUTES, NUMBER (Minutes the wind plant was available.)
    - MONTH_NO, NUMBER (Month number, ranging from 1 to 12.)
    - YEAR_NO, NUMBER (Year number, ranging from 2022 to 2025.)
    - WIND_SPEED_ESTIMATED, FLOAT (Estimated wind speed.)
    - EXPECTED_PLF, FLOAT (Expected Plant Load Factor.)
    - PLANT_LONG_NAME, TEXT (The long name of the plant.)
    - BUDGET_VS_ACTUAL, FLOAT (Budgeted vs actual generation.)
    - GRID_UNAVAILABLE_MINUTES, NUMBER (Minutes the plant was grid-unavailable.)
    - TEMPERATURE_CORRECTED_WIND_SPEED, FLOAT (Wind speed corrected for temperature.)
    - STATUS_BASED_AVAILABLE_SAMPLES, FLOAT (Number of available samples based on status.)
    - PLANT_ID, NUMBER (Unique ID for the wind plant.)
    - STATUS_BASED_TOTAL_SAMPLES, FLOAT (Total number of samples based on status.)
    - AVAILABLE_SAMPLES, NUMBER (Number of available samples.)
    - RESOURCE_AVAILABILITY, NUMBER (Availability of wind resources.)
    - TURBINE_UNAVAILABLE_MINUTES, NUMBER (Minutes the turbine was unavailable.)
    - LOSS_PRODUCTION, FLOAT (Energy loss due to inefficiencies.)
    - ACCOUNT_NAME, TEXT (Account name.)
    - ACTUAL_GENERATION, FLOAT (Actual energy generated.)
    - DATEVALUE, DATE (Date of the entry.)
    - DATETIMESTAMP, TIMESTAMP_NTZ (Timestamp for the data entry.)
    - WIND_SPEED_WITH_NTF, FLOAT (Wind speed with NTF correction.)
    - BUDGETED_GENERATION, FLOAT (Generation budget.)
    - EXPECTED_SAMPLES, NUMBER (Expected number of samples.)
    - STATUS_BASED_TIME_AVAILABILITY, FLOAT (Time-based availability based on status.)
    - PLANT_DATA_AVAILABILITY, NUMBER (Data availability for the plant.)
    - EXPECTED_GENERATION, FLOAT (Expected generation from the plant.)
    - TIME_BASED_MACHINE_AVAILABILITY, NUMBER (Machine availability based on time.)
    - TURBINE_AVAILABLE_MINUTES, NUMBER (Minutes the turbine was available.)
    - DENSITY_CORRECTED_WIND_SPEED, FLOAT (Wind speed corrected for air density.)
    - WIND_UNAVAILABLE_MINUTES, NUMBER (Minutes the wind plant was unavailable.)
    - MONTH_NAME, TEXT (Name of the month.)
    - DAY_NO, NUMBER (Day number in the month.)
    - CAPACITY_BASED_AVAILABILITY, FLOAT (Availability based on capacity.)
    - PLANT_SHORT_NAME, TEXT (Short name of the plant.)
    - ACTUAL_GENERATION_FULL_PERFORMANCE, FLOAT (Generation at full performance.)
    - ACTUAL_PLF, FLOAT (Actual Plant Load Factor.)
    - HOUR_NO, NUMBER (Hour of the day.)
    - CAPACITY, FLOAT (Capacity of the wind turbine in MW.)
    - GRID_AVAILABLE_MINUTES, NUMBER (Minutes the grid was available.)
    - WIND_SPEED_MEASURED, FLOAT (Measured wind speed at the turbine.)
    - ACCOUNT_ID, NUMBER (Account ID linked to the plant.)

List of Clients and Type of Plant they Possess (clients with a Solar Plant will have their data stored in RLS_VW_SOLAR_PLANT_METRICS_HOURLY, and similarly for a client with a Wind Plant will have their data stored in RLS_VW_WIND_PLANT_METRICS_HOURLY ):
-  'MPL_Wind_2MW': WIND
-  'QEPL_Wind_2MW': WIND
-  'Jodhani-2 Papers Private Limited': SOLAR
-  'Sipani Fibres Ltd': WIND
-  'M/s SAA AB ENGINEERING PVT LTD': WIND
-  'A N S Paper Mills Pvt Ltd': WIND
-  'Kingyarn': WIND
-  'Khayati Steel Industries Private Limited': WIND
-  'Balaji Malts Private Limited': WIND
-  'Jodhani-1 Papers Private Limited': SOLAR
-  'CIL_Wind_2MW': WIND
-  'Klene Paks Limited': WIND
-  'Balaji-1 Malts Private Limited': SOLAR
-  'Avon Plastic Industries Pvt Ltd': WIND
-  'Balaji-2 Malts Private Limited': SOLAR
-  'Klene Paks Ltd Solar': SOLAR
-  'SAA AB 3.1 solar power plant': SOLAR
-  'Nandan Industries Private Limited': WIND
-  'Sidwin fabric Pvt. Ltd.': WIND
-  'Kids Clinic India Limited': SOLAR
-  'Stovekraft Ltd Solar2': SOLAR
-  'Graphite India Limited Solar': SOLAR
-  'Milan Ginnings': WIND
-  'Eastern Silk mill': WIND
-  'StoveKraft Limited - Unit-3': SOLAR
-  'Stovekraft Limited': WIND
-  'KSIPL_Solar_13.5MWp': SOLAR
-  'SSPL_Wind_2MW': WIND
-  'Sustainable Spinning & Commodities': WIND
-  'KPL_Solar_6MWp': SOLAR
-  'Jodhani Papers Pvt Ltd': WIND
-  'Stovekraft Ltd Solar': SOLAR
-  'NDL_Wind_2MW': WIND
"""
    return Table_Schema


if __name__ == "__main__":
    mcp.run()
    print("MCP server is running...")
