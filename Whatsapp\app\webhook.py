import os
from flask import request
from whatsapp.message_extraction import extract_whatsapp_message_info
from DB.db_ops import fetch_messages_by_message_id
from helper.logger_setup import setup_logger
from whatsapp.sender_whatsapp import send_msg
from app.process_whatsapp import *
logging = setup_logger('webhook', 'webhook.log')

VERIFY_TOKEN = os.getenv('VERIFY_TOKEN')


# def handle_webhook():
#     if request.method == 'POST':
#         try:
#             message_details = request.json
#             logging.info(f"Received message: {message_details}")

#             # Validate incoming data structure
#             if not message_details or 'entry' not in message_details:
#                 logging.error("Invalid message structure: Missing 'entry'")
#                 return jsonify({"error": "Invalid message structure"}), 400

#             changes = message_details['entry'][0].get('changes', [])
#             if not changes:
#                 logging.error("Invalid message structure: Missing 'changes'")
#                 return jsonify({"error": "Invalid message structure"}), 400

#             value = changes[0].get('value', {})
#             if 'messages' in value:
#                 messages = value['messages'][0]
#                 contacts = value.get('contacts', [{}])[0]

#                 incoming_num = messages.get('from')
#                 incoming_name = contacts.get('profile', {}).get('name')
#                 incoming_message = messages.get('text', {}).get('body')

#                 if not all([incoming_num, incoming_name, incoming_message]):
#                     logging.error("Missing message details")
#                     return jsonify({"error": "Incomplete message data"}), 400

#                 # Output for debugging
#                 print(f'incoming_num = {incoming_num}')
#                 print(f'incoming_name = {incoming_name}')
#                 print(f'incoming_message = {incoming_message}')
            
#             elif 'statuses' in value:
#                 logging.info(f"Received status update: {value['statuses']}")
#             else:
#                 logging.warning("Unknown message type received")

#             return jsonify({"message": "Success"}), 200

#         except KeyError as e:
#             logging.error(f"Missing key in message: {e}")
#             return jsonify({"error": f"Missing key: {e}"}), 400

#         except Exception as e:
#             logging.exception(f"Error processing webhook: {e}")
#             return jsonify({"error": "Internal server error"}), 500


def verify_webhook():
    logging.info("Webhook initialized")
    mode = request.args.get('hub.mode')
    challenge = request.args.get('hub.challenge')
    token = request.args.get('hub.verify_token')

    if mode and token:
        if mode == "subscribe" and token == VERIFY_TOKEN:
            logging.info("Webhook initialized Successfully")
            return challenge, 200
        else:
            logging.info("Webhook Verification failed")
            return "Verification failed", 403



def handle_webhook():
	if request.method == 'POST':
		message_details = request.json
		logging.info(f"Received message: {message_details}")
		try:
			incoming_num, button_text, message_id = extract_whatsapp_message_info(message_details)
			if button_text and message_id:
				if button_text == 'Yes':
					user_data = fetch_messages_by_message_id(message_id)
					record = user_data[0]
					# Assign values to variables
					report_type = record['report_type']
					msg_id = record['id']
					incoming_num = record['wa_id']
					message_id = record['message_id']
					
					plant_short_name = record['plant_short_name']
					plant_long_name = record['plant_long_name']
					date = record['date']
					
					if report_type == 'solar':
						
						
						send_yes_solar(
							plant_short_name, plant_long_name, str(date), report_type, incoming_num
						)
						
					elif report_type == 'wind':
						
							send_yes_wind(
								plant_short_name, plant_long_name, str(date), report_type, incoming_num
							)

					elif report_type == 'combined':
						
						send_yes_combined(
							plant_short_name, plant_long_name, str(date), report_type, incoming_num
						)

				elif button_text == 'No':
					send_msg(incoming_num, "Thank you for using our service.")
								

		except Exception as e:
			print("error", e)
		return 'success', 200
	else:
		return 'success', 200
