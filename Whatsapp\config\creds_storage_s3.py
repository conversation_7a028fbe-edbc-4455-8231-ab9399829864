import os
from dotenv import load_dotenv

load_dotenv()

# Environment variable to switch between local and S3
USE_S3 = os.getenv("USE_S3", "False").lower() == "true"

if USE_S3:
    AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
    AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
    AWS_STORAGE_BUCKET_NAME = os.getenv("AWS_STORAGE_BUCKET_NAME")
    AWS_REGION = os.getenv("AWS_REGION", "us-east-1")
    S3_BASE_URL = f"https://{AWS_STORAGE_BUCKET_NAME}.s3.{AWS_REGION}.amazonaws.com"

STATIC_FOLDER = "static"
