###################################################################################################
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend to avoid GUI issues
import matplotlib.pyplot as plt
import matplotlib.image as mpimg
from matplotlib.backends.backend_pdf import PdfPages
import matplotlib.patches as patches
import pandas as pd
import re
import os
from helper.logger_setup import setup_logger

logger = setup_logger('solar_plot', 'solar_plot.log')


def plot_yearly_pr(dataframe, plant_name, date_col="time", pr_col=None, title="Last 12 Months Performance Ratio (PR%)"):
    """
    Plots monthly average Performance Ratio (PR%) for the last 12 months with an average line and value labels.
    Missing months are filled with 0 PR%.
    """
    try:
        logger.info("Starting plot_yearly_pr function")

        # Determine PR column if not specified
        if not pr_col:
            pr_col = "pr" if "pr" in dataframe.columns else dataframe.columns[1]

        if date_col not in dataframe.columns or pr_col not in dataframe.columns:
            raise ValueError("Provided column names do not exist in the dataframe.")

        df = dataframe.copy()
        df[date_col] = pd.to_datetime(df[date_col], errors='coerce').dt.tz_localize(None)
        df = df.dropna(subset=[date_col])

        # Ensure data spans last 12 months
        end_date = df[date_col].max()
        start_date = end_date - pd.DateOffset(months=11)  # Start date for last 12 months
        df = df[(df[date_col] >= start_date) & (df[date_col] <= end_date)]

        df["month_year"] = df[date_col].dt.strftime('%b %y')
        df["year_month"] = df[date_col].dt.to_period("M")

        # Group by available months
        df_monthly = df.groupby(["year_month", "month_year"])[pr_col].mean().reset_index()

        # Create full list of months
        all_months = pd.date_range(start=start_date, end=end_date, freq='MS').to_period("M")
        month_year_labels = [dt.strftime('%b %y') for dt in all_months.to_timestamp()]
        full_df = pd.DataFrame({
            "year_month": all_months,
            "month_year": month_year_labels
        })

        # Merge and fill missing months with 0
        df_monthly = pd.merge(full_df, df_monthly, on=["year_month", "month_year"], how="left")
        df_monthly[pr_col].fillna(0, inplace=True)
        df_monthly = df_monthly.sort_values("year_month")

        overall_avg = df_monthly[pr_col].mean()

        plt.figure(figsize=(12, 6))

        # Line plot
        plt.plot(df_monthly["month_year"], df_monthly[pr_col],
                 marker='o', linestyle='-', color='blue',
                 markersize=8, linewidth=2, label='Monthly PR%')

        # Average line
        plt.axhline(y=overall_avg, color='red', linestyle='dashed',
                    linewidth=2, label=f'Avg: {overall_avg:.2f}%')

        # Add value labels
        for x, y in zip(df_monthly["month_year"], df_monthly[pr_col]):
            plt.text(x, y + 1, f"{y:.1f}%",
                     ha='center', va='bottom', fontsize=9,
                     color='black', rotation=90)

        plt.xlabel("Month-Year")
        plt.ylabel("Performance Ratio (%)")
        plt.title(title)
        plt.legend(loc='upper right')
        plt.xticks(rotation=45)
        plt.grid(axis='y', linestyle='--', alpha=0.7)

        y_min = max(0, min(df_monthly[pr_col]) - 5)
        y_max = min(100, max(df_monthly[pr_col]) + 5)
        plt.ylim(y_min, y_max)

        output_filename = "static/plots_solar/" + f"{plant_name}_yearly_pr.png"
        plt.savefig(output_filename, dpi=150, bbox_inches='tight')
        plt.close()
        logger.info(f"Yearly PR plot saved to {output_filename}")
        return output_filename

    except Exception as e:
        logger.error(f"Error in plot_yearly_pr: {e}")
        raise


def plot_yearly_generation(dataframe, plant_name, date_col="time", title="Last 12 Months Energy Generation"):
    """
    Plots monthly energy generation with an average line and value labels inside bars.
    """
    try:
        logger.info("Starting plot_yearly_generation function")

        energy_col = dataframe.columns[1]
        if date_col not in dataframe.columns or energy_col not in dataframe.columns:
            raise ValueError("Provided column names do not exist in the dataframe.")

        df = dataframe.copy()
        df[date_col] = pd.to_datetime(df[date_col]).dt.tz_localize(None)
        df["month_year"] = df[date_col].dt.strftime('%b %y')
        df["year_month"] = df[date_col].dt.to_period("M")
        df_monthly = df.groupby(["year_month", "month_year"])[energy_col].sum().reset_index()
        df_monthly = df_monthly.sort_values("year_month")

        overall_avg = df_monthly[energy_col].mean()

        plt.figure(figsize=(12, 6))
        bars = plt.bar(df_monthly["month_year"], df_monthly[energy_col], color='royalblue', alpha=0.7, label='Monthly Generation')
        plt.axhline(y=overall_avg, color='r', linestyle='--', label=f'Avg: {overall_avg:.2f} KWh')

        # Add value labels on bars

        for bar in bars:
            height = bar.get_height()
            if height > 0:
                if height < overall_avg * 0.5:  # Small bars - label above
                    plt.text(bar.get_x() + bar.get_width() / 2, height + (overall_avg * 0.05), f"{height:.2f}",
                             ha='center', va='bottom', fontsize=8, color='black')
                else:  # Larger bars - label inside
                    plt.text(bar.get_x() + bar.get_width() / 2, height - (height * 0.1), f"{height:.2f}",
                             ha='center', va='top', fontsize=8, color='black')

        plt.xlabel("Month-Year")
        plt.ylabel("Total Energy Generated (kWh)")
        plt.legend()
        plt.xticks(rotation=45)

        output_filename = "static/plots_solar/" + f"{plant_name}_yearly_generation.png"
        plt.savefig(output_filename, dpi=150, bbox_inches='tight')
        plt.close()
        logger.info(f"Yearly generation plot saved to {output_filename}")
        return output_filename

    except Exception as e:
        logger.error(f"Error in plot_yearly_generation: {e}")
        raise


def plot_pr_monthly(dataframe, plant_name, date_col="time", title="PR% Over Last 30 Days"):
    """
    Plots PR% over time with an average line and value labels.
    """
    try:
        logger.info("Starting plot_pr_monthly function")

        pr_col = dataframe.columns[1]
        if date_col not in dataframe.columns or pr_col not in dataframe.columns:
            raise ValueError("Provided column names do not exist in the dataframe.")

        df = dataframe.copy()
        df[date_col] = pd.to_datetime(df[date_col], errors='coerce')
        df = df.dropna(subset=[date_col, pr_col])
        df[date_col] = df[date_col].dt.tz_localize(None)

        # Define last 30 days range
        end_date = df[date_col].max()
        start_date = end_date - pd.Timedelta(days=29)

        # Create a complete date range for 30 days
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        df = df.set_index(date_col).reindex(date_range).reset_index()
        df.columns = ['date', pr_col]

        average_pr = df[pr_col].mean()

        # Format dates for better display
        df['date_formatted'] = df['date'].dt.strftime('%m-%d')

        plt.figure(figsize=(12, 6))
        plt.plot(df['date_formatted'], df[pr_col], marker='o', linestyle='-', color='blue', label="PR%")
        plt.axhline(average_pr, color='red', linestyle='dashed', linewidth=2, label=f"Avg PR%: {average_pr:.2f}")

        # Add value labels on each point (every 3rd point to avoid crowding)
        for i, txt in enumerate(df[pr_col]):
            if not pd.isna(txt) and i % 3 == 0:
                plt.text(i, txt + 1, f"{txt:.2f}", ha='center', fontsize=9, color='black', rotation=45)

        plt.xlabel("Date")
        plt.ylabel("PR%")
        plt.xticks(rotation=45)

        # Show every 3rd label to avoid crowding
        ax = plt.gca()
        for i, label in enumerate(ax.get_xticklabels()):
            if i % 3 != 0:
                label.set_visible(False)

        plt.legend()
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        plt.ylim(40, 100)

        output_filename = "static/plots_solar/" + f"{plant_name}_daily_pr.png"
        plt.savefig(output_filename, dpi=150, bbox_inches='tight')
        plt.close()
        logger.info(f"PR plot saved to {output_filename}")
        return output_filename

    except Exception as e:
        logger.error(f"Error in plot_pr_monthly: {e}")
        raise


def plot_monthly_generation(dataframe, plant_name, date_col="time", title="Daily Generation Over Last 30 Days"):
    """
    Plots daily generation with an average line and value labels inside bars.
    """
    try:
        logger.info(f"Starting plot_monthly_generation function for {plant_name}")

        energy_col = dataframe.columns[1]
        if date_col not in dataframe.columns or energy_col not in dataframe.columns:
            raise ValueError("Provided column names do not exist in the dataframe.")

        df = dataframe.copy()
        df[date_col] = pd.to_datetime(df[date_col])

        # Generate last 30 days range
        end_date = df[date_col].max()
        start_date = end_date - pd.Timedelta(days=29)
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')

        # Reindex to fill missing dates with 0
        df = df.set_index(date_col).reindex(date_range).fillna(0).reset_index()
        df.columns = ['date', energy_col]

        average_generation = df[energy_col].mean()

        # Format dates for better display
        df['date_formatted'] = df['date'].dt.strftime('%m-%d')

        plt.figure(figsize=(12, 6))
        bars = plt.bar(df['date_formatted'], df[energy_col], color='skyblue', label="Daily Generation")
        plt.axhline(average_generation, color='red', linestyle='dashed', linewidth=2,
                    label=f"Avg: {average_generation:.2f} (KWh)")

        # Add value labels inside the bars (vertical orientation)
        for bar in bars:
            height = bar.get_height()
            if height != 0:
                plt.text(bar.get_x() + bar.get_width() / 2, height * 0.5, f"{height:.2f}",
                         ha='center', va='center', fontsize=10, color='black', rotation=90)

        plt.xlabel("Date")
        plt.ylabel("Daily Generation (kWh)")
        plt.xticks(rotation=45)

        # Show every 3rd label to avoid crowding
        ax = plt.gca()
        for i, label in enumerate(ax.get_xticklabels()):
            if i % 3 != 0:
                label.set_visible(False)

        plt.legend()
        plt.grid(axis='y', linestyle='--', alpha=0.7)

        # Save the plot
        output_filename = "static/plots_solar/" + f"{plant_name}_daily_generation.png"
        plt.savefig(output_filename, dpi=150, bbox_inches='tight')
        plt.close()
        logger.info(f"Daily generation plot saved to {output_filename}")
        return output_filename

    except Exception as e:
        logger.error(f"Error in plot_monthly_generation: {e}")
        raise


def get_clean_filename(img_file):
    """Extracts the filename without the dynamic prefix."""
    filename = os.path.basename(img_file)  # Extract filename from full path
    match = re.search(r"[^_]+_(.*)", filename)  # Remove everything before first underscore
    return match.group(1) if match else filename  # Return cleaned filename


def generate_solar_plot(image_files, data_values, pdf_filename, custom_titles, customer_name, Date):
    logger.info("Starting to generate solar plot PDF...")

    try:
        title_header = f"Daily Generation Report | Customer: {customer_name} | Date: {Date}"
        max_title_length = 60
        if len(title_header) > max_title_length:
            title_header = f"Daily Generation Report\nCustomer: {customer_name} | Date: {Date}"

        with PdfPages(pdf_filename) as pdf:
            # Improved figure size and spacing for better alignment
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle(title_header, fontsize=18, fontweight="bold", y=0.95)

            for ax, img_file, title in zip(axes.flat, image_files, custom_titles):
                try:
                    logger.info(f"Loading image: {img_file}")
                    img = mpimg.imread(img_file)
                    ax.imshow(img, aspect='auto')  # Use auto aspect for better fitting
                    ax.axis("off")
                    ax.set_title(title, fontsize=16, fontweight="bold", pad=15)

                    # Extract clean filename for lookup
                    clean_filename = os.path.basename(img_file)
                    clean_filename = get_clean_filename(clean_filename)
                    logger.info(f"Clean filename for data lookup: {clean_filename}")

                    if clean_filename in ["daily_generation.png", "daily_pr.png"]:
                        daily_value, daily_unit = data_values.get(clean_filename, ("N/A", ""))
                        monthly_key = clean_filename.replace("daily", "monthly")
                        monthly_value, monthly_unit = data_values.get(monthly_key, ("N/A", ""))

                        box_width, box_height = 0.3, 0.08
                        spacing = 0.15
                        daily_label_x, daily_label_y = 0.25, -0.24 - spacing
                        monthly_label_x, monthly_label_y = 0.55, -0.24 - spacing

                        value_name = "Daily Generation" if clean_filename == "daily_generation.png" else "AVG Daily PR"
                        ax.text(daily_label_x + box_width / 2, daily_label_y + spacing, value_name, transform=ax.transAxes,
                                ha="center", fontsize=12, fontweight="bold", color="black")

                        daily_box = patches.Rectangle(
                            (daily_label_x, daily_label_y), box_width, box_height, transform=ax.transAxes,
                            edgecolor="black", facecolor="white", linewidth=2
                        )
                        ax.add_patch(daily_box)
                        ax.text(daily_label_x + box_width / 2, daily_label_y + 0.04, f"{daily_value} {daily_unit}",
                                transform=ax.transAxes, ha="center", fontsize=12, fontweight="bold", color="black",
                                bbox=dict(facecolor="white", edgecolor="black", boxstyle="round,pad=0.3"))

                        value_name = "Monthly Generation" if clean_filename == "daily_generation.png" else "AVG Monthly PR"
                        num = 0.1 if clean_filename == "daily_generation.png" else 0
                        ax.text(monthly_label_x + num + box_width / 2, monthly_label_y + spacing, value_name, transform=ax.transAxes,
                                ha="center", fontsize=12, fontweight="bold", color="black")

                        monthly_box = patches.Rectangle(
                            (monthly_label_x, monthly_label_y), box_width, box_height, transform=ax.transAxes,
                            edgecolor="black", facecolor="white", linewidth=2
                        )
                        ax.add_patch(monthly_box)
                        ax.text(monthly_label_x + num + box_width / 2, monthly_label_y + 0.04, f"{monthly_value} {monthly_unit}",
                                transform=ax.transAxes, ha="center", fontsize=12, fontweight="bold", color="black",
                                bbox=dict(facecolor="white", edgecolor="black", boxstyle="round,pad=0.3"))

                    elif clean_filename in ["yearly_generation.png", "yearly_pr.png"]:
                        yearly_value, yearly_unit = data_values.get(clean_filename, ("N/A", ""))
                        logger.info(f"Yearly value for {clean_filename}: {yearly_value} {yearly_unit}")

                        box_width, box_height = 0.3, 0.08
                        spacing = 0.15
                        yearly_label_x, yearly_label_y = 0.4, -0.24 - spacing

                        value_name = "Yearly Generation" if clean_filename == "yearly_generation.png" else "AVG Yearly PR"
                        ax.text(yearly_label_x + box_width / 2, yearly_label_y + spacing, value_name, transform=ax.transAxes,
                                ha="center", fontsize=12, fontweight="bold", color="black")

                        yearly_box = patches.Rectangle(
                            (yearly_label_x, yearly_label_y), box_width, box_height, transform=ax.transAxes,
                            edgecolor="black", facecolor="white", linewidth=2
                        )
                        ax.add_patch(yearly_box)
                        ax.text(yearly_label_x + box_width / 2, yearly_label_y + 0.04, f"{yearly_value} {yearly_unit}",
                                transform=ax.transAxes, ha="center", fontsize=12, fontweight="bold", color="black",
                                bbox=dict(facecolor="white", edgecolor="black", boxstyle="round,pad=0.3"))

                except FileNotFoundError:
                    logger.error(f"File not found: {img_file}")
                except Exception as e:
                    logger.error(f"Error loading image {img_file}: {e}")

            # Improved layout with better spacing and margins
            plt.subplots_adjust(left=0.05, right=0.95, top=0.90, bottom=0.05,
                              hspace=0.25, wspace=0.15)
            pdf.savefig(fig, dpi=300, bbox_inches='tight', facecolor='white',
                       pad_inches=0.2)
            plt.close(fig)
            logger.info(f"PDF saved to {pdf_filename}")
            return pdf_filename

    except Exception as e:
        logger.critical(f"Failed to generate PDF: {e}")
        raise
