"""
Enhanced Wind Plotting Module using Seaborn + Matplotlib Hybrid
Improved plotting functions with better aesthetics and production-ready performance
"""

import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend for production
import matplotlib.pyplot as plt
import matplotlib.image as mpimg
from matplotlib.backends.backend_pdf import PdfPages
import seaborn as sns
import pandas as pd
import numpy as np
import os
from helper.logger_setup import setup_logger

logger = setup_logger('wind_plots_enhanced', 'wind_plots_enhanced.log')

# Enhanced color palettes
WIND_PALETTE = ['#264653', '#2A9D8F', '#E9C46A', '#F4A261', '#E76F51', '#A8DADC']

def setup_enhanced_style():
    """Setup enhanced Seaborn + Matplotlib styling for wind plots"""
    sns.set_style("whitegrid")
    sns.set_context("notebook", font_scale=1.1)
    sns.set_palette(WIND_PALETTE)

    plt.rcParams.update({
        'figure.facecolor': 'white',
        'axes.facecolor': '#FAFAFA',
        'axes.edgecolor': '#CCCCCC',
        'axes.linewidth': 0.8,
        'axes.grid': True,
        'grid.color': '#E0E0E0',
        'grid.alpha': 0.3,
        'xtick.color': '#333333',
        'ytick.color': '#333333',
        'text.color': '#333333',
        'legend.framealpha': 0.9,
        'legend.shadow': True
    })


def plot_daily_generation_last_30_days(dataframe, plant_name, date_col="time"):
    """
    Enhanced wind generation plot for last 30 days using Seaborn + Matplotlib hybrid
    """
    try:
        logger.info(f"Starting enhanced plot_daily_generation_last_30_days for {plant_name}")
        setup_enhanced_style()

        # Convert date column to datetime
        df = dataframe.copy()
        df[date_col] = pd.to_datetime(df[date_col], errors='coerce')
        df = df.dropna(subset=[date_col])
        df[date_col] = df[date_col].dt.tz_localize(None)

        # Filter last 30 days data
        last_30_days = df[date_col].max() - pd.DateOffset(days=30)
        df = df[df[date_col] >= last_30_days]

        if df.empty:
            raise ValueError("No data available for the last 30 days.")

        # Create enhanced figure
        fig, ax = plt.subplots(figsize=(16, 8))

        # Prepare data for Seaborn
        generation_cols = df.columns[1:]
        # Create mapping for generic turbine names
        turbine_mapping = {col: f'Turbine {i+1}' for i, col in enumerate(generation_cols)}
        df_renamed = df.rename(columns=turbine_mapping)
        df_melted = df_renamed.melt(id_vars=[date_col], value_vars=list(turbine_mapping.values()),
                           var_name='Turbine', value_name='Generation')

        # Use Seaborn lineplot with enhanced styling
        sns.lineplot(data=df_melted, x=date_col, y='Generation', hue='Turbine',
                    ax=ax, linewidth=2.5, marker='o', markersize=4, alpha=0.8)

        # Calculate and add average line
        df['avg_generation'] = df[generation_cols].mean(axis=1)
        average_generation = df['avg_generation'].mean()

        ax.axhline(average_generation, color=WIND_PALETTE[4], linestyle='--',
                  linewidth=3, alpha=0.9,
                  label=f"Average: {average_generation:.2f} kWh")

        # Enhanced styling
        ax.set_title("Daily Generation Over Last 30 Days", fontsize=16, fontweight='bold',
                    pad=20, color='#2C3E50')
        ax.set_xlabel("Date", fontsize=12, fontweight='bold')
        ax.set_ylabel("Generation (kWh)", fontsize=12, fontweight='bold')
        ax.tick_params(axis='x', rotation=45)
        ax.legend(loc='upper right', framealpha=0.9, shadow=True)

        plt.tight_layout()

        output_image = f"{plant_name}_daily_generation_last_30_days.png"
        plt.savefig(output_image, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        logger.info(f"Enhanced wind generation plot saved as {output_image}")
        return output_image

    except Exception as e:
        logger.error(f"Error in enhanced plot_daily_generation_last_30_days: {e}")
        raise


def plot_wind_speed_last_30_days(dataframe, plant_name, date_col="time"):
    """
    Enhanced wind speed plot for last 30 days using Seaborn + Matplotlib hybrid
    """
    try:
        logger.info(f"Starting enhanced plot_wind_speed_last_30_days for {plant_name}")
        setup_enhanced_style()

        if date_col not in dataframe.columns:
            raise ValueError(f"'{date_col}' column not found in the dataframe.")

        # Select wind speed columns (all columns except the date column)
        wind_cols = [col for col in dataframe.columns if col != date_col]
        if not wind_cols:
            raise ValueError("No wind speed data found in the dataframe.")

        # Convert date column to datetime and drop invalid dates
        df = dataframe.copy()
        df[date_col] = pd.to_datetime(df[date_col], errors='coerce')
        df = df.dropna(subset=[date_col])
        df[date_col] = df[date_col].dt.tz_localize(None)

        # Filter last 30 days
        last_30_days = df[date_col].max() - pd.Timedelta(days=30)
        df = df[df[date_col] >= last_30_days]

        if df.empty:
            raise ValueError("No data available for the last 30 days.")

        # Create enhanced figure
        fig, ax = plt.subplots(figsize=(16, 8))

        # Use Seaborn lineplot for multiple wind speed lines
        # Create mapping for generic location names
        location_mapping = {col: f'Location {i+1}' for i, col in enumerate(wind_cols)}
        df_renamed = df.rename(columns=location_mapping)
        df_melted = df_renamed.melt(id_vars=[date_col], value_vars=list(location_mapping.values()),
                           var_name='Location', value_name='Wind Speed')

        sns.lineplot(data=df_melted, x=date_col, y='Wind Speed', hue='Location',
                    ax=ax, linewidth=2, marker='o', markersize=4, alpha=0.8)

        # Calculate and plot average line
        average_wind_speed = df[wind_cols].mean(axis=1).mean()
        ax.axhline(average_wind_speed, color=WIND_PALETTE[4], linestyle='--',
                  linewidth=3, alpha=0.9,
                  label=f"Overall Average: {average_wind_speed:.2f} m/s")

        # Enhanced styling
        ax.set_title("Wind Speed Over Last 30 Days", fontsize=16, fontweight='bold',
                    pad=20, color='#2C3E50')
        ax.set_xlabel("Date", fontsize=12, fontweight='bold')
        ax.set_ylabel("Wind Speed (m/s)", fontsize=12, fontweight='bold')
        ax.tick_params(axis='x', rotation=45)
        ax.legend(loc='upper right', framealpha=0.9, shadow=True)

        plt.tight_layout()

        output_image = f"{plant_name}_wind_speed_last_30_days.png"
        plt.savefig(output_image, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        logger.info(f"Enhanced wind speed plot saved as {output_image}")
        return output_image

    except Exception as e:
        logger.error(f"Error in enhanced plot_wind_speed_last_30_days: {e}")
        raise


def plot_plant_wise_generation_pdf(dataframe, plant_name, date_col="time"):
    """
    Enhanced wind generation yearly plot using Seaborn + Matplotlib hybrid
    """
    try:
        logger.info(f"Starting enhanced plot_plant_wise_generation_pdf for {plant_name}")
        setup_enhanced_style()

        output_pdf = f"{plant_name}_plant_wise_generation.pdf"
        generation_cols = dataframe.columns[1:]

        if date_col not in dataframe.columns or len(generation_cols) == 0:
            raise ValueError("Provided column names do not exist in the dataframe.")

        df = dataframe.copy()
        df[date_col] = pd.to_datetime(df[date_col], errors='coerce')
        df = df.dropna(subset=[date_col])
        df[date_col] = df[date_col].dt.tz_localize(None)

        num_plots = len(generation_cols)
        cols = 2
        rows = (num_plots + cols - 1) // cols

        with PdfPages(output_pdf) as pdf:
            fig, axes = plt.subplots(rows, cols, figsize=(16, 6 * rows))
            fig.suptitle("Plant-wise Generation Report",
                        fontsize=18, fontweight='bold', y=0.98, color='#2C3E50')

            if rows == 1:
                axes = [axes] if cols == 1 else axes

            for i, col in enumerate(generation_cols):
                row_idx, col_idx = divmod(i, cols)
                ax = axes[row_idx][col_idx] if rows > 1 else axes[col_idx]

                df_monthly = df.resample('ME', on=date_col).sum()[col].reset_index()
                df_monthly['month_year'] = df_monthly[date_col].dt.strftime('%b %y')
                overall_avg = df_monthly[col].mean()

                # Use Seaborn barplot with enhanced styling
                bars = sns.barplot(data=df_monthly, x='month_year', y=col, ax=ax,
                                  palette=sns.color_palette("viridis", len(df_monthly)),
                                  alpha=0.8, legend=False)

                # Enhanced average line
                ax.axhline(y=overall_avg, color=WIND_PALETTE[4], linestyle='--',
                          linewidth=2.5, alpha=0.9, label=f'Avg: {overall_avg:.2f} kWh')

                # Enhanced value labels
                for bar in bars.patches:
                    height = bar.get_height()
                    if height > 0:
                        ax.text(bar.get_x() + bar.get_width()/2., height + overall_avg * 0.02,
                               f'{height:.0f}', ha='center', va='bottom',
                               fontsize=8, fontweight='bold', color='#2C3E50')

                # Enhanced styling for each subplot
                ax.set_title("Turbine Generation", fontsize=14, fontweight='bold', color='#2C3E50')
                ax.set_xlabel("Month-Year", fontsize=11, fontweight='bold')
                ax.set_ylabel("Total Energy Generated (kWh)", fontsize=11, fontweight='bold')
                ax.tick_params(axis='x', rotation=45, labelsize=10)
                ax.legend(framealpha=0.9, shadow=True)

            # Hide unused axes
            if num_plots < rows * cols:
                for j in range(num_plots, rows * cols):
                    row_idx, col_idx = divmod(j, cols)
                    ax = axes[row_idx][col_idx] if rows > 1 else axes[col_idx]
                    ax.axis('off')

            plt.tight_layout(rect=[0, 0, 1, 0.95])
            pdf.savefig(fig, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close(fig)

        logger.info(f"Enhanced plant-wise generation plots saved to {output_pdf}")
        return output_pdf

    except Exception as e:
        logger.error(f"Error in enhanced plot_plant_wise_generation_pdf: {e}")
        raise


def plot_wind_speed_last_12_months(dataframe, plant_name, date_col="time"):
    """
    Enhanced wind speed yearly plot using Seaborn + Matplotlib hybrid
    """
    try:
        logger.info(f"Starting enhanced plot_wind_speed_last_12_months for {plant_name}")
        setup_enhanced_style()

        output_pdf = f"{plant_name}_wind_speed_last_12_months.pdf"
        wind_cols = dataframe.columns[1:]

        if date_col not in dataframe.columns or len(wind_cols) == 0:
            raise ValueError("Provided column names do not exist in the dataframe.")

        df = dataframe.copy()
        df[date_col] = pd.to_datetime(df[date_col], errors='coerce')
        df = df.dropna(subset=[date_col])
        df[date_col] = df[date_col].dt.tz_localize(None)

        last_12_months = df[date_col].max() - pd.DateOffset(months=12)
        df = df[df[date_col] >= last_12_months]

        if df.empty:
            raise ValueError("No data available for the last 12 months.")

        num_plots = len(wind_cols)
        cols = 2
        rows = (num_plots + cols - 1) // cols

        with PdfPages(output_pdf) as pdf:
            fig, axes = plt.subplots(rows, cols, figsize=(16, 6 * rows))
            fig.suptitle("Wind Speed Report",
                        fontsize=18, fontweight='bold', y=0.98, color='#2C3E50')

            if rows == 1:
                axes = [axes] if cols == 1 else axes

            for i, col in enumerate(wind_cols):
                row_idx, col_idx = divmod(i, cols)
                ax = axes[row_idx][col_idx] if rows > 1 else axes[col_idx]

                df_monthly = df.resample('ME', on=date_col).mean()[col].reset_index()
                df_monthly['month_year'] = df_monthly[date_col].dt.strftime('%b %y')
                overall_avg = df_monthly[col].mean()

                # Use Seaborn lineplot with enhanced styling
                sns.lineplot(data=df_monthly, x='month_year', y=col, ax=ax,
                           color=WIND_PALETTE[1], linewidth=3, marker='o', markersize=8, alpha=0.8)

                # Enhanced average line
                ax.axhline(y=overall_avg, color=WIND_PALETTE[4], linestyle='--',
                          linewidth=2.5, alpha=0.9, label=f'Avg: {overall_avg:.2f} m/s')

                # Add confidence band
                std_dev = df_monthly[col].std()
                ax.fill_between(range(len(df_monthly)),
                               overall_avg - std_dev, overall_avg + std_dev,
                               color=WIND_PALETTE[4], alpha=0.1)

                # Enhanced styling for each subplot
                ax.set_title("Wind Speed Analysis", fontsize=14, fontweight='bold', color='#2C3E50')
                ax.set_xlabel("Month-Year", fontsize=11, fontweight='bold')
                ax.set_ylabel("Average Wind Speed (m/s)", fontsize=11, fontweight='bold')
                ax.tick_params(axis='x', rotation=45, labelsize=10)
                ax.legend(framealpha=0.9, shadow=True)

            # Hide unused axes
            if num_plots < rows * cols:
                for j in range(num_plots, rows * cols):
                    row_idx, col_idx = divmod(j, cols)
                    ax = axes[row_idx][col_idx] if rows > 1 else axes[col_idx]
                    ax.axis('off')

            plt.tight_layout(rect=[0, 0, 1, 0.95])
            pdf.savefig(fig, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close(fig)

        logger.info(f"Enhanced wind speed plots saved to {output_pdf}")
        return output_pdf

    except Exception as e:
        logger.error(f"Error in enhanced plot_wind_speed_last_12_months: {e}")
        raise


def generate_wind_plot(wind_plot, generation_plot, pdf_filename):
    """
    Enhanced wind plot PDF generation using Seaborn + Matplotlib hybrid
    """
    logger.info("Starting enhanced wind plot PDF generation...")
    setup_enhanced_style()

    try:
        with PdfPages(pdf_filename) as pdf:
            fig, axes = plt.subplots(1, 2, figsize=(16, 8))
            fig.suptitle("Wind Analysis Report", fontsize=18, fontweight='bold',
                        y=0.95, color='#2C3E50')

            # Load and display wind plot with enhanced frame
            try:
                logger.info(f"Loading enhanced image: {wind_plot}")
                wind_img = mpimg.imread(wind_plot)
                axes[0].imshow(wind_img)
                axes[0].axis("off")
                axes[0].set_title("Wind Speed Analysis", fontsize=14, fontweight='bold', color='#2C3E50')
            except FileNotFoundError:
                logger.error(f"File not found: {wind_plot}")
                axes[0].text(0.5, 0.5, "Wind Plot Not Found",
                           ha='center', va='center', fontsize=14,
                           color='red', fontweight='bold')
            except Exception as e:
                logger.error(f"Error loading wind plot {wind_plot}: {e}")
                axes[0].text(0.5, 0.5, f"Error: {e}",
                           ha='center', va='center', fontsize=12, color='red')

            # Load and display generation plot with enhanced frame
            try:
                logger.info(f"Loading enhanced image: {generation_plot}")
                gen_img = mpimg.imread(generation_plot)
                axes[1].imshow(gen_img)
                axes[1].axis("off")
                axes[1].set_title("Generation Analysis", fontsize=14, fontweight='bold', color='#2C3E50')
            except FileNotFoundError:
                logger.error(f"File not found: {generation_plot}")
                axes[1].text(0.5, 0.5, "Generation Plot Not Found",
                           ha='center', va='center', fontsize=14,
                           color='red', fontweight='bold')
            except Exception as e:
                logger.error(f"Error loading generation plot {generation_plot}: {e}")
                axes[1].text(0.5, 0.5, f"Error: {e}",
                           ha='center', va='center', fontsize=12, color='red')

            plt.tight_layout(rect=[0, 0, 1, 0.93])
            pdf.savefig(fig, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close(fig)

        logger.info(f"Enhanced wind plot PDF saved to {pdf_filename}")
        return pdf_filename

    except Exception as e:
        logger.error(f"Error in enhanced generate_wind_plot: {e}")
        raise
