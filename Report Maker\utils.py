import pandas as pd
import time
import csv
import logging
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from IntegrationUtilities import PrescintoIntegrationUtilities
from reportlab.lib.pagesizes import A4
from PyPDF2 import PdfMerger
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle

# Setup logger
logging = logging.getLogger('dgr_utilities')

# Initialize Prescinto Integration
m = PrescintoIntegrationUtilities(server='IN', token='1d8f9dc5-8f50-4ffc-8b90-1b40b866283c')

def fetch_data(plant_name, params, category, start_date, end_date, retries=3):
    """
    Fetches data from Prescinto API with error handling and retries.
    """
    for attempt in range(retries):
        try:
            data = m.fetchDataV2(plant_name, category, params, None, start_date, end_date, granularity='15m')
            if isinstance(data, str):  # API returned an error message as a string
                logging.error(f"Error fetching {params} (Attempt {attempt+1}): {data}")
                continue
            df = pd.DataFrame(data)
            if not df.empty:
                return df
        except Exception as e:
            logging.error(f"Exception fetching {params} (Attempt {attempt+1}): {e}")
        time.sleep(5)  # Wait before retrying
    return pd.DataFrame()

def generate_solar_dgr_report(poa, pr, total_generation, start_date, customer_name, project):
    output_file = f"Solar_DGR_Report_{start_date}.csv"
    try:
        with open(output_file, mode="w", newline="") as file:
            writer = csv.writer(file)
            writer.writerow(["Date", "Customer Name", "Project", "POA(Kwh/m2)", "PR%", "Daily Generation(kWh)"])
            writer.writerow([start_date, customer_name, project, poa, pr, total_generation])
        return output_file
    except Exception as e:
        logging.error(f"Error generating CSV report: {e}")
        return None

def generate_dgr_report(wind_speed_data, daily_generation, start_date, customer_name, project, ma_percent):
    output_file = f"Wind_DGR_Report_{start_date}.csv"
    try:
        merged_data = pd.merge(wind_speed_data, daily_generation, on="time", how="left").fillna(0)
        loc_nos = [col.replace(".Wind-Speed", "") for col in wind_speed_data.columns if col != "time"]

        processed_data = [
            {
                "Date": start_date,
                "Customer Name": customer_name,
                "Project": project,
                "Loc No": loc_no,
                "Avg Wind Speed": merged_data[f"{loc_no}.Wind-Speed"].mean(),
                "Total Daily Generation": merged_data.get(f"{loc_no}.Generation today", pd.Series(0)).sum(),
                "MA%": ma_percent
            }
            for loc_no in loc_nos
        ]

        df = pd.DataFrame(processed_data)
        df.to_csv(output_file, index=False)
        return output_file
    except Exception as e:
        logging.error(f"Error generating CSV report: {e}")
        return None

def generate_solar_dgr_pdf(date, customer_name, project, poa, pr, daily_generation):
    output_file = f"Solar_DGR_Summary_{date}.pdf"
    try:
        doc = SimpleDocTemplate(output_file, pagesize=A4)
        elements = []
        styles = getSampleStyleSheet()

        title = Paragraph("<b><font size=14>Solar Daily Generation Report</font></b>", styles["Title"])
        elements.append(title)
        elements.append(Spacer(1, 10))

        data = [
            ["Date:", date],
            ["Customer Name:", customer_name],
            ["Project:", project],
            ["POA (Kwh/m2):", poa],
            ["PR%:", pr],
            ["Daily Generation (MU):", daily_generation]
        ]

        table = Table(data, colWidths=[150, 250])
        table.setStyle(TableStyle([('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey), ('TEXTCOLOR', (0, 0), (-1, 0), colors.black)]))

        elements.append(table)
        doc.build(elements)
        return output_file
    except Exception as e:
        logging.error(f"Error generating PDF report: {e}")
        return None

def csv_to_pdf(csv_filename):
    if csv_filename is None:
        logging.error("CSV to PDF conversion skipped due to missing CSV file.")
        return None

    pdf_filename = csv_filename.replace(".csv", ".pdf")
    try:
        df = pd.read_csv(csv_filename)
        data = [df.columns.tolist()] + df.values.tolist()

        doc = SimpleDocTemplate(pdf_filename, pagesize=A4)
        elements = []

        table = Table(data)
        table.setStyle(TableStyle([('BACKGROUND', (0, 0), (-1, 0), colors.grey), ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke)]))

        elements.append(table)
        doc.build(elements)
        return pdf_filename
    except Exception as e:
        logging.error(f"Error converting CSV to PDF: {e}")
        return None

def merge_pdfs(pdf1, pdf2, output_filename):
    if not pdf1 or not pdf2:
        logging.error("Skipping PDF merging due to missing files.")
        return None

    try:
        merger = PdfMerger()
        merger.append(pdf1)
        merger.append(pdf2)
        merger.write(output_filename)
        merger.close()
        return output_filename
    except Exception as e:
        logging.error(f"Error merging PDFs: {e}")
        return None


def generate_dgr_pdf(date, customer_name, project, avg_wind_speed, total_generation, ma_percent):
    output_file = f"Wind_DGR_Summary_{date}.pdf"
    try:
        doc = SimpleDocTemplate(output_file, pagesize=A4)
        elements = []
        styles = getSampleStyleSheet()

        title = Paragraph("<b><font size=14>Wind Daily Generation Report</font></b>", styles["Title"])
        elements.append(title)
        elements.append(Spacer(1, 10))

        data = [
            ["Date:", date],
            ["Customer Name:", customer_name],
            ["Project:", project],
            ["Avg Wind Speed (m/s):", avg_wind_speed],
            ["Total Daily Generation (MU):", total_generation],
            ["MA%:", ma_percent]
        ]

        table = Table(data, colWidths=[150, 250])
        table.setStyle(TableStyle([('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey), ('TEXTCOLOR', (0, 0), (-1, 0), colors.black)]))

        elements.append(table)
        doc.build(elements)
        return output_file
    except Exception as e:
        logging.error(f"Error generating Wind DGR PDF: {e}")
        return None